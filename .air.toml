root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
args_bin = []
# Just plain old shell command. You could use `make` as well.
cmd = "bash ./scripts/swagger.sh && go build -o ./build/bin/server ./cmd/main.go"
# Binary file yields from `cmd`.
bin = "./build/bin/server"
delay = 1000
exclude_dir = ["assets", "tmp", "vendor", "testdata", "docs"]
exclude_file = []
exclude_regex = ["_test.go"]
exclude_unchanged = false
follow_symlink = false
full_bin = ""
include_dir = []
include_ext = ["go", "tpl", "tmpl", "html"]
kill_delay = "0s"
log = "build-errors.log"
send_interrupt = false
stop_on_error = true

[color]
app = ""
build = "yellow"
main = "magenta"
runner = "green"
watcher = "cyan"

[log]
time = false

[misc]
clean_on_exit = false

[screen]
clear_on_rebuild = false
