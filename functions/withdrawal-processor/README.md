# Withdrawal Processor Cloud Function

This Cloud Function processes withdrawal payouts and refreshes balance views using an event-driven architecture with Google Cloud Pub/Sub.

## Architecture

### Previous Implementation (HTTP)
- Cloud Scheduler → HTTP POST → Cloud Function
- Direct HTTP requests with JSON payloads
- Synchronous processing

### Current Implementation (Pub/Sub)
- Cloud Scheduler → Pub/Sub Topic → Cloud Function
- Event-driven architecture with CloudEvents
- Asynchronous processing with built-in retry mechanisms

## Pub/Sub Topic

**Topic Name:** `withdrawal-processor-events`

The function subscribes to this topic and processes messages containing action payloads.

## Message Format

### Pub/Sub Message Structure
When Cloud Scheduler publishes to Pub/Sub, the message structure is:

```json
{
  "message": {
    "data": "eyJhY3Rpb24iOiJ1cGRhdGVfcGF5b3V0cyJ9", // base64 encoded payload
    "messageId": "15555405354119659",
    "publishTime": "2025-07-16T00:19:12.74Z"
  },
  "subscription": "projects/PROJECT_ID/subscriptions/SUBSCRIPTION_NAME"
}
```

### Decoded Payload
The `data` field contains base64-encoded JSON payloads:

```json
{"action": "update_payouts"}
```

or

```json
{"action": "refresh_balance_view"}
```

### Processing Flow
1. Function receives CloudEvent with Pub/Sub message structure
2. Extracts `message.data` field (base64 encoded)
3. Decodes base64 to get JSON payload
4. Parses JSON to extract action

## Supported Actions

### 1. `update_payouts`
- **Schedule:** Every hour (0 * * * *)
- **Purpose:** Updates payout statuses from pending to available after the 3-day holding period
- **Database Operations:**
  - Updates `invoice_payouts` table
  - Changes status from `pending` to `available`
  - Only processes invoices completed more than 3 days ago

### 2. `refresh_balance_view`
- **Schedule:** Every 15 minutes (*/15 * * * *)
- **Purpose:** Refreshes the materialized view for withdrawal balance calculations
- **Database Operations:**
  - Refreshes `withdrawal_balance_view` materialized view
  - Provides up-to-date balance information for withdrawal eligibility

## Deployment

The function is deployed using GitHub Actions workflow: `.github/workflows/deploy.withdrawal-function.yml`

### Deployment Steps:
1. **Create Pub/Sub Topic:** Creates `withdrawal-processor-events` topic
2. **Deploy Function:** Deploys with Pub/Sub trigger instead of HTTP
3. **Setup Scheduler Jobs:** Creates Cloud Scheduler jobs that publish to Pub/Sub

### Environment Variables:
- `DATABASE_URL`: PostgreSQL connection string (from GCP Secret Manager)

### Function Configuration:
- **Runtime:** Go 1.23
- **Memory:** 256MB
- **Timeout:** 540s (9 minutes)
- **Max Instances:** 10
- **Min Instances:** 0
- **VPC Connector:** cloudsql-connector (for database access)

## Benefits of Pub/Sub Architecture

1. **Scalability:** Better handling of concurrent events
2. **Reliability:** Built-in retry mechanisms and dead letter queues
3. **Decoupling:** Scheduler and function are loosely coupled
4. **Monitoring:** Better observability with Pub/Sub metrics
5. **Future-proofing:** Easy to add more publishers or subscribers

## Testing

### Local Testing
```bash
cd functions/withdrawal-processor
go test -v
```

### Production Testing
The deployment workflow automatically publishes a test message to verify the function works:

```bash
gcloud pubsub topics publish withdrawal-processor-events \
  --message='{"action":"update_payouts"}' \
  --project=YOUR_PROJECT_ID
```

## Monitoring

### Cloud Logging
Function logs are available in Google Cloud Logging with structured logging for better filtering and alerting.

### Pub/Sub Metrics
Monitor message processing through Cloud Monitoring:
- Message publish rate
- Message delivery rate
- Function execution metrics
- Error rates

### Cloud Scheduler
Monitor scheduled job execution and success rates in the Cloud Scheduler console.

## Error Handling

The function returns appropriate errors for:
- Invalid message format
- Unsupported actions
- Database connection failures
- SQL execution errors

Failed messages are automatically retried by Pub/Sub according to the subscription's retry policy.
