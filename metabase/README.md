# 🚀 Izy Mercado - Metabase Analytics Platform

This is a **production-ready, streamlined deployment system** for Metabase analytics platform with comprehensive business intelligence pre-configured for Izy Mercado.

## 📋 **What This System Provides**

### **Complete Business Analytics**
- ✅ **Produtos Mais Vendidos** - Top selling products with profitability metrics
- ✅ **Receita por Período** - Revenue trends over time
- ✅ **Performance por Empresa** - Company performance analysis
- ✅ **Aná<PERSON>e Geográfica** - Geographic distribution of sales
- ✅ **Métricas de Comissão** - Platform commission analytics

### **Infrastructure**
- ✅ **Regular GCP VM** (99.95% uptime, non-preemptible)
- ✅ **Persistent Storage** (20GB, survives VM recreation)
- ✅ **Static IP** (DNS stability)
- ✅ **Auto-restart Services** (survives reboots)
- ✅ **SSL-ready Nginx** (reverse proxy configured)

## 🎯 **Two-Step Deployment**

### **Step 1: Deploy Infrastructure**
```bash
cd metabase
./deploy.sh
```

### **Step 2: Setup Analytics**
```bash
./complete-setup.sh
```

**That's it!** Your complete Metabase analytics platform is ready.

## 📁 **Essential Files**

| File | Purpose |
|------|---------|
| `deploy.sh` | **Main deployment script** - Creates VM, configures infrastructure |
| `complete-setup.sh` | **Analytics setup script** - Creates all questions, dashboards, visualizations |
| `vm-startup.sh` | **VM initialization** - Installs Docker, configures services |
| `docker-compose.yml` | **Container configuration** - Metabase + PostgreSQL |
| `.env.example` | **Configuration template** - Copy to `.env` and customize |
| `verify-deployment.sh` | **Verification script** - Check deployment status |
| `troubleshoot-disk.sh` | **Troubleshooting** - Diagnose disk/service issues |

## ⚙️ **Configuration**

### **1. Environment Setup**
```bash
cp .env.example .env
nano .env
```

### **2. Required Settings**
```bash
# GCP Configuration
PROJECT_ID=your-gcp-project-id
VM_NAME=metabase
ZONE=us-central1-a
MACHINE_TYPE=e2-medium

# Domain Configuration
METABASE_DOMAIN=data.yourdomain.com

# Production Database
PROD_DB_HOST=your-db-host
PROD_DB_PORT=5432
PROD_DB_NAME=your-db-name
PROD_DB_USER=metabase_readonly
PROD_DB_PASSWORD=your-secure-password
```

## 🚀 **Deployment Process**

### **Prerequisites**
1. **GCP Authentication**
   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   ```

2. **Persistent Disk** (first time only)
   ```bash
   gcloud compute disks create metabase-data \
     --zone=us-central1-a \
     --size=20GB \
     --type=pd-standard
   ```

### **Full Deployment**
```bash
# 1. Deploy infrastructure
./deploy.sh

# 2. Setup analytics (follow prompts)
./complete-setup.sh
```

### **What complete-setup.sh Does**
1. ✅ Waits for Metabase to be ready
2. ✅ Guides you through admin account creation
3. ✅ Connects to production database
4. ✅ Creates Analytics collection
5. ✅ Creates all 5 business analytics questions
6. ✅ Creates main dashboard
7. ✅ Adds all questions to dashboard with proper layout
8. ✅ Provides final access URLs

## 🌐 **Access Your Analytics**

After deployment:
- **🔗 Main URL**: https://data.izymercado.com.br
- **👤 Login**: <EMAIL>
- **🔑 Password**: IzyMetabase2024!

## 📊 **Business Analytics Included**

### **1. Produtos Mais Vendidos**
- Top 20 products by quantity sold
- Revenue and commission metrics
- Brand and product analysis

### **2. Receita por Período**
- Monthly revenue trends (12 months)
- Order volume analysis
- Platform vs company revenue split

### **3. Performance por Empresa**
- Top 20 companies by revenue
- Geographic distribution
- Average order value analysis

### **4. Análise Geográfica**
- Revenue by state and city
- Company distribution mapping
- Regional performance metrics

### **5. Métricas de Comissão**
- Weekly commission trends (8 weeks)
- Commission rate analysis
- Platform revenue tracking

## 🔧 **Management Commands**

### **VM Management**
```bash
# Start/Stop VM
gcloud compute instances start metabase --zone=us-central1-a
gcloud compute instances stop metabase --zone=us-central1-a

# SSH to VM
gcloud compute ssh metabase --zone=us-central1-a
```

### **Service Management**
```bash
# Check status
./verify-deployment.sh

# Troubleshoot issues
gcloud compute ssh metabase --zone=us-central1-a --command='sudo /opt/metabase/troubleshoot-disk.sh'

# View logs
gcloud compute ssh metabase --zone=us-central1-a --command='cd /opt/metabase && sudo docker-compose logs -f'
```

## 🛡️ **Security Features**

- ✅ **UFW Firewall** configured
- ✅ **Security Headers** in Nginx
- ✅ **Read-only Database** access
- ✅ **SSL-ready** configuration
- ✅ **Private networking** for database

## 💰 **Cost Optimization**

### **Monthly Costs** (Estimated)
- **VM Instance**: ~$25/month (e2-medium)
- **Persistent Disk**: ~$4/month (20GB)
- **Static IP**: ~$3/month
- **Total**: ~$32/month

### **Cost vs Reliability**
- **99.95% uptime** guarantee
- **Data persistence** across VM operations
- **No preemption** interruptions
- **Predictable costs**

## 🔄 **Backup & Recovery**

### **Automatic Snapshots**
```bash
# Create snapshot
gcloud compute disks snapshot metabase-data \
  --zone=us-central1-a \
  --snapshot-names=metabase-backup-$(date +%Y%m%d)
```

### **Fresh Deployment**
If you need to start completely fresh:
1. Delete VM: `gcloud compute instances delete metabase --zone=us-central1-a`
2. Run: `./deploy.sh`
3. Run: `./complete-setup.sh`

## 📞 **Support**

### **Common Issues**
- **VM won't start**: Check quotas and billing
- **Services not responding**: Run `./verify-deployment.sh`
- **Database connection fails**: Check `.env` credentials
- **Disk mounting issues**: Run `troubleshoot-disk.sh`

### **Logs to Check**
```bash
# Deployment logs
cat deploy.log
cat complete-setup.log

# Service logs
gcloud compute ssh metabase --zone=us-central1-a --command='sudo journalctl -u metabase -f'
```

## ✅ **Success Checklist**

- [ ] GCP authentication configured
- [ ] Persistent disk created
- [ ] `.env` file configured with correct values
- [ ] `./deploy.sh` completed successfully
- [ ] `./complete-setup.sh` completed successfully
- [ ] Can access https://data.izymercado.com.br
- [ ] All 5 analytics questions visible in dashboard
- [ ] Production database connected and syncing

## 🎉 **You're Done!**

Your **production-ready Metabase analytics platform** is now live with:
- 🛡️ **Reliable infrastructure** (regular instances)
- 📊 **Complete business analytics** (5 key metrics)
- 🌐 **Domain access** (SSL-ready)
- 💾 **Data persistence** (survives restarts)
- 🔄 **Auto-recovery** (services restart automatically)

**Access your analytics at: https://data.izymercado.com.br** 🚀
