# 🚀 Metabase Deployment Guide

This guide explains how to deploy Metabase using the new regular (non-preemptible) GCP VM instances with persistent storage.

## 📋 Prerequisites

### 1. **Google Cloud Setup**
```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Authenticate
gcloud auth login

# Set project
gcloud config set project YOUR_PROJECT_ID
```

### 2. **Required Files**
Ensure you're in the `/metabase` directory with these files:
- `deploy.sh` - Main deployment script
- `vm-startup.sh` - VM initialization script  
- `docker-compose.yml` - Container configuration
- `.env` - Environment configuration (copy from `.env.example`)

## 🔧 Configuration

### 1. **Create Environment File**
```bash
cd metabase
cp .env.example .env
```

### 2. **Edit Configuration**
```bash
nano .env
```

Required settings:
```bash
# GCP Configuration
PROJECT_ID=your-gcp-project-id
VM_NAME=metabase
ZONE=us-central1-a
MACHINE_TYPE=e2-medium

# Domain Configuration  
METABASE_DOMAIN=data.yourdomain.com

# Production Database Configuration
PROD_DB_HOST=your-db-host
PROD_DB_PORT=5432
PROD_DB_NAME=your-db-name
PROD_DB_USER=metabase_readonly
PROD_DB_PASSWORD=your-secure-password
```

## 🚀 Deployment Process

### 1. **Create Persistent Disk** (First Time Only)
```bash
# Create the persistent disk for data storage
gcloud compute disks create metabase-data \
  --zone=us-central1-a \
  --size=20GB \
  --type=pd-standard \
  --description="Persistent disk for Metabase data and PostgreSQL"
```

### 2. **Deploy VM**
```bash
cd metabase
./deploy.sh
```

The script will:
- ✅ Validate configuration and authentication
- ✅ Check for existing persistent disk (required)
- ✅ Reserve static IP address
- ✅ Create regular (non-preemptible) VM instance
- ✅ Attach persistent disk for data preservation
- ✅ Configure firewall rules
- ✅ Set up automatic service startup

### 3. **Monitor Deployment**
```bash
# Check startup progress
gcloud compute ssh metabase --zone=us-central1-a --command='sudo tail -f /var/log/metabase-startup.log'

# Verify deployment
./verify-deployment.sh
```

## 🌐 DNS Configuration

### 1. **Get Static IP**
The deployment script will output the static IP address. Update your DNS:

**Cloudflare Example:**
- **Type**: A
- **Name**: data (for data.yourdomain.com)
- **Content**: [STATIC_IP_FROM_DEPLOYMENT]
- **TTL**: Auto

### 2. **Verify DNS**
```bash
nslookup data.yourdomain.com
```

## 🔍 Verification & Troubleshooting

### 1. **Check Services**
```bash
# SSH to VM
gcloud compute ssh metabase --zone=us-central1-a

# Check systemd service
sudo systemctl status metabase

# Check Docker containers
cd /opt/metabase && sudo docker-compose ps

# View logs
sudo docker-compose logs -f
```

### 2. **Troubleshoot Issues**
```bash
# Run troubleshooting script
sudo /opt/metabase/troubleshoot-disk.sh

# Check disk mounting
df -h /opt/metabase-data

# Restart services if needed
sudo systemctl restart metabase
```

## 🔄 VM Management

### **Start/Stop VM**
```bash
# Stop VM (preserves data on persistent disk)
gcloud compute instances stop metabase --zone=us-central1-a

# Start VM (automatically restores services)
gcloud compute instances start metabase --zone=us-central1-a
```

### **Update Configuration**
```bash
# Update .env and redeploy
./deploy.sh
# Choose "N" to update existing VM metadata
```

### **Complete Rebuild**
```bash
# Redeploy (preserves persistent disk data)
./deploy.sh
# Choose "Y" to delete and recreate VM
```

## 📊 Service Architecture

### **VM Configuration**
- **Type**: Regular (non-preemptible) instance
- **Machine**: e2-medium (1 vCPU, 4GB RAM)
- **OS**: Debian 12
- **Storage**: 20GB boot disk + 20GB persistent data disk

### **Container Stack**
- **Metabase**: Latest version with PostgreSQL backend
- **PostgreSQL**: 15-alpine for Metabase metadata
- **Nginx**: Reverse proxy with SSL-ready configuration
- **Docker**: Auto-restart policies configured

### **Data Persistence**
- **Persistent Disk**: `/opt/metabase-data` (survives VM recreation)
- **PostgreSQL Data**: `/opt/metabase-data/postgres_data`
- **Metabase Data**: `/opt/metabase-data/metabase_data`
- **Configuration**: Preserved in persistent storage

### **Network Configuration**
- **Static IP**: Reserved for DNS stability
- **Firewall**: HTTP (80), HTTPS (443), Metabase (3000)
- **Domain**: Nginx reverse proxy with SSL support

## 🛡️ Security Features

### **VM Security**
- Regular instances (no preemption vulnerabilities)
- Automatic security updates
- UFW firewall configured
- SSH key-based access only

### **Application Security**
- Nginx reverse proxy with security headers
- SSL/TLS ready configuration
- Database read-only access for analytics
- Container isolation

## 💰 Cost Optimization

### **Monthly Costs** (Estimated)
- **VM Instance**: ~$25/month (e2-medium)
- **Persistent Disk**: ~$4/month (20GB pd-standard)
- **Static IP**: ~$3/month
- **Total**: ~$32/month

### **Cost vs Reliability**
- **Previous (Spot)**: ~$19/month, 60-90% uptime
- **New (Regular)**: ~$32/month, 99.95% uptime
- **Improvement**: +$13/month for guaranteed reliability

## 🔄 Backup & Recovery

### **Automatic Backups**
```bash
# Create snapshot of persistent disk
gcloud compute disks snapshot metabase-data \
  --zone=us-central1-a \
  --snapshot-names=metabase-backup-$(date +%Y%m%d)
```

### **Restore from Backup**
```bash
# Create new disk from snapshot
gcloud compute disks create metabase-data-restored \
  --zone=us-central1-a \
  --source-snapshot=metabase-backup-YYYYMMDD

# Update deployment to use restored disk
```

## 📞 Support

### **Common Issues**
1. **Disk mounting fails**: Run `troubleshoot-disk.sh`
2. **Services won't start**: Check `systemctl status metabase`
3. **Can't access Metabase**: Verify firewall and DNS
4. **Data not persisting**: Check persistent disk attachment

### **Getting Help**
- Check deployment logs: `deploy.log`
- Run verification: `./verify-deployment.sh`
- SSH and troubleshoot: `sudo /opt/metabase/troubleshoot-disk.sh`

## ✅ Success Checklist

- [ ] Persistent disk created and attached
- [ ] VM deployed with regular instance type
- [ ] Static IP reserved and assigned
- [ ] DNS A record updated
- [ ] Metabase accessible via domain
- [ ] Services auto-start on boot
- [ ] Data persists across VM restarts
- [ ] Backup strategy implemented
