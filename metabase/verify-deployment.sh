#!/bin/bash

# Metabase Deployment Verification Script
# This script verifies that the regular (non-preemptible) instance is properly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔍 Verifying Metabase Deployment...${NC}"

# Load environment variables
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ Error: .env file not found!${NC}"
    exit 1
fi

source .env

# Check if VM exists
echo -e "${BLUE}📋 Checking VM status...${NC}"
if gcloud compute instances describe $VM_NAME --zone=$ZONE &>/dev/null; then
    VM_STATUS=$(gcloud compute instances describe $VM_NAME --zone=$ZONE --format='get(status)')
    PROVISIONING_MODEL=$(gcloud compute instances describe $VM_NAME --zone=$ZONE --format='get(scheduling.provisioningModel)')
    MAINTENANCE_POLICY=$(gcloud compute instances describe $VM_NAME --zone=$ZONE --format='get(scheduling.onHostMaintenance)')
    VM_IP=$(gcloud compute instances describe $VM_NAME --zone=$ZONE --format='get(networkInterfaces[0].accessConfigs[0].natIP)')
    
    echo -e "${GREEN}✅ VM exists: $VM_NAME${NC}"
    echo -e "${BLUE}   Status: $VM_STATUS${NC}"
    echo -e "${BLUE}   Provisioning Model: $PROVISIONING_MODEL${NC}"
    echo -e "${BLUE}   Maintenance Policy: $MAINTENANCE_POLICY${NC}"
    echo -e "${BLUE}   External IP: $VM_IP${NC}"
    
    if [ "$PROVISIONING_MODEL" = "STANDARD" ]; then
        echo -e "${GREEN}✅ Instance is regular (non-preemptible)${NC}"
    else
        echo -e "${RED}❌ Instance is still using spot/preemptible model${NC}"
    fi
    
    if [ "$MAINTENANCE_POLICY" = "MIGRATE" ]; then
        echo -e "${GREEN}✅ Maintenance policy allows live migration${NC}"
    else
        echo -e "${YELLOW}⚠️  Maintenance policy: $MAINTENANCE_POLICY${NC}"
    fi
else
    echo -e "${RED}❌ VM not found: $VM_NAME${NC}"
    exit 1
fi

# Check static IP
echo -e "${BLUE}📋 Checking static IP...${NC}"
STATIC_IP_NAME="metabase-static-ip"
REGION=$(echo $ZONE | sed 's/-[a-z]$//')

if gcloud compute addresses describe $STATIC_IP_NAME --region=$REGION &>/dev/null; then
    STATIC_IP=$(gcloud compute addresses describe $STATIC_IP_NAME --region=$REGION --format='get(address)')
    STATIC_IP_STATUS=$(gcloud compute addresses describe $STATIC_IP_NAME --region=$REGION --format='get(status)')
    
    echo -e "${GREEN}✅ Static IP reserved: $STATIC_IP${NC}"
    echo -e "${BLUE}   Status: $STATIC_IP_STATUS${NC}"
    
    if [ "$VM_IP" = "$STATIC_IP" ]; then
        echo -e "${GREEN}✅ VM is using the reserved static IP${NC}"
    else
        echo -e "${YELLOW}⚠️  VM IP ($VM_IP) differs from static IP ($STATIC_IP)${NC}"
    fi
else
    echo -e "${RED}❌ Static IP not found: $STATIC_IP_NAME${NC}"
fi

# Check persistent disk
echo -e "${BLUE}📋 Checking persistent disk...${NC}"
if gcloud compute disks describe metabase-data --zone=$ZONE &>/dev/null; then
    DISK_SIZE=$(gcloud compute disks describe metabase-data --zone=$ZONE --format='get(sizeGb)')
    DISK_TYPE=$(gcloud compute disks describe metabase-data --zone=$ZONE --format='get(type)' | sed 's/.*\///')
    DISK_STATUS=$(gcloud compute disks describe metabase-data --zone=$ZONE --format='get(status)')
    
    echo -e "${GREEN}✅ Persistent disk exists: metabase-data${NC}"
    echo -e "${BLUE}   Size: ${DISK_SIZE}GB${NC}"
    echo -e "${BLUE}   Type: $DISK_TYPE${NC}"
    echo -e "${BLUE}   Status: $DISK_STATUS${NC}"
else
    echo -e "${RED}❌ Persistent disk not found: metabase-data${NC}"
fi

# Check if VM is running and test connectivity
if [ "$VM_STATUS" = "RUNNING" ]; then
    echo -e "${BLUE}📋 Testing connectivity...${NC}"
    
    # Test HTTP connectivity
    if curl -s --connect-timeout 10 http://$VM_IP:3000/api/health &>/dev/null; then
        echo -e "${GREEN}✅ Metabase is responding on port 3000${NC}"
    else
        echo -e "${YELLOW}⚠️  Metabase not responding (may still be starting up)${NC}"
    fi
    
    # Test Nginx
    if curl -s --connect-timeout 10 http://$VM_IP &>/dev/null; then
        echo -e "${GREEN}✅ Nginx is responding on port 80${NC}"
    else
        echo -e "${YELLOW}⚠️  Nginx not responding${NC}"
    fi
    
    # Test domain resolution
    if [ ! -z "$METABASE_DOMAIN" ]; then
        echo -e "${BLUE}📋 Testing domain resolution...${NC}"
        DOMAIN_IP=$(nslookup $METABASE_DOMAIN | grep -A1 "Name:" | tail -1 | awk '{print $2}' 2>/dev/null || echo "")
        
        if [ "$DOMAIN_IP" = "$VM_IP" ]; then
            echo -e "${GREEN}✅ Domain resolves to correct IP: $METABASE_DOMAIN → $VM_IP${NC}"
        else
            echo -e "${YELLOW}⚠️  Domain resolution issue:${NC}"
            echo -e "${YELLOW}   Domain: $METABASE_DOMAIN${NC}"
            echo -e "${YELLOW}   Resolves to: $DOMAIN_IP${NC}"
            echo -e "${YELLOW}   Expected: $VM_IP${NC}"
            echo -e "${YELLOW}   Update your DNS A record to point to: $VM_IP${NC}"
        fi
    fi
else
    echo -e "${YELLOW}⚠️  VM is not running, skipping connectivity tests${NC}"
fi

# Summary
echo ""
echo -e "${GREEN}📊 Deployment Summary:${NC}"
echo -e "${BLUE}🖥️  VM: $VM_NAME ($VM_STATUS)${NC}"
echo -e "${BLUE}⚙️  Type: $PROVISIONING_MODEL instance${NC}"
echo -e "${BLUE}🌐 IP: $VM_IP${NC}"
echo -e "${BLUE}💾 Disk: metabase-data (${DISK_SIZE:-?}GB)${NC}"
echo -e "${BLUE}🔗 URL: https://$METABASE_DOMAIN${NC}"

echo ""
echo -e "${YELLOW}💡 Useful commands:${NC}"
echo -e "• SSH to VM: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE${NC}"
echo -e "• Check Metabase: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='sudo systemctl status metabase'${NC}"
echo -e "• View logs: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='cd /opt/metabase && sudo docker-compose logs -f'${NC}"
echo -e "• Check disk: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='df -h /opt/metabase-data'${NC}"

echo ""
echo -e "${GREEN}✅ Verification completed!${NC}"
