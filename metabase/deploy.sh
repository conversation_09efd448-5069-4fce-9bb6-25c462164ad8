#!/bin/bash

# Metabase VM Deployment Script
# This script deploys a regular (non-preemptible) GCP VM instance with persistent storage
# Execute from within the /metabase directory

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages with timestamp
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" | tee -a deploy.log
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

echo -e "${GREEN}🚀 Metabase VM Deployment Starting...${NC}"
log "Deployment started"

# Verify we're in the correct directory
if [ ! -f "./vm-startup.sh" ] || [ ! -f "./docker-compose.yml" ]; then
    echo -e "${RED}❌ Error: Required files not found in current directory${NC}"
    echo -e "${YELLOW}💡 Please run this script from the /metabase directory${NC}"
    echo -e "${YELLOW}💡 Expected files: ./vm-startup.sh, ./docker-compose.yml, ./.env${NC}"
    exit 1
fi

# Check if .env file exists
if [ ! -f "./.env" ]; then
    echo -e "${RED}❌ Error: .env file not found!${NC}"
    echo -e "${YELLOW}💡 Please copy .env.example to .env and configure your values${NC}"
    exit 1
fi

# Load environment variables
echo -e "${BLUE}📋 Loading environment variables...${NC}"
source ./.env

# Verify gcloud is installed
if ! command_exists gcloud; then
    echo -e "${RED}❌ Error: gcloud CLI not found${NC}"
    echo -e "${YELLOW}💡 Please install Google Cloud SDK: https://cloud.google.com/sdk/docs/install${NC}"
    exit 1
fi

# Check authentication
echo -e "${BLUE}🔐 Checking Google Cloud authentication...${NC}"
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Error: Not authenticated with Google Cloud${NC}"
    echo -e "${YELLOW}💡 Please run: gcloud auth login${NC}"
    exit 1
fi

# Check project configuration
CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null)
if [ "$CURRENT_PROJECT" != "$PROJECT_ID" ]; then
    echo -e "${YELLOW}⚠️  Setting project to $PROJECT_ID${NC}"
    gcloud config set project $PROJECT_ID
fi

# Validate required variables
required_vars=("PROJECT_ID" "VM_NAME" "ZONE" "MACHINE_TYPE")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}❌ Error: $var is not set in .env${NC}"
        exit 1
    fi
done

# Set default domain if not provided
if [ -z "$METABASE_DOMAIN" ]; then
    METABASE_DOMAIN="metabase.yourdomain.com"
    echo -e "${YELLOW}⚠️  METABASE_DOMAIN not set, using default: $METABASE_DOMAIN${NC}"
fi

echo -e "${GREEN}🎯 Deployment Configuration:${NC}"
echo -e "${BLUE}📦 Project: $PROJECT_ID${NC}"
echo -e "${BLUE}🖥️  VM Name: $VM_NAME${NC}"
echo -e "${BLUE}📍 Zone: $ZONE${NC}"
echo -e "${BLUE}⚙️  Machine Type: $MACHINE_TYPE${NC}"
echo -e "${BLUE}🌐 Domain: $METABASE_DOMAIN${NC}"
echo -e "${BLUE}💾 Persistent Disk: metabase-data${NC}"
log "Configuration validated: VM=$VM_NAME, Zone=$ZONE, Type=$MACHINE_TYPE"

# Check if VM already exists
if gcloud compute instances describe $VM_NAME --zone=$ZONE &>/dev/null; then
    VM_STATUS=$(gcloud compute instances describe $VM_NAME --zone=$ZONE --format='get(status)')
    echo -e "${YELLOW}⚠️  VM $VM_NAME already exists (Status: $VM_STATUS)${NC}"

    read -p "Do you want to delete and recreate it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🗑️  Deleting existing VM...${NC}"
        log "Deleting existing VM: $VM_NAME"

        # Stop VM if running
        if [ "$VM_STATUS" = "RUNNING" ]; then
            gcloud compute instances stop $VM_NAME --zone=$ZONE --quiet
            echo -e "${BLUE}⏳ Waiting for VM to stop...${NC}"
            sleep 30
        fi

        # Delete VM
        gcloud compute instances delete $VM_NAME --zone=$ZONE --quiet
        echo -e "${GREEN}✅ VM deleted successfully${NC}"
        log "VM deleted successfully"
    else
        echo -e "${BLUE}ℹ️  Updating existing VM metadata...${NC}"
        log "Updating existing VM metadata"

        # Create temporary files for metadata update
        TEMP_DIR=$(mktemp -d)
        cp ./.env "$TEMP_DIR/metabase-env"
        cp ./docker-compose.yml "$TEMP_DIR/docker-compose"

        # Update metadata
        gcloud compute instances add-metadata $VM_NAME \
          --zone=$ZONE \
          --metadata-from-file startup-script=./vm-startup.sh,metabase-env="$TEMP_DIR/metabase-env",docker-compose="$TEMP_DIR/docker-compose" \
          --metadata metabase-domain="$METABASE_DOMAIN"

        # Clean up temporary files
        rm -rf "$TEMP_DIR"

        # Restart VM to apply changes
        echo -e "${YELLOW}🔄 Restarting VM to apply changes...${NC}"
        if [ "$VM_STATUS" = "RUNNING" ]; then
            gcloud compute instances stop $VM_NAME --zone=$ZONE --quiet
            echo -e "${BLUE}⏳ Waiting for VM to stop...${NC}"
            sleep 30
        fi

        gcloud compute instances start $VM_NAME --zone=$ZONE --quiet
        echo -e "${BLUE}⏳ Waiting for VM to start...${NC}"
        sleep 60

        VM_IP=$(gcloud compute instances describe $VM_NAME --zone=$ZONE --format='get(networkInterfaces[0].accessConfigs[0].natIP)')
        echo -e "${GREEN}✅ VM updated and restarted!${NC}"
        echo -e "${BLUE}🔗 VM IP: $VM_IP${NC}"
        echo -e "${BLUE}🔗 Metabase URL (direct): http://$VM_IP:3000${NC}"
        echo -e "${BLUE}🔗 Metabase URL (domain): https://$METABASE_DOMAIN${NC}"
        log "VM updated successfully: IP=$VM_IP"
        exit 0
    fi
fi

# Check if persistent disk exists (REQUIRED - do not create new one)
echo -e "${YELLOW}💾 Checking existing persistent disk...${NC}"
if ! gcloud compute disks describe metabase-data --zone=$ZONE &>/dev/null; then
    echo -e "${RED}❌ Error: Persistent disk 'metabase-data' not found!${NC}"
    echo -e "${YELLOW}💡 The disk must exist before running this script${NC}"
    echo -e "${YELLOW}💡 Create it with: gcloud compute disks create metabase-data --zone=$ZONE --size=20GB --type=pd-standard${NC}"
    exit 1
else
    DISK_SIZE=$(gcloud compute disks describe metabase-data --zone=$ZONE --format='get(sizeGb)')
    echo -e "${GREEN}✅ Found existing persistent disk 'metabase-data' (${DISK_SIZE}GB)${NC}"
    log "Using existing persistent disk: metabase-data (${DISK_SIZE}GB)"
fi

# Reserve static external IP if not exists
echo -e "${YELLOW}🌐 Checking static IP reservation...${NC}"
STATIC_IP_NAME="metabase-static-ip"
REGION=$(echo $ZONE | sed 's/-[a-z]$//')

if ! gcloud compute addresses describe $STATIC_IP_NAME --region=$REGION &>/dev/null; then
    echo -e "${YELLOW}📍 Reserving static external IP...${NC}"
    gcloud compute addresses create $STATIC_IP_NAME --region=$REGION
    STATIC_IP=$(gcloud compute addresses describe $STATIC_IP_NAME --region=$REGION --format='get(address)')
    echo -e "${GREEN}✅ Static IP reserved: $STATIC_IP${NC}"
    log "Static IP reserved: $STATIC_IP"
else
    STATIC_IP=$(gcloud compute addresses describe $STATIC_IP_NAME --region=$REGION --format='get(address)')
    echo -e "${GREEN}✅ Using existing static IP: $STATIC_IP${NC}"
    log "Using existing static IP: $STATIC_IP"
fi

# Create temporary files for metadata
echo -e "${YELLOW}📝 Preparing metadata files...${NC}"
TEMP_DIR=$(mktemp -d)
cp ./.env "$TEMP_DIR/metabase-env"
cp ./docker-compose.yml "$TEMP_DIR/docker-compose"
log "Metadata files prepared in: $TEMP_DIR"

# Create VM with startup script (regular instance, not spot)
echo -e "${YELLOW}🖥️  Creating VM instance (regular, non-preemptible)...${NC}"
log "Creating VM instance with persistent disk attachment"

gcloud compute instances create $VM_NAME \
  --zone=$ZONE \
  --machine-type=$MACHINE_TYPE \
  --network-interface=network-tier=PREMIUM,stack-type=IPV4_ONLY,subnet=default,address=$STATIC_IP \
  --maintenance-policy=MIGRATE \
  --provisioning-model=STANDARD \
  --service-account=default \
  --scopes=https://www.googleapis.com/auth/cloud-platform \
  --create-disk=auto-delete=yes,boot=yes,device-name=$VM_NAME,image=projects/debian-cloud/global/images/family/debian-12,mode=rw,size=20,type=projects/$PROJECT_ID/zones/$ZONE/diskTypes/pd-standard \
  --disk=name=metabase-data,device-name=metabase-data,mode=rw,boot=no,auto-delete=no \
  --metadata-from-file startup-script=./vm-startup.sh,metabase-env="$TEMP_DIR/metabase-env",docker-compose="$TEMP_DIR/docker-compose" \
  --metadata metabase-domain="$METABASE_DOMAIN" \
  --tags=metabase-server \
  --reservation-affinity=any

echo -e "${GREEN}✅ VM instance created successfully${NC}"
log "VM instance created: $VM_NAME"

# Wait for VM to be ready
echo -e "${YELLOW}⏳ Waiting for VM to initialize...${NC}"
sleep 30

# Create firewall rules if they don't exist
echo -e "${YELLOW}🔥 Configuring firewall rules...${NC}"

# HTTP rule
if ! gcloud compute firewall-rules describe metabase-http &>/dev/null; then
    gcloud compute firewall-rules create metabase-http \
      --allow tcp:80 \
      --source-ranges 0.0.0.0/0 \
      --target-tags metabase-server \
      --description "Allow HTTP traffic to Metabase"
    echo -e "${GREEN}✅ HTTP firewall rule created${NC}"
    log "HTTP firewall rule created"
else
    echo -e "${GREEN}✅ HTTP firewall rule already exists${NC}"
fi

# HTTPS rule
if ! gcloud compute firewall-rules describe metabase-https &>/dev/null; then
    gcloud compute firewall-rules create metabase-https \
      --allow tcp:443 \
      --source-ranges 0.0.0.0/0 \
      --target-tags metabase-server \
      --description "Allow HTTPS traffic to Metabase"
    echo -e "${GREEN}✅ HTTPS firewall rule created${NC}"
    log "HTTPS firewall rule created"
else
    echo -e "${GREEN}✅ HTTPS firewall rule already exists${NC}"
fi

# Metabase direct access rule (optional)
if ! gcloud compute firewall-rules describe metabase-direct &>/dev/null; then
    gcloud compute firewall-rules create metabase-direct \
      --allow tcp:3000 \
      --source-ranges 0.0.0.0/0 \
      --target-tags metabase-server \
      --description "Allow direct access to Metabase"
    echo -e "${GREEN}✅ Metabase direct access firewall rule created${NC}"
    log "Metabase direct access firewall rule created"
else
    echo -e "${GREEN}✅ Metabase direct access firewall rule already exists${NC}"
fi

# Get VM IP (should be the static IP we reserved)
VM_IP=$(gcloud compute instances describe $VM_NAME --zone=$ZONE --format='get(networkInterfaces[0].accessConfigs[0].natIP)')

# Clean up temporary files
rm -rf "$TEMP_DIR"
log "Temporary files cleaned up"

# Deployment completion
echo ""
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${GREEN}✅ Instance Type: Regular (non-preemptible)${NC}"
echo -e "${GREEN}✅ Static IP: $STATIC_IP${NC}"
echo -e "${GREEN}✅ Persistent Disk: metabase-data attached${NC}"
echo -e "${GREEN}✅ Docker services configured for auto-restart${NC}"
echo -e "${GREEN}✅ Systemd service enabled for boot startup${NC}"
echo ""
echo -e "${BLUE}🔗 Connection Information:${NC}"
echo -e "${BLUE}   VM IP: $VM_IP${NC}"
echo -e "${BLUE}   Metabase URL (direct): http://$VM_IP:3000${NC}"
echo -e "${BLUE}   Metabase URL (domain): https://$METABASE_DOMAIN${NC}"
echo ""
echo -e "${YELLOW}📋 Next Steps:${NC}"
echo -e "1. Wait 3-5 minutes for startup script to complete"
echo -e "2. Update DNS A Record to point to: $STATIC_IP"
echo -e "3. Verify services: ./verify-deployment.sh"
echo -e "4. Access Metabase at: https://$METABASE_DOMAIN"
echo -e "5. Complete initial Metabase setup"
echo ""
echo -e "${YELLOW}💡 Useful Commands:${NC}"
echo -e "• SSH to VM: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE${NC}"
echo -e "• Check startup: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='sudo tail -f /var/log/metabase-startup.log'${NC}"
echo -e "• Service status: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='sudo systemctl status metabase'${NC}"
echo -e "• Docker logs: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='cd /opt/metabase && sudo docker-compose logs -f'${NC}"
echo -e "• Troubleshoot: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='sudo /opt/metabase/troubleshoot-disk.sh'${NC}"
echo ""
echo -e "${GREEN}✅ Deployment log saved to: deploy.log${NC}"

# Final logging
log "Deployment completed successfully: VM=$VM_NAME, IP=$VM_IP, Static IP=$STATIC_IP"
log "Services configured: Docker auto-restart, systemd boot startup, persistent disk attached"

echo -e "${GREEN}🚀 Metabase VM deployment finished!${NC}"