#!/bin/bash

# Complete Metabase Setup Script
# This is the ONLY script needed to setup Metabase from scratch with full analytics
# It handles: VM deployment, database connection, questions, dashboard, and visualizations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
METABASE_URL="https://data.izymercado.com.br"
SESSION_FILE="/tmp/metabase_session.txt"

# Function to log with timestamp
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" | tee -a complete-setup.log
}

# Function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3

    if [ -f "$SESSION_FILE" ]; then
        SESSION_TOKEN=$(cat "$SESSION_FILE")
        curl -s -X "$method" \
             -H "Content-Type: application/json" \
             -H "X-Metabase-Session: $SESSION_TOKEN" \
             -d "$data" \
             "$METABASE_URL$endpoint"
    else
        echo "❌ Session file not found"
        return 1
    fi
}

echo -e "${BLUE}🚀 COMPLETE METABASE SETUP - FROM ZERO TO HERO${NC}"
echo -e "${BLUE}================================================${NC}"
log "Starting complete Metabase setup"

# Step 1: Check if VM exists and deploy if needed
echo -e "${YELLOW}📋 Step 1: Checking VM status...${NC}"
if ! gcloud compute instances describe metabase --zone=us-central1-a &>/dev/null; then
    echo -e "${YELLOW}🔧 VM not found. Deploying new VM...${NC}"
    log "Deploying new VM"
    ./deploy.sh
    echo -e "${YELLOW}⏳ Waiting 3 minutes for VM to be ready...${NC}"
    sleep 180
else
    echo -e "${GREEN}✅ VM already exists${NC}"
    log "VM already exists"
fi

# Step 2: Wait for Metabase to be ready
echo -e "${YELLOW}📋 Step 2: Waiting for Metabase to be ready...${NC}"
max_attempts=30
attempt=0
while [ $attempt -lt $max_attempts ]; do
    if curl -s "$METABASE_URL/api/health" &>/dev/null; then
        echo -e "${GREEN}✅ Metabase is ready!${NC}"
        log "Metabase is ready"
        break
    fi
    echo -e "${YELLOW}⏳ Waiting for Metabase... (attempt $((attempt+1))/$max_attempts)${NC}"
    sleep 10
    ((attempt++))
done

if [ $attempt -eq $max_attempts ]; then
    echo -e "${RED}❌ Metabase failed to start${NC}"
    log "ERROR: Metabase failed to start"
    exit 1
fi

# Step 3: Setup admin user and database
echo -e "${YELLOW}📋 Step 3: Setting up admin user and database...${NC}"
echo -e "${BLUE}Please follow these steps:${NC}"
echo -e "1. Open $METABASE_URL in your browser"
echo -e "2. Create admin account with:"
echo -e "   - Email: <EMAIL>"
echo -e "   - Password: IzyMetabase2024!"
echo -e "3. Skip the database setup for now"
echo -e "4. Open browser developer tools (F12)"
echo -e "5. Go to Application/Storage > Cookies"
echo -e "6. Find 'metabase.SESSION' cookie and copy its value"
echo ""
read -p "Enter your Metabase session token: " SESSION_TOKEN

if [ -z "$SESSION_TOKEN" ]; then
    echo -e "${RED}❌ Session token is required${NC}"
    log "ERROR: Session token not provided"
    exit 1
fi

# Save session token
echo "$SESSION_TOKEN" > "$SESSION_FILE"
log "Session token saved"

# Load environment variables
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found${NC}"
    log "ERROR: .env file not found"
    exit 1
fi
source .env

# Step 4: Add production database
echo -e "${YELLOW}📋 Step 4: Adding production database...${NC}"
log "Adding production database"

database_response=$(api_call "POST" "/api/database" "{
    \"engine\": \"postgres\",
    \"name\": \"Izy Mercado Production\",
    \"details\": {
        \"host\": \"$PROD_DB_HOST\",
        \"port\": $PROD_DB_PORT,
        \"dbname\": \"$PROD_DB_NAME\",
        \"user\": \"$PROD_DB_USER\",
        \"password\": \"$PROD_DB_PASSWORD\",
        \"ssl\": false,
        \"tunnel-enabled\": false,
        \"schema-filters-type\": \"all\",
        \"json-unfolding\": false
    },
    \"auto_run_queries\": true,
    \"is_full_sync\": true
}")

database_id=$(echo "$database_response" | jq -r '.id' 2>/dev/null || echo "null")
if [ "$database_id" = "null" ]; then
    echo -e "${RED}❌ Failed to add database${NC}"
    log "ERROR: Failed to add database"
    echo "Response: $database_response"
    exit 1
fi
echo -e "${GREEN}✅ Database added (ID: $database_id)${NC}"
log "Database added with ID: $database_id"

# Step 5: Create Analytics collection
echo -e "${YELLOW}📋 Step 5: Creating Analytics collection...${NC}"
log "Creating Analytics collection"
collection_response=$(api_call "POST" "/api/collection" "{
    \"name\": \"Analytics\",
    \"description\": \"Análises de negócio do Izy Mercado\",
    \"color\": \"#509EE3\"
}")

collection_id=$(echo "$collection_response" | jq -r '.id' 2>/dev/null || echo "null")
if [ "$collection_id" = "null" ]; then
    echo -e "${RED}❌ Failed to create collection${NC}"
    log "ERROR: Failed to create collection"
    echo "Response: $collection_response"
    exit 1
fi
echo -e "${GREEN}✅ Collection created (ID: $collection_id)${NC}"
log "Collection created with ID: $collection_id"

echo -e "${YELLOW}⏳ Waiting for database sync...${NC}"
log "Waiting for database sync"
sleep 60

# Step 6: Create all analytics questions
echo -e "${YELLOW}📋 Step 6: Creating analytics questions...${NC}"
log "Creating analytics questions"

# Function to create a question
create_question() {
    local name="$1"
    local sql="$2"
    local display="$3"
    local viz_settings="$4"

    echo -e "${YELLOW}📊 Creating question: $name${NC}"

    # Escape quotes in SQL properly
    local escaped_sql=$(echo "$sql" | sed 's/"/\\"/g')

    question_data="{
        \"name\": \"$name\",
        \"dataset_query\": {
            \"type\": \"native\",
            \"native\": {
                \"query\": \"$escaped_sql\"
            },
            \"database\": $database_id
        },
        \"display\": \"$display\",
        \"visualization_settings\": ${viz_settings:-{}},
        \"collection_id\": $collection_id
    }"

    response=$(api_call "POST" "/api/card" "$question_data")
    question_id=$(echo "$response" | jq -r '.id' 2>/dev/null || echo "null")

    if [ "$question_id" = "null" ]; then
        echo -e "${RED}❌ Failed to create question: $name${NC}"
        log "ERROR: Failed to create question: $name"
        log "ERROR: Response: $response"
        echo "Response: $response" >&2
        return 1
    else
        echo -e "${GREEN}✅ Created question: $name (ID: $question_id)${NC}"
        log "Created question: $name (ID: $question_id)"
        echo "$question_id"
    fi
}

# Create all analytics questions
question_ids=()

# 1. Produtos Mais Vendidos (Corrigida)
sql1="SELECT
    ip.product_name as \"Nome do Produto\",
    ip.product_brand as \"Marca\",
    SUM(ip.quantity) as \"Quantidade Total Vendida\",
    COUNT(DISTINCT i.id) as \"Número de Pedidos\",
    SUM(ip.quantity * ip.unit_price) / 100.0 as \"Receita Bruta Total (R$)\",
    AVG(ip.unit_price) / 100.0 as \"Preço Médio (R$)\"
FROM invoice_products ip
JOIN invoices i ON ip.invoice_id = i.id
WHERE i.status = 'completed'
    AND i.created_at >= CURRENT_DATE - INTERVAL '6 months'
GROUP BY ip.product_name, ip.product_brand, ip.product_ean, ip.product_external_id
ORDER BY SUM(ip.quantity) DESC
LIMIT 20;"

viz1='{}'
id1=$(create_question "Produtos Mais Vendidos" "$sql1" "table" "$viz1")
question_ids+=("$id1")

# 2. Receita por Período (Corrigida)
sql2="SELECT
    DATE_TRUNC('month', i.created_at) as \"Mês\",
    COUNT(i.id) as \"Total de Pedidos\",
    SUM(i.amount) / 100.0 as \"Receita Bruta (R$)\",
    SUM(i.company_amount) / 100.0 as \"Receita Empresas (R$)\",
    SUM(i.amount - i.company_amount) / 100.0 as \"Comissão Plataforma (R$)\"
FROM invoices i
WHERE i.status = 'completed'
    AND i.created_at >= CURRENT_DATE - INTERVAL '12 months'
GROUP BY DATE_TRUNC('month', i.created_at)
ORDER BY \"Mês\" DESC;"

viz2='{}'
id2=$(create_question "Receita por Período" "$sql2" "line" "$viz2")
question_ids+=("$id2")

# 3. Performance por Empresa (Corrigida)
sql3="SELECT
    c.name as \"Nome da Empresa\",
    COUNT(i.id) as \"Total de Pedidos\",
    SUM(i.amount) / 100.0 as \"Receita Total (R$)\",
    AVG(i.amount) / 100.0 as \"Ticket Médio (R$)\",
    SUM(i.company_amount) / 100.0 as \"Receita Empresa (R$)\",
    SUM(i.amount - i.company_amount) / 100.0 as \"Comissão Gerada (R$)\"
FROM companies c
JOIN invoices i ON c.id = i.company_id
WHERE i.status = 'completed'
    AND i.created_at >= CURRENT_DATE - INTERVAL '3 months'
GROUP BY c.id, c.name
ORDER BY SUM(i.amount) DESC
LIMIT 20;"

viz3='{}'
id3=$(create_question "Performance por Empresa" "$sql3" "table" "$viz3")
question_ids+=("$id3")

# 4. Análise Geográfica (Corrigida)
sql4="SELECT
    ca.state as \"Estado\",
    ca.city as \"Cidade\",
    COUNT(DISTINCT c.id) as \"Número de Empresas\",
    COUNT(i.id) as \"Total de Pedidos\",
    SUM(i.amount) / 100.0 as \"Receita Total (R$)\",
    AVG(i.amount) / 100.0 as \"Ticket Médio (R$)\"
FROM companies c
JOIN company_addresses ca ON c.id = ca.company_id AND ca.is_default = true
JOIN invoices i ON c.id = i.company_id
WHERE i.status = 'completed'
    AND i.created_at >= CURRENT_DATE - INTERVAL '6 months'
GROUP BY ca.state, ca.city
ORDER BY SUM(i.amount) DESC
LIMIT 30;"

viz4='{}'
id4=$(create_question "Análise Geográfica" "$sql4" "table" "$viz4")
question_ids+=("$id4")

# 5. Métricas de Comissão (Corrigida)
sql5="SELECT
    DATE_TRUNC('week', i.created_at) as \"Semana\",
    COUNT(i.id) as \"Pedidos\",
    SUM(i.amount) / 100.0 as \"Receita Bruta (R$)\",
    SUM(i.company_amount) / 100.0 as \"Receita Empresas (R$)\",
    SUM(i.amount - i.company_amount) / 100.0 as \"Comissão Total (R$)\",
    ROUND(AVG((i.amount - i.company_amount) * 100.0 / i.amount), 2) as \"Taxa Média Comissão (%)\"
FROM invoices i
WHERE i.status = 'completed'
    AND i.created_at >= CURRENT_DATE - INTERVAL '8 weeks'
GROUP BY DATE_TRUNC('week', i.created_at)
ORDER BY \"Semana\" DESC;"

viz5='{}'
id5=$(create_question "Métricas de Comissão" "$sql5" "table" "$viz5")
question_ids+=("$id5")

# 4. Análise Geográfica
sql4="SELECT
    c.state as \"Estado\",
    c.city as \"Cidade\",
    COUNT(DISTINCT c.id) as \"Número de Empresas\",
    COUNT(i.id) as \"Total de Pedidos\",
    CONCAT('R$ ', TO_CHAR(SUM(i.total_amount) / 100.0, 'FM999G999G999D00')) as \"Receita Total\",
    CONCAT('R$ ', TO_CHAR(AVG(i.total_amount) / 100.0, 'FM999G999G999D00')) as \"Ticket Médio\"
FROM companies c
JOIN invoices i ON c.id = i.company_id
WHERE i.status = 'completed'
    AND i.created_at >= CURRENT_DATE - INTERVAL '6 months'
GROUP BY c.state, c.city
ORDER BY SUM(i.total_amount) DESC
LIMIT 30;"

viz4='{}'
id4=$(create_question "Análise Geográfica" "$sql4" "bar" "$viz4")
question_ids+=("$id4")

# 5. Métricas de Comissão
sql5="SELECT
    DATE_TRUNC('week', i.created_at) as \"Semana\",
    COUNT(i.id) as \"Pedidos\",
    CONCAT('R$ ', TO_CHAR(SUM(i.total_amount) / 100.0, 'FM999G999G999D00')) as \"Receita Bruta\",
    CONCAT('R$ ', TO_CHAR(SUM(ROUND(i.total_amount * (c.commission_rate / 10000.0))) / 100.0, 'FM999G999G999D00')) as \"Comissão Total\",
    CONCAT(ROUND(AVG(c.commission_rate / 100.0), 2), '%') as \"Taxa Média de Comissão\"
FROM invoices i
JOIN companies c ON i.company_id = c.id
WHERE i.status = 'completed'
    AND i.created_at >= CURRENT_DATE - INTERVAL '8 weeks'
GROUP BY DATE_TRUNC('week', i.created_at)
ORDER BY \"Semana\" DESC;"

viz5='{}'
id5=$(create_question "Métricas de Comissão" "$sql5" "line" "$viz5")
question_ids+=("$id5")

# Step 7: Create main dashboard
echo -e "${YELLOW}📋 Step 7: Creating main dashboard...${NC}"
log "Creating main dashboard"

dashboard_response=$(api_call "POST" "/api/dashboard" "{
    \"name\": \"Izy Mercado - Analytics Principal\",
    \"description\": \"Dashboard principal com métricas de negócio do Izy Mercado\",
    \"collection_id\": $collection_id
}")

dashboard_id=$(echo "$dashboard_response" | jq -r '.id' 2>/dev/null || echo "null")
if [ "$dashboard_id" = "null" ]; then
    echo -e "${RED}❌ Failed to create dashboard${NC}"
    log "ERROR: Failed to create dashboard"
    exit 1
fi
echo -e "${GREEN}✅ Dashboard created (ID: $dashboard_id)${NC}"
log "Dashboard created with ID: $dashboard_id"

# Step 8: Add questions to dashboard
echo -e "${YELLOW}📋 Step 8: Adding questions to dashboard...${NC}"
log "Adding questions to dashboard"

# Add each question to the dashboard with specific positioning
row=0
for i in "${!question_ids[@]}"; do
    question_id="${question_ids[$i]}"
    if [ "$question_id" != "null" ] && [ -n "$question_id" ]; then
        echo -e "${YELLOW}📊 Adding question ID $question_id to dashboard...${NC}"

        # Calculate position (2 questions per row)
        col=$((i % 2))
        if [ $col -eq 0 ] && [ $i -gt 0 ]; then
            ((row++))
        fi

        dashcard_response=$(api_call "POST" "/api/dashboard/$dashboard_id/dashcard" "{
            \"cardId\": $question_id,
            \"row\": $row,
            \"col\": $((col * 12)),
            \"sizeX\": 12,
            \"sizeY\": 8
        }")

        dashcard_id=$(echo "$dashcard_response" | jq -r '.id' 2>/dev/null || echo "null")
        if [ "$dashcard_id" != "null" ]; then
            echo -e "${GREEN}✅ Added question to dashboard${NC}"
        else
            echo -e "${RED}❌ Failed to add question to dashboard${NC}"
        fi
    fi
done

# Step 9: Final setup completion
echo -e "${YELLOW}📋 Step 9: Finalizing setup...${NC}"
log "Finalizing setup"

# Clean up session file
rm -f "$SESSION_FILE"

echo ""
echo -e "${GREEN}🎉 COMPLETE SETUP FINISHED SUCCESSFULLY!${NC}"
echo -e "${GREEN}================================================${NC}"
echo ""
echo -e "${BLUE}📊 Your Metabase Analytics is ready!${NC}"
echo -e "${BLUE}🔗 Dashboard URL: $METABASE_URL/dashboard/$dashboard_id${NC}"
echo -e "${BLUE}🔗 Main URL: $METABASE_URL${NC}"
echo ""
echo -e "${YELLOW}📋 What was created:${NC}"
echo -e "✅ Production database connection"
echo -e "✅ Analytics collection"
echo -e "✅ ${#question_ids[@]} business analytics questions"
echo -e "✅ Main dashboard with all visualizations"
echo ""
echo -e "${YELLOW}💡 Login credentials:${NC}"
echo -e "📧 Email: <EMAIL>"
echo -e "🔑 Password: IzyMetabase2024!"
echo ""
echo -e "${GREEN}🚀 Your business analytics platform is ready to use!${NC}"

log "Complete setup finished successfully"
log "Dashboard URL: $METABASE_URL/dashboard/$dashboard_id"
