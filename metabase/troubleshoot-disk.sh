#!/bin/bash

# Metabase Disk Troubleshooting Script
# This script helps diagnose persistent disk mounting issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔍 Metabase Disk Troubleshooting...${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ Please run as root (use sudo)${NC}"
    exit 1
fi

echo -e "${BLUE}📋 System Information:${NC}"
echo -e "Hostname: $(hostname)"
echo -e "Uptime: $(uptime -p)"
echo ""

echo -e "${BLUE}💾 Available Block Devices:${NC}"
lsblk -f
echo ""

echo -e "${BLUE}🔍 Disk by ID:${NC}"
ls -la /dev/disk/by-id/ | grep -E "(google|scsi)" || echo "No Google/SCSI disks found"
echo ""

echo -e "${BLUE}📊 Disk Usage:${NC}"
df -h
echo ""

echo -e "${BLUE}🔗 Mount Points:${NC}"
mount | grep -E "(metabase|sdb|nvme)" || echo "No metabase-related mounts found"
echo ""

echo -e "${BLUE}📝 fstab entries:${NC}"
grep -E "(metabase|sdb|nvme)" /etc/fstab || echo "No metabase-related fstab entries found"
echo ""

# Check for persistent disk
echo -e "${BLUE}🔍 Checking for Metabase persistent disk...${NC}"
FOUND_DISK=""

# Try different possible device names
for device in /dev/disk/by-id/google-metabase-data /dev/sdb /dev/nvme0n2 /dev/disk/by-id/scsi-0Google_PersistentDisk_metabase-data; do
    if [ -e "$device" ]; then
        FOUND_DISK="$device"
        echo -e "${GREEN}✅ Found persistent disk: $FOUND_DISK${NC}"
        
        # Check if it's formatted
        if blkid $FOUND_DISK &>/dev/null; then
            FSTYPE=$(blkid -o value -s TYPE $FOUND_DISK)
            echo -e "${GREEN}✅ Disk is formatted with: $FSTYPE${NC}"
        else
            echo -e "${YELLOW}⚠️ Disk is not formatted${NC}"
        fi
        
        # Check if it's mounted
        if mount | grep -q "$FOUND_DISK"; then
            MOUNT_POINT=$(mount | grep "$FOUND_DISK" | awk '{print $3}')
            echo -e "${GREEN}✅ Disk is mounted at: $MOUNT_POINT${NC}"
        else
            echo -e "${YELLOW}⚠️ Disk is not mounted${NC}"
        fi
        break
    fi
done

if [ -z "$FOUND_DISK" ]; then
    echo -e "${RED}❌ No persistent disk found${NC}"
    echo -e "${YELLOW}💡 Available disks:${NC}"
    lsblk -rno NAME,SIZE,TYPE | grep disk
fi

echo ""

# Check Metabase directory
echo -e "${BLUE}📁 Metabase Directory Status:${NC}"
if [ -L /opt/metabase ]; then
    TARGET=$(readlink /opt/metabase)
    echo -e "${GREEN}✅ /opt/metabase is a symlink to: $TARGET${NC}"
    if [ -d "$TARGET" ]; then
        echo -e "${GREEN}✅ Target directory exists${NC}"
    else
        echo -e "${RED}❌ Target directory does not exist${NC}"
    fi
elif [ -d /opt/metabase ]; then
    echo -e "${YELLOW}⚠️ /opt/metabase is a regular directory${NC}"
else
    echo -e "${RED}❌ /opt/metabase does not exist${NC}"
fi

# Check data directories
echo -e "${BLUE}📊 Data Directory Status:${NC}"
for dir in /opt/metabase-data /opt/metabase-data/postgres_data /opt/metabase-data/metabase_data /opt/metabase/postgres_data /opt/metabase/metabase_data; do
    if [ -d "$dir" ]; then
        SIZE=$(du -sh "$dir" 2>/dev/null | cut -f1)
        echo -e "${GREEN}✅ $dir exists (size: $SIZE)${NC}"
    else
        echo -e "${YELLOW}⚠️ $dir does not exist${NC}"
    fi
done

echo ""

# Check Docker and Metabase status
echo -e "${BLUE}🐳 Docker Status:${NC}"
if systemctl is-active --quiet docker; then
    echo -e "${GREEN}✅ Docker is running${NC}"
    
    # Check if Metabase containers are running
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(metabase|postgres)" &>/dev/null; then
        echo -e "${GREEN}✅ Metabase containers are running:${NC}"
        docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(metabase|postgres)"
    else
        echo -e "${YELLOW}⚠️ No Metabase containers running${NC}"
    fi
else
    echo -e "${RED}❌ Docker is not running${NC}"
fi

echo ""

# Check Metabase service
echo -e "${BLUE}⚙️ Metabase Service Status:${NC}"
if systemctl is-active --quiet metabase; then
    echo -e "${GREEN}✅ Metabase service is active${NC}"
elif systemctl is-enabled --quiet metabase; then
    echo -e "${YELLOW}⚠️ Metabase service is enabled but not active${NC}"
    echo -e "${BLUE}Service status:${NC}"
    systemctl status metabase --no-pager -l
else
    echo -e "${RED}❌ Metabase service is not enabled${NC}"
fi

echo ""

# Provide recommendations
echo -e "${YELLOW}💡 Troubleshooting Recommendations:${NC}"

if [ -z "$FOUND_DISK" ]; then
    echo -e "1. Check if persistent disk was created: ${BLUE}gcloud compute disks list${NC}"
    echo -e "2. Verify disk is attached to VM: ${BLUE}gcloud compute instances describe metabase --zone=us-central1-a${NC}"
    echo -e "3. Try restarting the VM: ${BLUE}gcloud compute instances stop/start metabase${NC}"
elif [ ! -z "$FOUND_DISK" ] && ! mount | grep -q "$FOUND_DISK"; then
    echo -e "1. Try manual mount: ${BLUE}sudo mount $FOUND_DISK /opt/metabase-data${NC}"
    echo -e "2. Check disk for errors: ${BLUE}sudo fsck $FOUND_DISK${NC}"
    echo -e "3. Format if needed: ${BLUE}sudo mkfs.ext4 -F $FOUND_DISK${NC}"
fi

echo -e "4. Check startup logs: ${BLUE}sudo tail -f /var/log/metabase-startup.log${NC}"
echo -e "5. Restart Metabase service: ${BLUE}sudo systemctl restart metabase${NC}"
echo -e "6. Check Docker logs: ${BLUE}cd /opt/metabase && sudo docker-compose logs -f${NC}"

echo ""
echo -e "${GREEN}✅ Troubleshooting completed!${NC}"
