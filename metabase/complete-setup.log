2025-07-16 20:27:56: Starting complete Metabase setup
2025-07-16 20:27:58: VM already exists
2025-07-16 20:27:59: Metabase is ready
2025-07-16 20:28:13: Session token saved
2025-07-16 20:28:13: Adding production database
2025-07-16 20:28:14: Database added with ID: 3
2025-07-16 20:28:14: Creating Analytics collection
2025-07-16 20:28:14: Collection created with ID: 5
2025-07-16 20:28:14: Waiting for database sync
2025-07-16 20:29:14: Creating analytics questions
2025-07-16 20:29:15: ERROR: Failed to create question: Produtos Mais Vendidos
2025-07-16 20:33:22: Starting complete Metabase setup
2025-07-16 20:33:25: VM already exists
2025-07-16 20:33:25: Metabase is ready
2025-07-16 20:33:41: Session token saved
2025-07-16 20:33:41: Adding production database
2025-07-16 20:33:42: ERROR: Failed to add database
2025-07-16 20:35:07: Starting complete Metabase setup
2025-07-16 20:35:09: VM already exists
2025-07-16 20:35:10: Metabase is ready
2025-07-16 20:35:11: Session token saved
2025-07-16 20:35:11: Adding production database
2025-07-16 20:35:12: Database added with ID: 4
2025-07-16 20:35:12: Creating Analytics collection
2025-07-16 20:35:13: Collection created with ID: 6
2025-07-16 20:35:13: Waiting for database sync
2025-07-16 20:36:13: Creating analytics questions
2025-07-16 20:36:13: ERROR: Failed to create question: Produtos Mais Vendidos
2025-07-16 20:44:26: Starting complete Metabase setup
2025-07-16 20:44:28: VM already exists
2025-07-16 20:44:29: Metabase is ready
2025-07-16 20:44:38: Session token saved
2025-07-16 20:44:38: Adding production database
2025-07-16 20:44:39: Database added with ID: 5
2025-07-16 20:44:39: Creating Analytics collection
2025-07-16 20:44:39: Collection created with ID: 7
2025-07-16 20:44:39: Waiting for database sync
2025-07-16 20:45:39: Creating analytics questions
2025-07-16 20:45:40: Created question: Produtos Mais Vendidos (ID: 39)
2025-07-16 20:45:41: Created question: Receita por Período (ID: 40)
2025-07-16 20:45:41: Created question: Performance por Empresa (ID: 41)
2025-07-16 20:45:42: Created question: Análise Geográfica (ID: 42)
2025-07-16 20:45:43: Created question: Métricas de Comissão (ID: 43)
2025-07-16 20:45:43: Creating main dashboard
2025-07-16 20:45:44: Dashboard created with ID: 2
2025-07-16 20:45:44: Adding questions to dashboard
2025-07-16 20:51:06: Starting complete Metabase setup
2025-07-16 20:51:08: VM already exists
2025-07-16 20:51:09: Metabase is ready
2025-07-16 20:51:19: Session token saved
2025-07-16 20:51:19: Adding production database
2025-07-16 20:51:19: Database added with ID: 6
2025-07-16 20:51:19: Creating Analytics collection
2025-07-16 20:51:20: Collection created with ID: 8
2025-07-16 20:51:20: Waiting for database sync
2025-07-16 20:52:20: Creating analytics questions
2025-07-16 20:52:21: Created question: Produtos Mais Vendidos (ID: 45)
2025-07-16 20:52:21: Created question: Receita por Período (ID: 46)
2025-07-16 20:52:22: Created question: Performance por Empresa (ID: 47)
2025-07-16 20:52:23: Created question: Análise Geográfica (ID: 48)
2025-07-16 20:52:23: Created question: Métricas de Comissão (ID: 49)
2025-07-16 20:52:23: Creating main dashboard
2025-07-16 20:52:24: Dashboard created with ID: 3
2025-07-16 20:52:24: Adding questions to dashboard
2025-07-16 20:53:41: Starting complete Metabase setup
2025-07-16 20:53:43: VM already exists
2025-07-16 20:53:44: Metabase is ready
2025-07-16 20:53:48: Session token saved
2025-07-16 20:53:48: Adding production database
2025-07-16 20:53:48: ERROR: Failed to add database
2025-07-16 20:53:55: Starting complete Metabase setup
2025-07-16 20:53:57: VM already exists
2025-07-16 20:53:58: Metabase is ready
2025-07-16 20:53:58: Session token saved
2025-07-16 20:53:58: Adding production database
2025-07-16 20:53:59: Database added with ID: 7
2025-07-16 20:53:59: Creating Analytics collection
2025-07-16 20:54:00: Collection created with ID: 9
2025-07-16 20:54:00: Waiting for database sync
2025-07-16 20:55:00: Creating analytics questions
2025-07-16 20:55:01: Created question: Produtos Mais Vendidos (ID: 50)
2025-07-16 20:55:01: Created question: Receita por Período (ID: 51)
2025-07-16 20:55:02: Created question: Performance por Empresa (ID: 52)
2025-07-16 20:55:02: Created question: Análise Geográfica (ID: 53)
2025-07-16 20:55:03: Created question: Métricas de Comissão (ID: 54)
2025-07-16 20:55:04: Created question: Análise Geográfica (ID: 55)
2025-07-16 20:55:04: Created question: Métricas de Comissão (ID: 56)
2025-07-16 20:55:04: Creating main dashboard
2025-07-16 20:55:05: Dashboard created with ID: 4
2025-07-16 20:55:05: Adding questions to dashboard
2025-07-16 21:00:54: Starting complete Metabase setup
2025-07-16 21:00:57: VM already exists
2025-07-16 21:00:58: Metabase is ready
2025-07-16 21:01:07: Session token saved
2025-07-16 21:01:07: Adding production database
2025-07-16 21:01:08: Database added with ID: 8
2025-07-16 21:01:08: Creating Analytics collection
2025-07-16 21:01:09: Collection created with ID: 10
2025-07-16 21:01:09: Waiting for database sync
2025-07-16 21:02:09: Creating analytics questions
2025-07-16 21:02:09: Created question: Produtos Mais Vendidos (ID: 57)
2025-07-16 21:02:10: Created question: Receita por Período (ID: 58)
2025-07-16 21:02:11: Created question: Performance por Empresa (ID: 59)
2025-07-16 21:02:11: Created question: Análise Geográfica (ID: 60)
2025-07-16 21:02:12: Created question: Métricas de Comissão (ID: 61)
2025-07-16 21:02:12: Created question: Análise Geográfica (ID: 62)
2025-07-16 21:02:13: Created question: Métricas de Comissão (ID: 63)
2025-07-16 21:02:13: Creating main dashboard
2025-07-16 21:02:14: Dashboard created with ID: 5
2025-07-16 21:02:14: Adding questions to dashboard
