#!/bin/bash

# VM Startup Script for Metabase
# This script will be executed when the VM starts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting Metabase VM initialization...${NC}"

# Update system
echo -e "${BLUE}📦 Updating system packages...${NC}"
apt-get update
apt-get upgrade -y

# Install Docker
echo -e "${BLUE}🐳 Installing Docker...${NC}"
if ! command -v docker &> /dev/null; then
    # Install Docker
    apt-get install -y ca-certificates curl gnupg lsb-release
    mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # Start and enable Docker
    systemctl start docker
    systemctl enable docker
    
    echo -e "${GREEN}✅ Docker installed successfully${NC}"
else
    echo -e "${GREEN}✅ Docker already installed${NC}"
fi

# Install Docker Compose (standalone)
echo -e "${BLUE}🔧 Installing Docker Compose...${NC}"
if ! command -v docker-compose &> /dev/null; then
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    echo -e "${GREEN}✅ Docker Compose installed successfully${NC}"
else
    echo -e "${GREEN}✅ Docker Compose already installed${NC}"
fi

# Install additional tools
echo -e "${BLUE}🛠️ Installing additional tools...${NC}"
apt-get install -y curl wget htop nano git unzip nginx ufw

# Mount persistent disk for data preservation
echo -e "${BLUE}💾 Setting up persistent disk...${NC}"
MOUNT_POINT="/opt/metabase-data"

# Create mount point
mkdir -p $MOUNT_POINT

# Find the persistent disk device
echo -e "${BLUE}🔍 Detecting persistent disk...${NC}"
DEVICE_NAME=""

# Try different possible device names
for device in /dev/disk/by-id/google-metabase-data /dev/sdb /dev/nvme0n2 /dev/disk/by-id/scsi-0Google_PersistentDisk_metabase-data; do
    if [ -e "$device" ]; then
        DEVICE_NAME="$device"
        echo -e "${GREEN}✅ Found persistent disk: $DEVICE_NAME${NC}"
        break
    fi
done

# If no device found, list available disks for debugging
if [ -z "$DEVICE_NAME" ]; then
    echo -e "${YELLOW}⚠️ Persistent disk not found. Available disks:${NC}"
    lsblk
    echo -e "${YELLOW}Available disk IDs:${NC}"
    ls -la /dev/disk/by-id/ | grep -E "(google|scsi)" || true

    # Try to find any additional disk that's not the boot disk
    ADDITIONAL_DISK=$(lsblk -rno NAME,TYPE | grep disk | grep -v $(lsblk -rno NAME,MOUNTPOINT | grep "/$" | cut -d' ' -f1 | sed 's/[0-9]*$//') | head -1 | cut -d' ' -f1)
    if [ ! -z "$ADDITIONAL_DISK" ]; then
        DEVICE_NAME="/dev/$ADDITIONAL_DISK"
        echo -e "${YELLOW}🔍 Using detected additional disk: $DEVICE_NAME${NC}"
    else
        echo -e "${RED}❌ No persistent disk found. Continuing without persistent storage...${NC}"
        # Create local directory instead
        mkdir -p $MOUNT_POINT
        chown -R root:root $MOUNT_POINT
        chmod 755 $MOUNT_POINT
        DEVICE_NAME=""
    fi
fi

# If we found a device, format and mount it
if [ ! -z "$DEVICE_NAME" ]; then
    # Check if disk is already formatted
    if ! blkid $DEVICE_NAME &>/dev/null; then
        echo -e "${YELLOW}📀 Formatting persistent disk...${NC}"
        mkfs.ext4 -F $DEVICE_NAME
    else
        echo -e "${GREEN}✅ Persistent disk already formatted${NC}"
    fi

    # Mount the disk
    echo -e "${BLUE}🔗 Mounting persistent disk...${NC}"
    if mount $DEVICE_NAME $MOUNT_POINT; then
        echo -e "${GREEN}✅ Persistent disk mounted successfully${NC}"

        # Add to fstab for automatic mounting on boot
        if ! grep -q "$DEVICE_NAME" /etc/fstab; then
            echo "$DEVICE_NAME $MOUNT_POINT ext4 defaults 0 2" >> /etc/fstab
            echo -e "${GREEN}✅ Added to fstab for automatic mounting${NC}"
        fi
    else
        echo -e "${RED}❌ Failed to mount persistent disk. Using local storage...${NC}"
        DEVICE_NAME=""
    fi
fi

# Set proper permissions
chown -R root:root $MOUNT_POINT
chmod 755 $MOUNT_POINT

# Create metabase directory (symlink to persistent storage if available)
echo -e "${BLUE}📁 Setting up Metabase directory...${NC}"
if [ ! -z "$DEVICE_NAME" ]; then
    # Using persistent storage
    if [ ! -L /opt/metabase ]; then
        # If /opt/metabase exists and is not a symlink, back it up
        if [ -d /opt/metabase ] && [ ! -L /opt/metabase ]; then
            mv /opt/metabase /opt/metabase.backup.$(date +%Y%m%d_%H%M%S)
        fi

        # Create metabase directory in persistent storage
        mkdir -p $MOUNT_POINT/metabase

        # Create symlink
        ln -sf $MOUNT_POINT/metabase /opt/metabase
        echo -e "${GREEN}✅ Metabase directory linked to persistent storage${NC}"
    fi
else
    # Using local storage (fallback)
    if [ ! -d /opt/metabase ]; then
        mkdir -p /opt/metabase
        echo -e "${YELLOW}⚠️ Using local storage for Metabase (data may not persist)${NC}"
    fi
fi

cd /opt/metabase

# Download configuration files from metadata (if available)
echo -e "${BLUE}📥 Downloading configuration files...${NC}"

# Create .env file from metadata (only if it doesn't exist to preserve data)
if [ ! -f /opt/metabase/.env ]; then
    if curl -f -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/attributes/metabase-env > /opt/metabase/.env 2>/dev/null; then
        echo -e "${GREEN}✅ Environment file downloaded from metadata${NC}"
    else
        echo -e "${YELLOW}⚠️ Creating default environment file${NC}"
        cat > /opt/metabase/.env << 'EOF'
# Metabase Database Configuration
METABASE_DB_PASSWORD=secure_metabase_password_change_me

# Production Database Configuration (read-only access)
PROD_DB_HOST=your-production-db-host
PROD_DB_NAME=izy_mercado
PROD_DB_USER=metabase_readonly
PROD_DB_PASSWORD=your-readonly-password
PROD_DB_PORT=5432
EOF
    fi
else
    echo -e "${GREEN}✅ Using existing environment file (data preserved)${NC}"
fi

# Add storage path configuration to .env file
echo -e "${BLUE}📝 Configuring storage paths...${NC}"
if [ ! -z "$DEVICE_NAME" ]; then
    # Using persistent storage
    if ! grep -q "POSTGRES_DATA_PATH" /opt/metabase/.env; then
        echo "" >> /opt/metabase/.env
        echo "# Storage Configuration (Persistent Disk)" >> /opt/metabase/.env
        echo "POSTGRES_DATA_PATH=/opt/metabase-data/postgres_data" >> /opt/metabase/.env
        echo "METABASE_DATA_PATH=/opt/metabase-data/metabase_data" >> /opt/metabase/.env
    fi
    echo -e "${GREEN}✅ Configured for persistent storage${NC}"
else
    # Using local storage
    if ! grep -q "POSTGRES_DATA_PATH" /opt/metabase/.env; then
        echo "" >> /opt/metabase/.env
        echo "# Storage Configuration (Local Storage)" >> /opt/metabase/.env
        echo "POSTGRES_DATA_PATH=/opt/metabase/postgres_data" >> /opt/metabase/.env
        echo "METABASE_DATA_PATH=/opt/metabase/metabase_data" >> /opt/metabase/.env
    fi
    echo -e "${YELLOW}⚠️ Configured for local storage${NC}"
fi

# Download docker-compose.yml from metadata (only if it doesn't exist to preserve data)
if [ ! -f /opt/metabase/docker-compose.yml ]; then
    if curl -f -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/attributes/docker-compose > /opt/metabase/docker-compose.yml 2>/dev/null; then
        echo -e "${GREEN}✅ Docker Compose file downloaded from metadata${NC}"
    else
        echo -e "${YELLOW}⚠️ Docker Compose file not found in metadata${NC}"
        echo -e "${YELLOW}💡 Please upload docker-compose.yml manually${NC}"
    fi
else
    echo -e "${GREEN}✅ Using existing Docker Compose file (data preserved)${NC}"
fi

# Create directories for data storage
echo -e "${BLUE}📁 Creating data directories...${NC}"
if [ ! -z "$DEVICE_NAME" ]; then
    # Using persistent storage
    mkdir -p /opt/metabase-data/postgres_data
    mkdir -p /opt/metabase-data/metabase_data
    chmod 755 /opt/metabase-data/postgres_data
    chmod 755 /opt/metabase-data/metabase_data
    chown -R root:root /opt/metabase-data
    echo -e "${GREEN}✅ Data directories created on persistent disk${NC}"
else
    # Using local storage
    mkdir -p /opt/metabase/postgres_data
    mkdir -p /opt/metabase/metabase_data
    chmod 755 /opt/metabase/postgres_data
    chmod 755 /opt/metabase/metabase_data
    echo -e "${YELLOW}⚠️ Data directories created on local storage${NC}"
fi

# Set proper permissions
chown -R root:root /opt/metabase
chmod 600 /opt/metabase/.env

# Create systemd service for auto-start
echo -e "${BLUE}⚙️ Creating systemd service...${NC}"
cat > /etc/systemd/system/metabase.service << 'EOF'
[Unit]
Description=Metabase Analytics
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/metabase
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl daemon-reload
systemctl enable metabase.service

# Configure Nginx reverse proxy
echo -e "${BLUE}🌐 Configuring Nginx reverse proxy...${NC}"

# Get domain from metadata or use default
METABASE_DOMAIN=$(curl -f -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/attributes/metabase-domain 2>/dev/null || echo "metabase.yourdomain.com")

# Create Nginx configuration
cat > /etc/nginx/sites-available/metabase << EOF
server {
    listen 80;
    server_name ${METABASE_DOMAIN};

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/metabase /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
nginx -t

# Start and enable Nginx
systemctl start nginx
systemctl enable nginx

echo -e "${GREEN}✅ Nginx configured for domain: ${METABASE_DOMAIN}${NC}"

# Configure firewall
echo -e "${BLUE}🔥 Configuring firewall...${NC}"
if command -v ufw &> /dev/null; then
    ufw --force enable
    ufw allow ssh
    ufw allow 80/tcp
    ufw allow 443/tcp
    ufw allow 3000/tcp  # Keep for direct access if needed
    ufw --force reload
    echo -e "${GREEN}✅ UFW firewall configured${NC}"
else
    echo -e "${YELLOW}⚠️ UFW not available, using iptables for basic firewall${NC}"
    # Basic iptables rules as fallback
    iptables -A INPUT -i lo -j ACCEPT
    iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
    iptables -A INPUT -p tcp --dport 22 -j ACCEPT
    iptables -A INPUT -p tcp --dport 80 -j ACCEPT
    iptables -A INPUT -p tcp --dport 443 -j ACCEPT
    iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
    iptables -A INPUT -j DROP
    echo -e "${GREEN}✅ Basic iptables firewall configured${NC}"
fi

echo -e "${GREEN}🎉 VM initialization completed!${NC}"
echo -e "${GREEN}✅ Regular (non-preemptible) instance configured${NC}"
if [ ! -z "$DEVICE_NAME" ]; then
    echo -e "${GREEN}✅ Persistent disk mounted at /opt/metabase-data${NC}"
    echo -e "${GREEN}✅ Data will persist across VM restarts${NC}"
else
    echo -e "${YELLOW}⚠️ Using local storage (data may not persist)${NC}"
fi
echo -e "${GREEN}✅ Static IP configured for DNS stability${NC}"
echo -e "${BLUE}📋 Next steps:${NC}"
echo -e "1. Configure /opt/metabase/.env with your database credentials"
echo -e "2. Upload docker-compose.yml to /opt/metabase/"
echo -e "3. Start Metabase: systemctl start metabase"
echo -e "4. Check status: systemctl status metabase"
echo -e "5. View logs: docker-compose -f /opt/metabase/docker-compose.yml logs -f"
echo -e "6. Access via: https://${METABASE_DOMAIN}"
echo -e "7. Check Nginx: systemctl status nginx"
echo -e "8. Data is preserved in persistent disk: /opt/metabase-data"

# Log completion
echo "$(date): VM startup script completed" >> /var/log/metabase-startup.log
