version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: metabase-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: metabase
      POSTGRES_USER: metabase
      POSTGRES_PASSWORD: ${METABASE_DB_PASSWORD}
    volumes:
      - ${POSTGRES_DATA_PATH:-/opt/metabase-data/postgres_data}:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - metabase-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U metabase -d metabase"]
      interval: 30s
      timeout: 10s
      retries: 3

  metabase:
    image: metabase/metabase:latest
    container_name: metabase-app
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      # Metabase database configuration (for Metabase's own data)
      MB_DB_TYPE: postgres
      MB_DB_DBNAME: metabase
      MB_DB_PORT: 5432
      MB_DB_USER: metabase
      MB_DB_PASS: ${METABASE_DB_PASSWORD}
      MB_DB_HOST: postgres
      
      # Java options for performance
      JAVA_OPTS: "-Xmx1g -Djava.awt.headless=true"
      
      # Jetty configuration
      MB_JETTY_HOST: 0.0.0.0
      MB_JETTY_PORT: 3000
      
      # Production database connection (read-only access to your app data)
      PROD_DB_HOST: ${PROD_DB_HOST}
      PROD_DB_NAME: ${PROD_DB_NAME}
      PROD_DB_USER: ${PROD_DB_USER}
      PROD_DB_PASSWORD: ${PROD_DB_PASSWORD}
      PROD_DB_PORT: ${PROD_DB_PORT:-5432}
    ports:
      - "3000:3000"
    volumes:
      - ${METABASE_DATA_PATH:-/opt/metabase-data/metabase_data}:/metabase-data
    networks:
      - metabase-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# Volumes are now using persistent disk paths directly
# No need for named volumes since we're using host paths

networks:
  metabase-network:
    driver: bridge
