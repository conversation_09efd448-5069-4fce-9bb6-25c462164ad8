# Pull Request Template

## Description

<!-- Please include a summary of the change and which issue is addressed. -->
<!-- Include relevant motivation and context. -->
<!-- Link to the issue this PR resolves, if applicable. -->

Fixes: [#IssueNumber](https://github.com/${{ github.repository }}/issues/${{ issueNumber }})

## Type of Change

<!-- Please select the type of change that applies to this PR (add an X in the box). -->
- [ ] **Major**: Introduces a breaking change (changes that will break backwards compatibility).
- [ ] **Minor**: Adds a new feature or functionality (backwards compatible).
- [ ] **Patch**: Fixes a bug or minor issue (backwards compatible).

> **Important:** Please ensure you label the PR appropriately to reflect the version change.
> - `major`: Breaking changes
> - `minor`: New features
> - `patch`: Bug fixes

## Changelog

<!-- Please provide a list of changes made in this PR. -->
<!-- Each item should follow this format: -->
- **[type]**: short description of the change (e.g. **[feature]**: added user authentication API)

### Example:
- **[feature]**: Added user authentication flow.
- **[bugfix]**: Fixed issue with payment gateway response time.
- **[breaking]**: Removed deprecated `v1` API endpoints, now only supporting `v2`.

## Checklist

- [ ] I have checked the code for possible breaking changes.
- [ ] I have added tests that validate the changes made in this PR (if applicable).
- [ ] I have updated documentation if necessary.
- [ ] I have included relevant labels (`major`, `minor`, `patch`) based on the type of change this PR introduces.
- [ ] The PR follows the coding conventions of the repository.

## PR Labels:

Please apply one of the following labels to indicate the version change:
- `major`: For **breaking changes** that will affect backwards compatibility.
- `minor`: For **new features** or functionality additions that are backwards compatible.
- `patch`: For **bug fixes** or **small improvements** that are backwards compatible.

> **Note**: The versioning is handled automatically based on the label you choose, following Semantic Versioning (SemVer). Ensure the correct label is applied.

---

**DO NOT MERGE** this PR until the version label (`major`, `minor`, `patch`) has been set.

---

## Screenshots (if applicable)

<!-- If your change involves a UI, include screenshots that show what the changes look like. -->

