name: Run Go Tests on Pull Request
on:
  pull_request:
    branches:
      - main

jobs:
  test:
    name: Test Go Application
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.23.4"

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download Swaggo
        run: go install github.com/swaggo/swag/cmd/swag@latest

      - name: Generate Swagger docs
        run: bash scripts/swagger.sh

      - name: Install dependencies
        run: go mod tidy

      - name: Run tests
        run: go test -cover ./...
