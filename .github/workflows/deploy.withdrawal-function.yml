name: Deploy Withdrawal Processor Function

on:
  push:
    branches:
      - main
    paths:
      - 'functions/withdrawal-processor/**'
  workflow_dispatch:

env:
  GCP_PROJECT_ID: axial-radius-455022-n2
  REGION: us-central1
  FUNCTION_NAME: withdrawal-processor

jobs:
  deploy-function:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.GCP_PROJECT_ID }}

    - name: Verify authentication
      run: gcloud auth list

    - name: Create Pub/Sub Topic
      run: |
        # Create Pub/Sub topic for withdrawal processor events
        gcloud pubsub topics create withdrawal-processor-events \
          --project=${{ env.GCP_PROJECT_ID }} || echo "Topic already exists"

    - name: Deploy Cloud Function
      working-directory: functions/withdrawal-processor
      run: |
        gcloud functions deploy ${{ env.FUNCTION_NAME }} \
          --gen2 \
          --runtime=go123 \
          --region=${{ env.REGION }} \
          --source=. \
          --entry-point=ProcessWithdrawalPayouts \
          --trigger-topic=withdrawal-processor-events \
          --memory=256MB \
          --timeout=540s \
          --max-instances=10 \
          --min-instances=0 \
          --set-secrets="DATABASE_URL=DATABASE_URL:latest" \
          --vpc-connector=cloudsql-connector \
          --project=${{ env.GCP_PROJECT_ID }}

    - name: Create/Update Cloud Scheduler Jobs
      run: |
        
        # Create payout updater job (every hour) - publishes to Pub/Sub
        gcloud scheduler jobs create pubsub withdrawal-payout-updater \
          --location=${{ env.REGION }} \
          --schedule="0 * * * *" \
          --topic=withdrawal-processor-events \
          --message-body='{"action":"update_payouts"}' \
          --project=${{ env.GCP_PROJECT_ID }} \
          --quiet || \
        gcloud scheduler jobs update pubsub withdrawal-payout-updater \
          --location=${{ env.REGION }} \
          --schedule="0 * * * *" \
          --topic=withdrawal-processor-events \
          --message-body='{"action":"update_payouts"}' \
          --project=${{ env.GCP_PROJECT_ID }}
        
        # Create balance refresher job (every 15 minutes) - publishes to Pub/Sub
        gcloud scheduler jobs create pubsub withdrawal-balance-refresher \
          --location=${{ env.REGION }} \
          --schedule="*/15 * * * *" \
          --topic=withdrawal-processor-events \
          --message-body='{"action":"refresh_balance_view"}' \
          --project=${{ env.GCP_PROJECT_ID }} \
          --quiet || \
        gcloud scheduler jobs update pubsub withdrawal-balance-refresher \
          --location=${{ env.REGION }} \
          --schedule="*/15 * * * *" \
          --topic=withdrawal-processor-events \
          --message-body='{"action":"refresh_balance_view"}' \
          --project=${{ env.GCP_PROJECT_ID }}

    - name: Test Function
      run: |
        echo "Testing function by publishing test message to Pub/Sub..."
        gcloud pubsub topics publish withdrawal-processor-events \
          --message='{"action":"update_payouts"}' \
          --project=${{ env.GCP_PROJECT_ID }} || echo "Function test failed"
        echo "Test message published successfully"

    - name: Deployment Summary
      run: |
        echo "🎉 Withdrawal Processor Function deployed successfully!"
        echo "📍 Function Trigger: Pub/Sub topic 'withdrawal-processor-events'"
        echo "⏰ Payout updates: Every hour via Cloud Scheduler"
        echo "🔄 Balance refresh: Every 15 minutes via Cloud Scheduler"
        echo "🚀 Architecture: Event-driven with Pub/Sub for better scalability"
