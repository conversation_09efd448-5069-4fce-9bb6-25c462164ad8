name: Auto-Tag & Release

on:
  pull_request:
    types:
      - closed  # Trigger only when the PR is closed (merged or closed manually)
    branches:
      - main  # Only trigger on merges to the main branch

jobs:
  tag:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Ensure full history for changelog generation

      - name: Set up Git user
        run: |
          git config --global user.name "github-actions"
          git config --global user.email "<EMAIL>"
      
      # Ensure the PR is merged and not just closed without merging
      - name: Check PR merge status
        run: |
          if [[ "${{ github.event.pull_request.merged }}" != "true" ]]; then
            echo "PR not merged. Skipping version increment." && exit 0
          fi
          
      # Get PR Labels for versioning
      - name: Get PR Labels
        id: pr_labels
        run: |
          pr_labels=$(curl -s \
            -H "Authorization: token ${{ secrets.PAT }}" \
            "https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.pull_request.number }}/labels" | jq -r '.[].name')

          echo "PR Labels: $pr_labels"

          # Default to patch increment
          major_increment=0
          minor_increment=0
          patch_increment=1  # Default increment

          # Check PR labels for versioning
          if [[ "$pr_labels" == *"major"* ]]; then
            major_increment=1
            minor_increment=0
            patch_increment=0
          elif [[ "$pr_labels" == *"minor"* ]]; then
            major_increment=0
            minor_increment=1
            patch_increment=0
          elif [[ "$pr_labels" == *"patch"* ]]; then
            major_increment=0
            minor_increment=0
            patch_increment=1
          fi

          echo "major_increment=$major_increment" >> "$GITHUB_ENV"
          echo "minor_increment=$minor_increment" >> "$GITHUB_ENV"
          echo "patch_increment=$patch_increment" >> "$GITHUB_ENV"

      - name: Determine Next Tag Version
        id: get_version
        run: |
          git fetch --tags || exit 1
          latest_tag=$(git tag --sort=-v:refname | grep -E '^v[0-9]+\.[0-9]+\.[0-9]+$' | head -n 1)

          if [[ -z "$latest_tag" ]]; then
            latest_tag="v0.0.0"
          fi

          IFS='.' read -r major minor patch <<< "${latest_tag//v/}"

          # Increment version parts based on labels
          new_major=$((major + major_increment))
          new_minor=$((minor + minor_increment))
          new_patch=$((patch + patch_increment))

          # If a major increment, reset minor and patch to 0
          if [[ $major_increment -eq 1 ]]; then
            new_minor=0
            new_patch=0
          fi

          new_version="v$new_major.$new_minor.$new_patch"
          echo "New version: $new_version"
          echo "new_version=$new_version" >> "$GITHUB_ENV"  # Store the new version in the environment

      - name: Get Commit Messages for Changelog
        id: changelog
        run: |
          if [ -z "$(git tag)" ]; then
            echo "changelog=First release 🎉" >> "$GITHUB_ENV"
          else
            changes=$(git log ${latest_tag}..HEAD --pretty=format:"- %s (%an)" --reverse)
            echo "changelog=$changes" >> "$GITHUB_ENV"
          fi

      - name: Create and Push Tag
        run: |
          # Ensure the latest tag is fetched and incremented correctly
          git fetch --tags
          latest_tag=$(git tag --sort=-v:refname | grep -E '^v[0-9]+\.[0-9]+\.[0-9]+$' | head -n 1)
          echo "Latest tag: $latest_tag"

          # Use the new version stored in the environment
          new_version="${{ env.new_version }}"
          echo "Creating new tag: $new_version"
          git tag -a "$new_version" -m "Release $new_version"
          git push origin "$new_version"

      - name: Create GitHub Release with Changelog
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ secrets.PAT }}
          tag_name: ${{ env.new_version }}  # Use the new version here
          name: "Release ${{ env.new_version }}"
          body: |
            ## What's Changed 🚀
            ${{ env.changelog }}
            ---
            **Full Changelog:** [Compare changes](https://github.com/${{ github.repository }}/compare/${{ env.latest_tag }}...${{ env.new_version }})
          draft: false
          prerelease: false
