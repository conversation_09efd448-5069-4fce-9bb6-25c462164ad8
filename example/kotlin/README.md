# IzyMercado Secure Push Notifications - Kotlin Multiplatform

This project demonstrates how to integrate with the IzyMercado order status secure push notification system using Kotlin Multiplatform (KMP). It provides real-time order status updates that are compatible with the existing React application.

## 🎯 Overview

This KMP implementation integrates with the same secure push notification system used by the React application and receives identical order status notifications. It demonstrates:

- **Real-time order status push notifications** (same as React app receives)
- **Cross-platform compatibility** (Android, iOS, JVM)
- **Secure FCM token management** (register, unregister, refresh logic)
- **Error handling and automatic retry**
- **Background/foreground notification handling** for mobile apps

## 🏗️ Project Structure

```
example/kotlin/
├── shared/                          # Shared KMP module
│   └── src/commonMain/kotlin/
│       ├── pushnotifications/       # Push notification implementation
│       │   ├── models/              # Data models matching React app
│       │   │   ├── OrderStatusNotification.kt
│       │   │   └── SecurePushModels.kt
│       │   ├── client/              # Secure push client
│       │   │   └── SecurePushClient.kt
│       │   └── OrderPushNotificationManager.kt
│       └── platform/                # Platform-specific implementations
├── androidApp/                      # Android application
│   └── src/main/kotlin/
│       ├── MainActivity.kt
│       ├── service/
│       │   └── OrderFirebaseMessagingService.kt
│       ├── viewmodel/
│       │   └── OrderPushNotificationViewModel.kt
│       └── ui/components/
│           ├── ConnectionStatusCard.kt
│           └── OrderUpdateCard.kt
├── iosApp/                          # iOS application
│   └── iosApp/
│       ├── ContentView.swift
│       └── OrderPushNotificationViewModel.swift
└── build.gradle.kts                 # Root build configuration
```

## 🚀 Quick Start

### Prerequisites

- **Kotlin 1.9.20+**
- **Android Studio** (for Android development)
- **Xcode 14+** (for iOS development)
- **JDK 11+**

### 1. Clone and Setup

```bash
cd example/kotlin
./gradlew build
```

### 2. Get Authentication Token

You need a valid authentication token from the IzyMercado API. This is the same token used in the React application.

### 3. Run Android App

```bash
./gradlew :androidApp:installDebug
```

### 4. Run iOS App

```bash
./gradlew :shared:embedAndSignAppleFrameworkForXcode
```

Then open `iosApp/iosApp.xcodeproj` in Xcode and run.

### 5. Run JVM Example

```bash
./gradlew :shared:run
```

## 🔔 Secure Push Notification Integration

### Backend API Integration

The push notification client integrates with secure backend endpoints:

- **VAPID Key**: `GET /v1/secure-push/vapid-key`
- **Token Generation**: `POST /v1/secure-push/generate`
- **Token Validation**: `POST /v1/secure-push/validate`
- **Token Revocation**: `DELETE /v1/secure-push/revoke`

### Authentication

Authentication uses the same token-based system as the React app:

```kotlin
// Create secure push manager
val manager = OrderPushNotificationManager.forDevelopment("your-auth-token", "android")
```

### Notification Format

The client handles the same notification types as the React application:

```kotlin
// Order status update
{
  "type": "invoice_status_update",
  "data": {
    "order_id": "12345678",
    "new_status": "preparing",
    "status_description": "Pedido em separação",
    "company_id": 123,
    // ... other fields
  },
  "timestamp": "2024-01-01T12:00:00Z"
}

// Connection established
{
  "type": "connection",
  "data": {
    "connection_id": "conn-123"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}

// Ping/Pong for keep-alive
{
  "type": "ping",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 📱 Usage Examples

### Basic Registration

```kotlin
// Create manager
val manager = OrderPushNotificationManager.forDevelopment("your-token", "android")

// Register for push notifications
manager.register(fcmToken, deviceInfo)

// Observe connection state
manager.connectionState.collect { state ->
    println("Registered: ${state.isRegistered}")
}

// Observe order updates
manager.orderUpdates.collect { notification ->
    println("Order ${notification.shortOrderId}: ${notification.statusDescription}")
}

// Start connection
manager.start()
```

### Partner-Specific Orders

```kotlin
// Filter orders for specific company (partner view)
val companyId = 123
manager.orderUpdatesForCompany(companyId).collect { notification ->
    println("Order for company $companyId: ${notification.getDisplayMessage()}")
}

// Filter for multiple companies
val companyIds = listOf(123, 456, 789)
manager.orderUpdatesForCompanies(companyIds).collect { notification ->
    // Handle order update
}
```

### New Orders vs Status Changes

```kotlin
// Only new orders
manager.newOrderUpdates().collect { notification ->
    println("🆕 New order: ${notification.orderId}")
}

// Only status changes
manager.statusChangeUpdates().collect { notification ->
    println("📦 Status change: ${notification.oldStatus} → ${notification.newStatus}")
}
```

### Error Handling

```kotlin
// Observe errors
manager.errors.collect { error ->
    println("Push notification error: $error")
}

// Manual re-registration
if (!manager.isRegistered) {
    manager.register(fcmToken, deviceInfo)
}
```

## 🔧 Configuration

### Environment Configuration

```kotlin
// Development environment
val devManager = OrderPushNotificationManager.forDevelopment("token", "android")

// Production environment
val prodManager = OrderPushNotificationManager.forProduction("token", "android")

// Custom configuration
val customConfig = SecurePushConfig(
    apiUrl = "https://custom.example.com",
    authToken = "token",
    platform = "android",
    maxRegistrationAttempts = 10,
    retryDelayMs = 5_000L
)
val customManager = OrderPushNotificationManager.withConfig(customConfig)
```

### Registration Management

```kotlin
val manager = OrderPushNotificationManager.forDevelopment("token", "android")

// Register for notifications
manager.register(fcmToken, deviceInfo)

// Unregister
manager.unregister()

// Validate token
val isValid = manager.validateToken()

// Cleanup resources
manager.cleanup()
```

## 📊 Data Models

### OrderStatusNotification

```kotlin
@Serializable
data class OrderStatusNotification(
    val orderId: String,
    val invoiceId: Int,
    val newStatus: String,
    val oldStatus: String? = null,
    val statusDescription: String,
    val message: String,
    val updatedBy: String,
    val updaterName: String? = null,
    val userId: Int,
    val companyId: Int,
    val companyName: String? = null,
    val timestamp: String
) {
    val shortOrderId: String get() = orderId.takeLast(8)
    val isNewOrder: Boolean get() = oldStatus.isNullOrBlank()
    
    fun getDisplayMessage(): String {
        return if (isNewOrder) {
            "🆕 Novo Pedido $shortOrderId - $statusDescription"
        } else {
            "📦 Pedido $shortOrderId - $statusDescription"
        }
    }
}
```

### SecurePushConnectionState

```kotlin
data class SecurePushConnectionState(
    val isRegistered: Boolean = false,
    val registrationAttempts: Int = 0,
    val lastError: String? = null,
    val pushToken: String? = null,
    val deviceId: String? = null,
    val lastRegistered: Long? = null
)
```

## 🔄 Compatibility with React App

This KMP implementation is fully compatible with the React application:

- ✅ **Same secure push notification system**
- ✅ **Same authentication method**
- ✅ **Same notification format**
- ✅ **Same backend API endpoints**
- ✅ **Same error handling**
- ✅ **Same token management**

## 🧪 Testing

### Unit Tests

```bash
./gradlew :shared:test
```

### Integration Tests

```bash
# Test with development server
./gradlew :shared:testDebugUnitTest

# Test with production server (requires valid token)
./gradlew :shared:testReleaseUnitTest
```

## 📚 Dependencies

- **Ktor Client**: HTTP client for backend API
- **Firebase Messaging**: FCM push notifications (Android)
- **Kotlinx Serialization**: JSON parsing
- **Kotlinx Coroutines**: Async programming
- **Kotlinx DateTime**: Date/time handling
- **Napier**: Logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions or issues:

1. Check the [troubleshooting guide](TROUBLESHOOTING.md)
2. Review the [API documentation](API.md)
3. Open an issue on GitHub
