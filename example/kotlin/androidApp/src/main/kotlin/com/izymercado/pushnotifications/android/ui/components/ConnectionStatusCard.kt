package com.izymercado.pushnotifications.android.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.izymercado.pushnotifications.models.SecurePushConnectionState
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun ConnectionStatusCard(
    connectionState: SecurePushConnectionState,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (connectionState.isRegistered) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Header
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = if (connectionState.isRegistered) "🟢" else "🔴",
                    style = MaterialTheme.typography.titleLarge
                )
                Text(
                    text = "Push Notification Status",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            // Status details
            StatusRow(
                label = "Status",
                value = if (connectionState.isRegistered) "Registered" else "Not Registered"
            )
            
            if (connectionState.registrationAttempts > 0) {
                StatusRow(
                    label = "Registration Attempts",
                    value = connectionState.registrationAttempts.toString()
                )
            }
            
            connectionState.pushToken?.let { token ->
                StatusRow(
                    label = "Push Token",
                    value = "...${token.takeLast(8)}"
                )
            }
            
            connectionState.deviceId?.let { deviceId ->
                StatusRow(
                    label = "Device ID",
                    value = "...${deviceId.takeLast(8)}"
                )
            }
            
            connectionState.lastRegistered?.let { timestamp ->
                val date = Date(timestamp * 1000)
                val formatter = SimpleDateFormat("MMM dd, HH:mm:ss", Locale.getDefault())
                StatusRow(
                    label = "Last Registered",
                    value = formatter.format(date)
                )
            }
            
            connectionState.lastError?.let { error ->
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "Last Error",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun StatusRow(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}
