package com.izymercado.pushnotifications.android.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.izymercado.pushnotifications.android.MainActivity
import com.izymercado.pushnotifications.android.R
import com.izymercado.pushnotifications.models.OrderStatusNotification
import io.github.aakira.napier.Napier

/**
 * Firebase Messaging Service for handling push notifications
 */
class OrderFirebaseMessagingService : FirebaseMessagingService() {
    
    companion object {
        private const val CHANNEL_ID = "order_notifications"
        private const val CHANNEL_NAME = "Order Notifications"
        private const val CHANNEL_DESCRIPTION = "Notifications for order status updates"
        
        // Callback for token refresh
        var onTokenRefresh: ((String) -> Unit)? = null
        
        // Callback for message received
        var onMessageReceived: ((Map<String, String>) -> Unit)? = null
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }
    
    /**
     * Called when a new FCM token is generated
     */
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        logDebug("New FCM token: ${token.take(20)}...")
        
        // Notify the app about token refresh
        onTokenRefresh?.invoke(token)
    }
    
    /**
     * Called when a message is received
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        
        logDebug("Message received from: ${remoteMessage.from}")
        
        // Handle data payload
        if (remoteMessage.data.isNotEmpty()) {
            logDebug("Message data payload: ${remoteMessage.data}")
            handleDataMessage(remoteMessage.data)
        }
        
        // Handle notification payload (if app is in foreground)
        remoteMessage.notification?.let { notification ->
            logDebug("Message notification: ${notification.title}")
            showNotification(
                title = notification.title ?: "IzyMercado",
                body = notification.body ?: "Nova notificação",
                data = remoteMessage.data
            )
        }
    }
    
    /**
     * Handle data message payload
     */
    private fun handleDataMessage(data: Map<String, String>) {
        try {
            // Notify the app about the message
            onMessageReceived?.invoke(data)
            
            // Handle order status updates
            if (data["type"] == "order_status_update") {
                val notification = OrderStatusNotification.fromFcmDataMap(data)
                if (notification != null) {
                    showOrderNotification(notification)
                } else {
                    logDebug("Failed to parse order notification")
                }
            }
            
        } catch (e: Exception) {
            logDebug("Error handling data message: ${e.message}")
        }
    }
    
    /**
     * Show notification for order status update
     */
    private fun showOrderNotification(notification: OrderStatusNotification) {
        val title = notification.getNotificationTitle()
        val body = notification.getNotificationBody()
        
        showNotification(
            title = title,
            body = body,
            data = notification.toFcmDataMap()
        )
    }
    
    /**
     * Show system notification
     */
    private fun showNotification(title: String, body: String, data: Map<String, String> = emptyMap()) {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            // Add notification data to intent
            data.forEach { (key, value) ->
                putExtra(key, value)
            }
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 
            0, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(body)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(System.currentTimeMillis().toInt(), notificationBuilder.build())
    }
    
    /**
     * Create notification channel for Android O and above
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = CHANNEL_DESCRIPTION
                enableLights(true)
                enableVibration(true)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * Log debug messages
     */
    private fun logDebug(message: String) {
        Napier.d("🔔 [FCMService] $message")
    }
}
