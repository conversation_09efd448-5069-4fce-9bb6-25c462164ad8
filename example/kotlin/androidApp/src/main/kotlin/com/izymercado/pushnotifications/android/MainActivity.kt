package com.izymercado.pushnotifications.android

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.izymercado.pushnotifications.android.service.OrderFirebaseMessagingService
import com.izymercado.pushnotifications.android.ui.theme.IzyMercadoPushTheme
import com.izymercado.pushnotifications.android.ui.components.ConnectionStatusCard
import com.izymercado.pushnotifications.android.ui.components.OrderUpdateCard
import com.izymercado.pushnotifications.android.viewmodel.OrderPushNotificationViewModel

class MainActivity : ComponentActivity() {
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            // Permission granted, notifications can be shown
        } else {
            // Permission denied, handle accordingly
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Request notification permission for Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.POST_NOTIFICATIONS
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
        
        setContent {
            IzyMercadoPushTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    OrderPushNotificationScreen()
                }
            }
        }
        
        // Handle notification data from intent
        handleNotificationIntent()
    }
    
    override fun onNewIntent(intent: android.content.Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleNotificationIntent()
    }
    
    private fun handleNotificationIntent() {
        intent?.extras?.let { extras ->
            val notificationData = mutableMapOf<String, String>()
            for (key in extras.keySet()) {
                extras.getString(key)?.let { value ->
                    notificationData[key] = value
                }
            }
            
            if (notificationData.isNotEmpty()) {
                // Handle notification tap
                // You can navigate to specific screens based on notification data
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderPushNotificationScreen(
    viewModel: OrderPushNotificationViewModel = viewModel()
) {
    val connectionState by viewModel.connectionState.collectAsState()
    val orderUpdates by viewModel.orderUpdates.collectAsState()
    val errors by viewModel.errors.collectAsState()
    
    var token by remember { mutableStateOf("") }
    var isProduction by remember { mutableStateOf(false) }
    
    // Set up FCM service callbacks
    LaunchedEffect(viewModel) {
        OrderFirebaseMessagingService.onTokenRefresh = { newToken ->
            viewModel.onTokenRefresh(newToken)
        }
        
        OrderFirebaseMessagingService.onMessageReceived = { data ->
            viewModel.handleNotification(data)
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Text(
            text = "IzyMercado Push Notifications",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        // Token input
        OutlinedTextField(
            value = token,
            onValueChange = { token = it },
            label = { Text("Authentication Token") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        // Environment selection
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = isProduction,
                onCheckedChange = { isProduction = it }
            )
            Text("Production Environment")
        }
        
        // Connection controls
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = { viewModel.register(token, isProduction) },
                enabled = token.isNotBlank() && !connectionState.isRegistered
            ) {
                Text("Register")
            }
            
            Button(
                onClick = { viewModel.unregister() },
                enabled = connectionState.isRegistered
            ) {
                Text("Unregister")
            }
            
            Button(
                onClick = { viewModel.validateToken() },
                enabled = connectionState.isRegistered
            ) {
                Text("Validate")
            }
        }
        
        // Connection status
        ConnectionStatusCard(connectionState)
        
        // Error display
        if (errors.isNotEmpty()) {
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Errors",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                    errors.takeLast(3).forEach { error ->
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
        
        // Order updates
        Text(
            text = "Order Updates (${orderUpdates.size})",
            style = MaterialTheme.typography.titleMedium
        )
        
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(orderUpdates.takeLast(10).reversed()) { order ->
                OrderUpdateCard(order)
            }
        }
    }
}
