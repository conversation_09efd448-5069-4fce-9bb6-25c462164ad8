package com.izymercado.pushnotifications.android.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.izymercado.pushnotifications.android.platform.AndroidPushNotificationManager
import com.izymercado.pushnotifications.models.OrderStatusNotification
import com.izymercado.pushnotifications.models.SecurePushConnectionState
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * ViewModel for managing push notification state in Android UI
 */
class OrderPushNotificationViewModel(application: Application) : AndroidViewModel(application) {
    
    private var pushManager: AndroidPushNotificationManager? = null
    
    // UI State flows
    private val _connectionState = MutableStateFlow(SecurePushConnectionState())
    val connectionState: StateFlow<SecurePushConnectionState> = _connectionState.asStateFlow()
    
    private val _orderUpdates = MutableStateFlow<List<OrderStatusNotification>>(emptyList())
    val orderUpdates: StateFlow<List<OrderStatusNotification>> = _orderUpdates.asStateFlow()
    
    private val _errors = MutableStateFlow<List<String>>(emptyList())
    val errors: StateFlow<List<String>> = _errors.asStateFlow()
    
    /**
     * Register for push notifications
     */
    fun register(token: String, isProduction: Boolean = false) {
        if (token.isBlank()) {
            addError("Authentication token is required")
            return
        }
        
        viewModelScope.launch {
            try {
                // Clean up existing manager
                pushManager?.cleanup()
                
                // Create new manager
                pushManager = if (isProduction) {
                    AndroidPushNotificationManager.forProduction(getApplication(), token)
                } else {
                    AndroidPushNotificationManager.forDevelopment(getApplication(), token)
                }
                
                // Observe state changes
                observeManagerState()
                
                // Initialize and register
                pushManager?.initializeAndRegister()
                
            } catch (e: Exception) {
                addError("Registration failed: ${e.message}")
            }
        }
    }
    
    /**
     * Unregister from push notifications
     */
    fun unregister() {
        viewModelScope.launch {
            try {
                pushManager?.unregister()
            } catch (e: Exception) {
                addError("Unregistration failed: ${e.message}")
            }
        }
    }
    
    /**
     * Validate current push token
     */
    fun validateToken() {
        viewModelScope.launch {
            try {
                val isValid = pushManager?.validateToken() ?: false
                if (isValid) {
                    addError("Token is valid")
                } else {
                    addError("Token is invalid or expired")
                }
            } catch (e: Exception) {
                addError("Token validation failed: ${e.message}")
            }
        }
    }
    
    /**
     * Handle FCM token refresh
     */
    fun onTokenRefresh(newToken: String) {
        viewModelScope.launch {
            try {
                (pushManager as? AndroidPushNotificationManager)?.onTokenRefresh(newToken)
            } catch (e: Exception) {
                addError("Token refresh failed: ${e.message}")
            }
        }
    }
    
    /**
     * Handle incoming notification
     */
    fun handleNotification(data: Map<String, String>) {
        viewModelScope.launch {
            try {
                pushManager?.handleNotification(data)
            } catch (e: Exception) {
                addError("Notification handling failed: ${e.message}")
            }
        }
    }
    
    /**
     * Observe manager state changes
     */
    private fun observeManagerState() {
        pushManager?.let { manager ->
            // Observe connection state
            viewModelScope.launch {
                manager.connectionState.collect { state ->
                    _connectionState.value = state
                }
            }
            
            // Observe order updates
            viewModelScope.launch {
                manager.orderUpdates.collect { notification ->
                    val currentList = _orderUpdates.value.toMutableList()
                    currentList.add(notification)
                    _orderUpdates.value = currentList
                }
            }
            
            // Observe errors
            viewModelScope.launch {
                manager.errors.collect { error ->
                    addError(error)
                }
            }
        }
    }
    
    /**
     * Add error to the error list
     */
    private fun addError(error: String) {
        val currentErrors = _errors.value.toMutableList()
        currentErrors.add(error)
        // Keep only last 10 errors
        if (currentErrors.size > 10) {
            currentErrors.removeAt(0)
        }
        _errors.value = currentErrors
    }
    
    /**
     * Clear all errors
     */
    fun clearErrors() {
        _errors.value = emptyList()
    }
    
    /**
     * Clear all order updates
     */
    fun clearOrderUpdates() {
        _orderUpdates.value = emptyList()
    }
    
    /**
     * Get orders for specific company
     */
    fun getOrdersForCompany(companyId: Int): List<OrderStatusNotification> {
        return _orderUpdates.value.filter { it.companyId == companyId }
    }
    
    /**
     * Get only new orders
     */
    fun getNewOrders(): List<OrderStatusNotification> {
        return _orderUpdates.value.filter { it.isNewOrder }
    }
    
    /**
     * Get only status updates
     */
    fun getStatusUpdates(): List<OrderStatusNotification> {
        return _orderUpdates.value.filter { !it.isNewOrder }
    }
    
    override fun onCleared() {
        super.onCleared()
        pushManager?.cleanup()
    }
}
