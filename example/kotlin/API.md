# API Documentation

Complete API reference for the IzyMercado WebSocket KMP integration.

## 🏗️ Core Classes

### OrderWebSocketManager

The main entry point for WebSocket integration.

```kotlin
class OrderWebSocketManager(private val config: WebSocketConfig)
```

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `connectionState` | `StateFlow<WebSocketConnectionState>` | Current connection state |
| `orderUpdates` | `SharedFlow<OrderStatusNotification>` | Stream of order updates |
| `errors` | `SharedFlow<String>` | Stream of error messages |
| `isConnected` | `Boolean` | Current connection status |
| `reconnectAttempts` | `Int` | Number of reconnection attempts |
| `lastError` | `String?` | Last error message |
| `connectionId` | `String?` | Server-assigned connection ID |

#### Methods

##### start()
```kotlin
fun start()
```
Starts the WebSocket connection.

##### stop()
```kotlin
fun stop()
```
Stops the WebSocket connection.

##### reconnect()
```kotlin
fun reconnect()
```
Manually triggers a reconnection.

##### orderUpdatesForCompany(companyId: Int)
```kotlin
fun orderUpdatesForCompany(companyId: Int): Flow<OrderStatusNotification>
```
Filters order updates for a specific company.

**Parameters:**
- `companyId`: The company ID to filter by

**Returns:** Flow of order notifications for the specified company

##### orderUpdatesForCompanies(companyIds: List<Int>)
```kotlin
fun orderUpdatesForCompanies(companyIds: List<Int>): Flow<OrderStatusNotification>
```
Filters order updates for multiple companies.

**Parameters:**
- `companyIds`: List of company IDs to filter by

**Returns:** Flow of order notifications for the specified companies

##### newOrderUpdates()
```kotlin
fun newOrderUpdates(): Flow<OrderStatusNotification>
```
Filters for new order notifications only.

**Returns:** Flow of new order notifications (where `oldStatus` is null)

##### statusChangeUpdates()
```kotlin
fun statusChangeUpdates(): Flow<OrderStatusNotification>
```
Filters for status change notifications only.

**Returns:** Flow of status change notifications (where `oldStatus` is not null)

##### cleanup()
```kotlin
fun cleanup()
```
Cleans up all resources and closes connections.

#### Companion Object

##### forDevelopment(token: String)
```kotlin
fun forDevelopment(token: String): OrderWebSocketManager
```
Creates a manager configured for development environment.

##### forProduction(token: String)
```kotlin
fun forProduction(token: String): OrderWebSocketManager
```
Creates a manager configured for production environment.

##### withConfig(config: WebSocketConfig)
```kotlin
fun withConfig(config: WebSocketConfig): OrderWebSocketManager
```
Creates a manager with custom configuration.

---

### WebSocketConfig

Configuration for WebSocket connection parameters.

```kotlin
data class WebSocketConfig(
    val wsUrl: String,
    val token: String,
    val maxReconnectAttempts: Int = 5,
    val pingIntervalMs: Long = 30_000L,
    val reconnectDelayMs: Long = 2_000L,
    val enabled: Boolean = true
)
```

#### Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `wsUrl` | `String` | - | WebSocket server URL |
| `token` | `String` | - | Authentication token |
| `maxReconnectAttempts` | `Int` | `5` | Maximum reconnection attempts |
| `pingIntervalMs` | `Long` | `30000` | Ping interval in milliseconds |
| `reconnectDelayMs` | `Long` | `2000` | Base delay between reconnection attempts |
| `enabled` | `Boolean` | `true` | Whether connection is enabled |

#### Methods

##### getConnectionUrl()
```kotlin
fun getConnectionUrl(): String
```
Returns the complete WebSocket URL with token parameter.

##### getReconnectDelay(attempt: Int)
```kotlin
fun getReconnectDelay(attempt: Int): Long
```
Calculates reconnection delay with linear backoff.

#### Companion Object

##### development(token: String)
```kotlin
fun development(token: String): WebSocketConfig
```
Creates development configuration.

**Default URL:** `ws://localhost:8080/v1/ws/connect`

##### production(token: String)
```kotlin
fun production(token: String): WebSocketConfig
```
Creates production configuration.

**Default URL:** `wss://api.izymercado.com.br/v1/ws/connect`

---

## 📊 Data Models

### OrderStatusNotification

Represents an order status update notification.

```kotlin
@Serializable
data class OrderStatusNotification(
    @SerialName("order_id") val orderId: String,
    @SerialName("invoice_id") val invoiceId: Int,
    @SerialName("new_status") val newStatus: String,
    @SerialName("old_status") val oldStatus: String? = null,
    @SerialName("status_description") val statusDescription: String,
    @SerialName("message") val message: String,
    @SerialName("updated_by") val updatedBy: String,
    @SerialName("updater_name") val updaterName: String? = null,
    @SerialName("user_id") val userId: Int,
    @SerialName("company_id") val companyId: Int,
    @SerialName("company_name") val companyName: String? = null,
    @SerialName("timestamp") val timestamp: String
)
```

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `orderId` | `String` | Unique order identifier |
| `invoiceId` | `Int` | Invoice ID associated with the order |
| `newStatus` | `String` | New order status |
| `oldStatus` | `String?` | Previous order status (null for new orders) |
| `statusDescription` | `String` | Human-readable status description |
| `message` | `String` | Additional message about the status change |
| `updatedBy` | `String` | User ID who updated the status |
| `updaterName` | `String?` | Name of the user who updated the status |
| `userId` | `Int` | User ID associated with the update |
| `companyId` | `Int` | Company ID that owns the order |
| `companyName` | `String?` | Company name |
| `timestamp` | `String` | ISO 8601 timestamp of the update |

#### Computed Properties

##### shortOrderId
```kotlin
val shortOrderId: String
```
Returns the last 8 characters of the order ID.

##### isNewOrder
```kotlin
val isNewOrder: Boolean
```
Returns `true` if this is a new order notification (oldStatus is null).

#### Methods

##### getDisplayMessage()
```kotlin
fun getDisplayMessage(): String
```
Returns a formatted display message for notifications.

**Example output:**
- New order: `"🆕 Novo Pedido 12345678 - Pedido confirmado"`
- Status change: `"📦 Pedido 12345678 - Em preparação"`

---

### WebSocketConnectionState

Represents the current state of the WebSocket connection.

```kotlin
data class WebSocketConnectionState(
    val isConnected: Boolean = false,
    val reconnectAttempts: Int = 0,
    val lastError: String? = null,
    val connectionId: String? = null
)
```

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `isConnected` | `Boolean` | Whether the WebSocket is currently connected |
| `reconnectAttempts` | `Int` | Number of reconnection attempts made |
| `lastError` | `String?` | Last error message, if any |
| `connectionId` | `String?` | Server-assigned connection identifier |

---

### WebSocketMessage

Internal message format for WebSocket communication.

```kotlin
@Serializable
data class WebSocketMessage(
    @SerialName("type") val type: String,
    @SerialName("action") val action: String? = null,
    @SerialName("data") val data: JsonElement? = null,
    @SerialName("timestamp") val timestamp: String,
    @SerialName("user_id") val userId: Int? = null,
    @SerialName("company_id") val companyId: Int? = null
)
```

#### Message Types

| Type | Description |
|------|-------------|
| `ping` | Keep-alive ping message |
| `pong` | Keep-alive pong response |
| `connection` | Connection establishment confirmation |
| `error` | Error message from server |
| `invoice_status_update` | Order status update notification |

---

## 🔄 Flow Types

### StateFlow<T>

Represents a state that can be observed for changes.

```kotlin
// Collect state changes
manager.connectionState.collect { state ->
    println("Connection: ${state.isConnected}")
}

// Get current value
val currentState = manager.connectionState.value
```

### SharedFlow<T>

Represents a stream of events that can be observed.

```kotlin
// Collect all events
manager.orderUpdates.collect { notification ->
    println("Order update: ${notification.orderId}")
}

// Collect with filtering
manager.orderUpdates
    .filter { it.companyId == 123 }
    .collect { notification ->
        println("Company 123 order: ${notification.orderId}")
    }
```

---

## 🔧 Error Handling

### Common Error Types

| Error | Description | Solution |
|-------|-------------|----------|
| `"Failed to establish WebSocket connection"` | Network or authentication issue | Check token and network connectivity |
| `"Authentication failed"` | Invalid or expired token | Refresh authentication token |
| `"Failed to parse server message"` | Malformed JSON from server | Check server compatibility |
| `"Connection closed"` | Server closed the connection | Automatic reconnection will be attempted |
| `"Max reconnection attempts reached"` | Too many failed reconnection attempts | Manual reconnection or check server status |

### Error Handling Pattern

```kotlin
// Observe errors
manager.errors.collect { error ->
    when {
        error.contains("authentication") -> {
            // Handle authentication errors
            refreshToken()
        }
        error.contains("network") -> {
            // Handle network errors
            checkNetworkConnectivity()
        }
        else -> {
            // Handle other errors
            logError(error)
        }
    }
}
```

---

## 🧪 Testing

### Unit Testing

```kotlin
@Test
fun testOrderNotificationParsing() {
    val json = """
        {
            "order_id": "12345678",
            "invoice_id": 123,
            "new_status": "preparing",
            "status_description": "Order is being prepared",
            "message": "Your order is now being prepared",
            "updated_by": "user123",
            "user_id": 456,
            "company_id": 789,
            "timestamp": "2024-01-01T12:00:00Z"
        }
    """
    
    val notification = Json.decodeFromString<OrderStatusNotification>(json)
    
    assertEquals("12345678", notification.orderId)
    assertEquals("preparing", notification.newStatus)
    assertTrue(notification.isNewOrder) // oldStatus is null
}
```

### Integration Testing

```kotlin
@Test
suspend fun testWebSocketConnection() {
    val config = WebSocketConfig.development("test-token")
    val manager = OrderWebSocketManager.withConfig(config)
    
    var connected = false
    
    // Test connection
    launch {
        manager.connectionState.collect { state ->
            connected = state.isConnected
        }
    }
    
    manager.start()
    delay(5000) // Wait for connection
    
    assertTrue(connected)
    
    manager.cleanup()
}
```
