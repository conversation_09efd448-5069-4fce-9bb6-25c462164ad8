package com.izymercado.pushnotifications.client

import com.izymercado.pushnotifications.models.*
import io.github.aakira.napier.Napier
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.datetime.Clock
import kotlinx.serialization.json.Json

/**
 * Secure push notification client that handles registration and token management
 * Matches the functionality of the React application's useOrderPushNotifications hook
 */
class SecurePushClient(
    private val httpClient: HttpClient,
    private val config: SecurePushConfig
) {
    private var registrationJob: Job? = null
    private var registrationAttempts = 0
    
    private val _connectionState = MutableStateFlow(SecurePushConnectionState())
    val connectionState: StateFlow<SecurePushConnectionState> = _connectionState.asStateFlow()
    
    private val _orderUpdates = MutableSharedFlow<OrderStatusNotification>()
    val orderUpdates: SharedFlow<OrderStatusNotification> = _orderUpdates.asSharedFlow()
    
    private val _errors = MutableSharedFlow<String>()
    val errors: SharedFlow<String> = _errors.asSharedFlow()
    
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    /**
     * Generate device ID for this installation
     */
    private fun generateDeviceId(): String {
        // In a real implementation, this should be stored persistently
        return "${config.platform}_${kotlin.random.Random.nextLong()}_${Clock.System.now().epochSeconds}"
    }
    
    /**
     * Register FCM token with the backend
     */
    suspend fun register(fcmToken: String, deviceInfo: DeviceInfo? = null) {
        if (!config.enabled) {
            logDebug("Push notifications disabled")
            return
        }
        
        if (fcmToken.isBlank()) {
            logDebug("FCM token is empty")
            _errors.emit("FCM token is required for registration")
            return
        }
        
        try {
            logDebug("Starting secure push notification registration")
            
            val deviceId = generateDeviceId()
            val request = SecurePushTokenRequest(
                deviceId = deviceId,
                platform = config.platform,
                appVersion = config.appVersion,
                fcmToken = fcmToken,
                deviceModel = deviceInfo?.deviceModel,
                osVersion = deviceInfo?.osVersion
            )
            
            val response = httpClient.post("${config.apiUrl}/v1/secure-push/generate") {
                contentType(ContentType.Application.Json)
                header("Authorization", "Bearer ${config.authToken}")
                setBody(request)
            }
            
            if (response.status.isSuccess()) {
                val tokenResponse = response.body<SecurePushTokenResponse>()
                
                _connectionState.value = _connectionState.value.copy(
                    isRegistered = true,
                    registrationAttempts = registrationAttempts,
                    lastError = null,
                    pushToken = tokenResponse.data.pushToken,
                    deviceId = tokenResponse.data.deviceId,
                    lastRegistered = Clock.System.now().epochSeconds
                )
                
                registrationAttempts = 0
                logDebug("Push notification registration successful")
                
            } else {
                val errorText = response.bodyAsText()
                val errorMessage = try {
                    val errorResponse = json.decodeFromString<ApiErrorResponse>(errorText)
                    errorResponse.error
                } catch (e: Exception) {
                    "Registration failed: ${response.status}"
                }
                
                throw Exception(errorMessage)
            }
            
        } catch (e: Exception) {
            val errorMessage = "Registration failed: ${e.message}"
            logDebug(errorMessage)
            
            _connectionState.value = _connectionState.value.copy(
                isRegistered = false,
                registrationAttempts = registrationAttempts,
                lastError = errorMessage
            )
            
            _errors.emit(errorMessage)
            
            // Schedule retry if we haven't exceeded max attempts
            if (registrationAttempts < config.maxRegistrationAttempts) {
                scheduleRegistrationRetry(fcmToken, deviceInfo)
            }
        }
    }
    
    /**
     * Validate current push token
     */
    suspend fun validateToken(): Boolean {
        val pushToken = _connectionState.value.pushToken ?: return false
        
        return try {
            val request = PushTokenValidationRequest(pushToken = pushToken)
            
            val response = httpClient.post("${config.apiUrl}/v1/secure-push/validate") {
                contentType(ContentType.Application.Json)
                header("Authorization", "Bearer ${config.authToken}")
                setBody(request)
            }
            
            if (response.status.isSuccess()) {
                val validationResponse = response.body<PushTokenValidationResponse>()
                logDebug("Token validation result: ${validationResponse.data.valid}")
                validationResponse.data.valid
            } else {
                logDebug("Token validation failed: ${response.status}")
                false
            }
            
        } catch (e: Exception) {
            logDebug("Token validation error: ${e.message}")
            false
        }
    }
    
    /**
     * Revoke current push token
     */
    suspend fun revoke() {
        val pushToken = _connectionState.value.pushToken ?: return
        
        try {
            val request = PushTokenRevocationRequest(pushToken = pushToken)
            
            val response = httpClient.delete("${config.apiUrl}/v1/secure-push/revoke") {
                contentType(ContentType.Application.Json)
                header("Authorization", "Bearer ${config.authToken}")
                setBody(request)
            }
            
            if (response.status.isSuccess()) {
                logDebug("Push token revoked successfully")
            } else {
                logDebug("Token revocation failed: ${response.status}")
            }
            
        } catch (e: Exception) {
            logDebug("Token revocation error: ${e.message}")
        } finally {
            // Reset state regardless of API call result
            _connectionState.value = SecurePushConnectionState()
            registrationAttempts = config.maxRegistrationAttempts // Prevent re-registration
        }
    }
    
    /**
     * Handle incoming push notification
     */
    suspend fun handleNotification(data: Map<String, String>) {
        try {
            logDebug("Handling push notification: ${data["type"]}")
            
            if (data["type"] == "order_status_update") {
                val notification = OrderStatusNotification.fromFcmDataMap(data)
                if (notification != null) {
                    logDebug("Order status update: ${notification.orderId} -> ${notification.newStatus}")
                    _orderUpdates.emit(notification)
                } else {
                    logDebug("Failed to parse order status notification")
                    _errors.emit("Failed to parse notification data")
                }
            } else {
                logDebug("Unknown notification type: ${data["type"]}")
            }
            
        } catch (e: Exception) {
            logDebug("Error handling notification: ${e.message}")
            _errors.emit("Error processing notification: ${e.message}")
        }
    }
    
    /**
     * Schedule registration retry with exponential backoff
     */
    private fun scheduleRegistrationRetry(fcmToken: String, deviceInfo: DeviceInfo?) {
        registrationAttempts++
        val delay = config.retryDelayMs * registrationAttempts
        
        logDebug("Scheduling registration retry $registrationAttempts/${config.maxRegistrationAttempts} in ${delay}ms")
        
        _connectionState.value = _connectionState.value.copy(
            registrationAttempts = registrationAttempts
        )
        
        registrationJob?.cancel()
        registrationJob = CoroutineScope(Dispatchers.Default).launch {
            delay(delay)
            if (isActive) {
                register(fcmToken, deviceInfo)
            }
        }
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        registrationJob?.cancel()
    }
    
    /**
     * Log debug messages
     */
    private fun logDebug(message: String) {
        Napier.d("🔔 [SecurePush] $message")
    }
}
