package com.izymercado.pushnotifications

import com.izymercado.pushnotifications.client.SecurePushClient
import com.izymercado.pushnotifications.models.*
import io.ktor.client.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.serialization.json.Json

/**
 * High-level manager for order push notifications
 * Provides a simple API that matches the React application's useOrderPushNotifications hook
 * Secure FCM implementation for order push notifications
 */
class OrderPushNotificationManager(
    private val config: SecurePushConfig
) {
    private val httpClient = HttpClient {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
            })
        }
    }

    private val pushClient = SecurePushClient(httpClient, config)
    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // Public state flows
    val connectionState: StateFlow<SecurePushConnectionState> = pushClient.connectionState
    val orderUpdates: SharedFlow<OrderStatusNotification> = pushClient.orderUpdates
    val errors: SharedFlow<String> = pushClient.errors
    
    // Convenience properties
    val isRegistered: Boolean
        get() = connectionState.value.isRegistered

    val registrationAttempts: Int
        get() = connectionState.value.registrationAttempts

    val lastError: String?
        get() = connectionState.value.lastError

    val pushToken: String?
        get() = connectionState.value.pushToken

    val deviceId: String?
        get() = connectionState.value.deviceId
    
    /**
     * Register FCM token with the server
     */
    fun register(fcmToken: String, deviceInfo: DeviceInfo? = null) {
        scope.launch {
            pushClient.register(fcmToken, deviceInfo)
        }
    }

    /**
     * Unregister push token from the server
     */
    fun unregister() {
        scope.launch {
            pushClient.revoke()
        }
    }

    /**
     * Validate current push token
     */
    suspend fun validateToken(): Boolean {
        return pushClient.validateToken()
    }

    /**
     * Handle incoming push notification
     */
    fun handleNotification(data: Map<String, String>) {
        scope.launch {
            pushClient.handleNotification(data)
        }
    }
    
    /**
     * Filter order updates by company ID
     * Useful for partner users who should only see orders for their companies
     */
    fun orderUpdatesForCompany(companyId: Int): Flow<OrderStatusNotification> {
        return orderUpdates.filter { it.companyId == companyId }
    }
    
    /**
     * Filter order updates by multiple company IDs
     * Useful for partner users with access to multiple companies
     */
    fun orderUpdatesForCompanies(companyIds: List<Int>): Flow<OrderStatusNotification> {
        return orderUpdates.filter { it.companyId in companyIds }
    }
    
    /**
     * Get only new order notifications (where oldStatus is null)
     */
    fun newOrderUpdates(): Flow<OrderStatusNotification> {
        return orderUpdates.filter { it.isNewOrder }
    }
    
    /**
     * Get only status change notifications (where oldStatus is not null)
     */
    fun statusChangeUpdates(): Flow<OrderStatusNotification> {
        return orderUpdates.filter { !it.isNewOrder }
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        pushClient.cleanup()
        scope.cancel()
        httpClient.close()
    }

    // Convenience methods for easy API usage
    fun start(fcmToken: String, deviceInfo: DeviceInfo? = null) = register(fcmToken, deviceInfo)
    fun stop() = unregister()
    fun reconnect(fcmToken: String, deviceInfo: DeviceInfo? = null) = register(fcmToken, deviceInfo)
    val isConnected: Boolean get() = isRegistered
    val reconnectAttempts: Int get() = registrationAttempts
    val connectionId: String? get() = pushToken

    companion object {
        /**
         * Create a manager for development environment
         */
        fun forDevelopment(token: String, platform: String = "android"): OrderPushNotificationManager {
            return OrderPushNotificationManager(SecurePushConfig.development(token, platform))
        }

        /**
         * Create a manager for production environment
         */
        fun forProduction(token: String, platform: String = "android"): OrderPushNotificationManager {
            return OrderPushNotificationManager(SecurePushConfig.production(token, platform))
        }

        /**
         * Create a manager with custom configuration
         */
        fun withConfig(config: SecurePushConfig): OrderPushNotificationManager {
            return OrderPushNotificationManager(config)
        }
    }
}
