package com.izymercado.pushnotifications.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Order status notification data model that matches the TypeScript interface
 * from the React application's useOrderPushNotifications.ts
 */
@Serializable
data class OrderStatusNotification(
    @SerialName("order_id")
    val orderId: String,
    
    @SerialName("invoice_id")
    val invoiceId: Int,
    
    @SerialName("new_status")
    val newStatus: String,
    
    @SerialName("old_status")
    val oldStatus: String? = null,
    
    @SerialName("status_description")
    val statusDescription: String,
    
    @SerialName("message")
    val message: String,
    
    @SerialName("updated_by")
    val updatedBy: String,
    
    @SerialName("updater_name")
    val updaterName: String? = null,
    
    @SerialName("user_id")
    val userId: Int,
    
    @SerialName("company_id")
    val companyId: Int,
    
    @SerialName("company_name")
    val companyName: String? = null,
    
    @SerialName("timestamp")
    val timestamp: String
) {
    /**
     * Convenience property to get the last 8 characters of order ID
     * (commonly used for display purposes)
     */
    val shortOrderId: String
        get() = orderId.takeLast(8)
    
    /**
     * Check if this is a new order notification
     * (typically when old_status is null or empty)
     */
    val isNewOrder: Boolean
        get() = oldStatus.isNullOrBlank()
    
    /**
     * Get a formatted display message for notifications
     */
    fun getDisplayMessage(): String {
        return if (isNewOrder) {
            "🆕 Novo Pedido $shortOrderId - $statusDescription"
        } else {
            "📦 Pedido $shortOrderId - $statusDescription"
        }
    }
    
    /**
     * Get notification title based on order type
     */
    fun getNotificationTitle(): String {
        return if (isNewOrder) {
            "🆕 Novo Pedido"
        } else {
            "📦 Status Atualizado"
        }
    }
    
    /**
     * Get notification body text
     */
    fun getNotificationBody(): String {
        return "Pedido $shortOrderId - $statusDescription"
    }
    
    /**
     * Convert to map for FCM data payload
     */
    fun toFcmDataMap(): Map<String, String> {
        return mapOf(
            "type" to "order_status_update",
            "order_id" to orderId,
            "invoice_id" to invoiceId.toString(),
            "new_status" to newStatus,
            "old_status" to (oldStatus ?: ""),
            "status_description" to statusDescription,
            "message" to message,
            "updated_by" to updatedBy,
            "updater_name" to (updaterName ?: ""),
            "user_id" to userId.toString(),
            "company_id" to companyId.toString(),
            "company_name" to (companyName ?: ""),
            "timestamp" to timestamp
        )
    }
    
    companion object {
        /**
         * Create OrderStatusNotification from FCM data payload
         */
        fun fromFcmDataMap(data: Map<String, String>): OrderStatusNotification? {
            return try {
                OrderStatusNotification(
                    orderId = data["order_id"] ?: return null,
                    invoiceId = data["invoice_id"]?.toIntOrNull() ?: return null,
                    newStatus = data["new_status"] ?: return null,
                    oldStatus = data["old_status"]?.takeIf { it.isNotBlank() },
                    statusDescription = data["status_description"] ?: return null,
                    message = data["message"] ?: return null,
                    updatedBy = data["updated_by"] ?: return null,
                    updaterName = data["updater_name"]?.takeIf { it.isNotBlank() },
                    userId = data["user_id"]?.toIntOrNull() ?: return null,
                    companyId = data["company_id"]?.toIntOrNull() ?: return null,
                    companyName = data["company_name"]?.takeIf { it.isNotBlank() },
                    timestamp = data["timestamp"] ?: return null
                )
            } catch (e: Exception) {
                null
            }
        }
    }
}
