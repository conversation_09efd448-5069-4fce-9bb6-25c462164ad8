package com.izymercado.pushnotifications.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Configuration for secure push notifications
 */
@Serializable
data class SecurePushConfig(
    val apiUrl: String,
    val authToken: String,
    val platform: String, // "android" or "ios"
    val appVersion: String = "1.0.0",
    val enabled: Boolean = true,
    val maxRegistrationAttempts: Int = 3,
    val retryDelayMs: Long = 5000L
) {
    companion object {
        fun development(authToken: String, platform: String): SecurePushConfig {
            return SecurePushConfig(
                apiUrl = "http://localhost:8080",
                authToken = authToken,
                platform = platform
            )
        }

        fun production(authToken: String, platform: String): SecurePushConfig {
            return SecurePushConfig(
                apiUrl = "https://api.izymercado.com.br",
                authToken = authToken,
                platform = platform
            )
        }
    }
}

/**
 * Device information for push notification registration
 */
@Serializable
data class DeviceInfo(
    @SerialName("device_model")
    val deviceModel: String,
    
    @SerialName("os_version")
    val osVersion: String,
    
    @SerialName("app_version")
    val appVersion: String = "1.0.0"
)

/**
 * Request payload for secure push token generation
 */
@Serializable
data class SecurePushTokenRequest(
    @SerialName("device_id")
    val deviceId: String,
    
    @SerialName("platform")
    val platform: String,
    
    @SerialName("app_version")
    val appVersion: String,
    
    @SerialName("fcm_token")
    val fcmToken: String,
    
    @SerialName("device_model")
    val deviceModel: String? = null,
    
    @SerialName("os_version")
    val osVersion: String? = null
)

/**
 * Response from secure push token generation
 */
@Serializable
data class SecurePushTokenResponse(
    @SerialName("data")
    val data: SecurePushTokenData
)

@Serializable
data class SecurePushTokenData(
    @SerialName("push_token")
    val pushToken: String,
    
    @SerialName("device_id")
    val deviceId: String,
    
    @SerialName("platform")
    val platform: String,
    
    @SerialName("expires_at")
    val expiresAt: String
)

/**
 * Request for push token validation
 */
@Serializable
data class PushTokenValidationRequest(
    @SerialName("push_token")
    val pushToken: String
)

/**
 * Response from push token validation
 */
@Serializable
data class PushTokenValidationResponse(
    @SerialName("data")
    val data: PushTokenValidationData
)

@Serializable
data class PushTokenValidationData(
    @SerialName("valid")
    val valid: Boolean,
    
    @SerialName("expires_at")
    val expiresAt: String? = null
)

/**
 * Request for push token revocation
 */
@Serializable
data class PushTokenRevocationRequest(
    @SerialName("push_token")
    val pushToken: String
)

/**
 * Response from push token revocation
 */
@Serializable
data class PushTokenRevocationResponse(
    @SerialName("data")
    val data: PushTokenRevocationData
)

@Serializable
data class PushTokenRevocationData(
    @SerialName("revoked")
    val revoked: Boolean,
    
    @SerialName("message")
    val message: String
)

/**
 * Connection state for secure push notifications
 */
data class SecurePushConnectionState(
    val isRegistered: Boolean = false,
    val registrationAttempts: Int = 0,
    val lastError: String? = null,
    val pushToken: String? = null,
    val deviceId: String? = null,
    val lastRegistered: Long? = null
)

/**
 * Error response from API
 */
@Serializable
data class ApiErrorResponse(
    @SerialName("error")
    val error: String,
    
    @SerialName("code")
    val code: String? = null,
    
    @SerialName("details")
    val details: String? = null
)
