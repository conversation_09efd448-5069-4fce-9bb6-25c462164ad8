package com.izymercado.pushnotifications.platform

import android.content.Context
import com.google.firebase.messaging.FirebaseMessaging
import com.izymercado.pushnotifications.OrderPushNotificationManager
import com.izymercado.pushnotifications.models.DeviceInfo
import com.izymercado.pushnotifications.models.SecurePushConfig
import io.github.aakira.napier.Napier
import kotlinx.coroutines.tasks.await

/**
 * Android-specific push notification manager that integrates with Firebase Cloud Messaging
 */
class AndroidPushNotificationManager(
    private val context: Context,
    config: SecurePushConfig
) : OrderPushNotificationManager(config) {
    
    /**
     * Initialize FCM and register for push notifications
     */
    suspend fun initializeAndRegister() {
        try {
            logDebug("Initializing Firebase Cloud Messaging")
            
            // Get FCM token
            val fcmToken = FirebaseMessaging.getInstance().token.await()
            logDebug("FCM token obtained: ${fcmToken.take(20)}...")
            
            // Get device info
            val deviceInfo = getDeviceInfo()
            
            // Register with backend
            register(fcmToken, deviceInfo)
            
        } catch (e: Exception) {
            logDebug("Failed to initialize FCM: ${e.message}")
            throw e
        }
    }
    
    /**
     * Get device information for registration
     */
    private fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            deviceModel = "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}",
            osVersion = "Android ${android.os.Build.VERSION.RELEASE}",
            appVersion = getAppVersion()
        )
    }
    
    /**
     * Get app version from package manager
     */
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0.0"
        } catch (e: Exception) {
            "1.0.0"
        }
    }
    
    /**
     * Handle FCM token refresh
     */
    suspend fun onTokenRefresh(newToken: String) {
        logDebug("FCM token refreshed: ${newToken.take(20)}...")
        val deviceInfo = getDeviceInfo()
        register(newToken, deviceInfo)
    }
    
    /**
     * Log debug messages
     */
    private fun logDebug(message: String) {
        Napier.d("🔔 [AndroidPush] $message")
    }
    
    companion object {
        /**
         * Create Android push manager for development
         */
        fun forDevelopment(context: Context, authToken: String): AndroidPushNotificationManager {
            val config = SecurePushConfig.development(authToken, "android")
            return AndroidPushNotificationManager(context, config)
        }
        
        /**
         * Create Android push manager for production
         */
        fun forProduction(context: Context, authToken: String): AndroidPushNotificationManager {
            val config = SecurePushConfig.production(authToken, "android")
            return AndroidPushNotificationManager(context, config)
        }
    }
}
