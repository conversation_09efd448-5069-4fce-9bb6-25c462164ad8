package com.izymercado.pushnotifications.platform

import com.izymercado.pushnotifications.OrderPushNotificationManager
import com.izymercado.pushnotifications.models.DeviceInfo
import com.izymercado.pushnotifications.models.SecurePushConfig
import io.github.aakira.napier.Napier
import platform.Foundation.NSBundle
import platform.UIKit.UIDevice

/**
 * iOS-specific push notification manager that integrates with APNs/FCM
 * Note: This requires additional iOS-specific FCM setup in the iOS app
 */
class IosPushNotificationManager(
    config: SecurePushConfig
) : OrderPushNotificationManager(config) {
    
    /**
     * Initialize and register for push notifications
     * Note: FCM token should be obtained from iOS-specific Firebase setup
     */
    fun initializeAndRegister(fcmToken: String) {
        try {
            logDebug("Initializing iOS push notifications")
            logDebug("FCM token: ${fcmToken.take(20)}...")
            
            // Get device info
            val deviceInfo = getDeviceInfo()
            
            // Register with backend
            register(fcmToken, deviceInfo)
            
        } catch (e: Exception) {
            logDebug("Failed to initialize iOS push: ${e.message}")
            throw e
        }
    }
    
    /**
     * Get device information for registration
     */
    private fun getDeviceInfo(): DeviceInfo {
        val device = UIDevice.currentDevice
        return DeviceInfo(
            deviceModel = "${device.model} ${device.name}",
            osVersion = "iOS ${device.systemVersion}",
            appVersion = getAppVersion()
        )
    }
    
    /**
     * Get app version from bundle
     */
    private fun getAppVersion(): String {
        return try {
            val bundle = NSBundle.mainBundle
            bundle.objectForInfoDictionaryKey("CFBundleShortVersionString") as? String ?: "1.0.0"
        } catch (e: Exception) {
            "1.0.0"
        }
    }
    
    /**
     * Handle FCM token refresh
     */
    fun onTokenRefresh(newToken: String) {
        logDebug("FCM token refreshed: ${newToken.take(20)}...")
        val deviceInfo = getDeviceInfo()
        register(newToken, deviceInfo)
    }
    
    /**
     * Log debug messages
     */
    private fun logDebug(message: String) {
        Napier.d("🔔 [iOSPush] $message")
    }
    
    companion object {
        /**
         * Create iOS push manager for development
         */
        fun forDevelopment(authToken: String): IosPushNotificationManager {
            val config = SecurePushConfig.development(authToken, "ios")
            return IosPushNotificationManager(config)
        }
        
        /**
         * Create iOS push manager for production
         */
        fun forProduction(authToken: String): IosPushNotificationManager {
            val config = SecurePushConfig.production(authToken, "ios")
            return IosPushNotificationManager(config)
        }
    }
}
