import SwiftUI
import shared

struct ContentView: View {
    @StateObject private var viewModel = OrderPushNotificationViewModel()

    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // Header
                Text("IzyMercado Push Notifications")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // Token input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Authentication Token")
                        .font(.headline)
                    
                    TextField("Enter your token", text: $viewModel.token)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                }
                
                // Environment selection
                Toggle("Production Environment", isOn: $viewModel.isProduction)
                    .padding(.horizontal)
                
                // Registration controls
                HStack(spacing: 12) {
                    But<PERSON>("Register") {
                        viewModel.register()
                    }
                    .disabled(viewModel.token.isEmpty || viewModel.connectionState.isRegistered)
                    .buttonStyle(.borderedProminent)

                    But<PERSON>("Unregister") {
                        viewModel.unregister()
                    }
                    .disabled(!viewModel.connectionState.isRegistered)
                    .buttonStyle(.bordered)

                    But<PERSON>("Validate") {
                        viewModel.validateToken()
                    }
                    .disabled(!viewModel.connectionState.isRegistered)
                    .buttonStyle(.bordered)
                }
                
                // Registration status
                ConnectionStatusView(connectionState: viewModel.connectionState)
                
                // Error display
                if !viewModel.errors.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Errors")
                            .font(.headline)
                            .foregroundColor(.red)
                        
                        ForEach(Array(viewModel.errors.suffix(3).enumerated()), id: \.offset) { _, error in
                            Text(error)
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.red.opacity(0.1))
                                .cornerRadius(4)
                        }
                    }
                    .padding()
                    .background(Color.red.opacity(0.05))
                    .cornerRadius(8)
                }
                
                // Order updates
                VStack(alignment: .leading, spacing: 8) {
                    Text("Order Updates (\(viewModel.orderUpdates.count))")
                        .font(.headline)
                    
                    ScrollView {
                        LazyVStack(spacing: 8) {
                            ForEach(Array(viewModel.orderUpdates.suffix(10).reversed().enumerated()), id: \.offset) { _, order in
                                OrderUpdateView(orderUpdate: order)
                            }
                        }
                    }
                }
                
                Spacer()
            }
            .padding()
        }
        .navigationTitle("Push Notifications")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct ConnectionStatusView: View {
    let connectionState: SecurePushConnectionState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Circle()
                    .fill(connectionState.isRegistered ? Color.green : Color.red)
                    .frame(width: 12, height: 12)

                Text(connectionState.isRegistered ? "Registered" : "Not Registered")
                    .font(.headline)
                    .fontWeight(.bold)
            }
            
            if let pushToken = connectionState.pushToken {
                Text("Push Token: \(String(pushToken.suffix(8)))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            if connectionState.registrationAttempts > 0 {
                Text("Registration attempts: \(connectionState.registrationAttempts)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            if let lastError = connectionState.lastError {
                Text("Last error: \(lastError)")
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
        .padding()
        .background(connectionState.isRegistered ? Color.green.opacity(0.1) : Color.red.opacity(0.1))
        .cornerRadius(8)
    }
}

struct OrderUpdateView: View {
    let orderUpdate: OrderStatusNotification
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(orderUpdate.isNewOrder ? "🆕 New Order" : "📦 Status Update")
                    .font(.headline)
                    .fontWeight(.bold)
                
                Spacer()
                
                Text(orderUpdate.shortOrderId)
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(4)
            }
            
            Text(orderUpdate.statusDescription)
                .font(.subheadline)
                .fontWeight(.medium)
            
            if !orderUpdate.message.isEmpty {
                Text(orderUpdate.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    if let companyName = orderUpdate.companyName {
                        Text("Company: \(companyName)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if let updaterName = orderUpdate.updaterName {
                        Text("Updated by: \(updaterName)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Text(formatTimestamp(orderUpdate.timestamp))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            if !orderUpdate.isNewOrder, let oldStatus = orderUpdate.oldStatus {
                Text("\(oldStatus) → \(orderUpdate.newStatus)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
            }
        }
        .padding()
        .background(orderUpdate.isNewOrder ? Color.orange.opacity(0.1) : Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    private func formatTimestamp(_ timestamp: String) -> String {
        // Simple timestamp formatting - in a real app you'd use proper date formatting
        if let tIndex = timestamp.firstIndex(of: "T"),
           let dotIndex = timestamp.firstIndex(of: ".") {
            return String(timestamp[timestamp.index(after: tIndex)..<dotIndex])
        }
        return timestamp
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
