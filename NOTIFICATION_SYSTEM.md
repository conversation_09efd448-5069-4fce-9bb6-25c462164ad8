# Real-time Order Notification System

## Overview

This document describes the comprehensive real-time order notification system implemented for the partner dashboard. The system provides WebSocket-based real-time updates, browser notifications, audio alerts, and a responsive user interface.

## Architecture

### Core Components

1. **WebSocket Service** (`src/hooks/useOrderWebSocket.ts`)
   - Manages WebSocket connections to the backend
   - <PERSON>les automatic reconnection with exponential backoff
   - Processes incoming order status updates
   - Maintains connection state and error handling

2. **Browser Notifications** (`src/hooks/useBrowserNotifications.ts`)
   - Manages browser notification permissions
   - Displays native notifications for order updates
   - Handles cross-browser compatibility
   - Provides user settings for notification preferences

3. **Audio Notifications** (`src/hooks/useAudioNotifications.ts`)
   - Generates programmatic notification sounds
   - Supports multiple sound types (new order, status update, connection, error)
   - Respects browser autoplay policies
   - Provides volume control and user preferences

4. **Notification State Management** (`src/hooks/useOrderNotifications.ts`)
   - Combines all notification functionality
   - Manages notification history and unread counts
   - Handles notification acknowledgment
   - Persists state across page navigation

5. **UI Components**
   - `NotificationSettings.tsx` - Settings dialog for user preferences
   - `ConnectionStatusIndicator.tsx` - Visual connection status indicator
   - `NotificationPanel.tsx` - Responsive notification list panel

## Features

### WebSocket Integration
- **Automatic Connection**: Connects when user is authenticated
- **Reconnection Logic**: Exponential backoff with max 5 attempts
- **Ping/Pong**: Keep-alive mechanism every 30 seconds
- **Error Handling**: Comprehensive error reporting and recovery

### Browser Notifications
- **Permission Management**: Requests and manages notification permissions
- **Cross-browser Support**: Works on Chrome, Firefox, Safari, Edge
- **Rich Notifications**: Includes order details and action buttons
- **Auto-dismiss**: Notifications auto-close after 5 seconds

### Audio Notifications
- **Programmatic Generation**: Creates sounds using Web Audio API
- **Multiple Sound Types**: Different tones for different notification types
- **Fallback System**: Works even without audio files
- **User Controls**: Volume control and enable/disable options

### Responsive Design
- **Mobile-first**: Uses drawer on mobile, sheet on desktop
- **Adaptive UI**: Adjusts to different screen sizes
- **Touch-friendly**: Optimized for touch interactions
- **Accessibility**: Full ARIA support and keyboard navigation

## Usage

### Basic Integration

```typescript
import useOrderNotifications from '@/hooks/useOrderNotifications';

const MyComponent = () => {
  const token = localStorage.getItem('token') || '';
  const notifications = useOrderNotifications(token, true);

  return (
    <div>
      {/* Connection Status */}
      <ConnectionStatusIndicator
        status={notifications.connectionState}
        onReconnect={notifications.reconnect}
        showDetails
      />

      {/* Notification Panel */}
      <NotificationPanel
        notifications={notifications.notifications}
        unreadCount={notifications.unreadCount}
        onMarkAsRead={notifications.markAsRead}
        onMarkAllAsRead={notifications.markAllAsRead}
        onClearAll={notifications.clearAllNotifications}
      />

      {/* Settings */}
      <NotificationSettings />
    </div>
  );
};
```

### WebSocket Message Format

```typescript
interface OrderStatusNotification {
  order_id: string;
  invoice_id: number;
  new_status: string;
  old_status?: string;
  status_description: string;
  message: string;
  updated_by: string;
  updater_name?: string;
  user_id: number;
  company_id: number;
  company_name?: string;
  timestamp: string;
}
```

## Configuration

### Environment Variables
- `VITE_NODE_ENV`: Determines WebSocket URL (development/production)

### WebSocket URLs
- **Development**: `ws://localhost:8080/v1/ws/connect`
- **Production**: `wss://api.izymercado.com.br/v1/ws/connect`

### Storage Keys
- `orderNotifications`: Notification history and state
- `orderNotificationSettings`: Browser notification preferences
- `audioNotificationSettings`: Audio notification preferences

## Browser Compatibility

### Supported Features by Browser

| Feature | Chrome | Firefox | Safari | Edge |
|---------|--------|---------|--------|------|
| WebSocket | ✅ | ✅ | ✅ | ✅ |
| Browser Notifications | ✅ | ✅ | ✅ | ✅ |
| Web Audio API | ✅ | ✅ | ✅ | ✅ |
| Service Worker | ✅ | ✅ | ✅ | ✅ |

### Fallbacks
- **No WebSocket**: Graceful degradation to polling (if implemented)
- **No Notifications**: Toast notifications as fallback
- **No Audio**: Silent operation with visual indicators
- **No Local Storage**: In-memory state management

## Performance Considerations

### Memory Management
- **Notification Limit**: Maximum 50 stored notifications
- **Auto-cleanup**: Removes notifications older than 7 days
- **Connection Pooling**: Single WebSocket connection per session

### Network Optimization
- **Reconnection Strategy**: Exponential backoff prevents server overload
- **Ping Interval**: 30-second intervals for connection health
- **Message Batching**: Efficient handling of multiple notifications

## Security

### Authentication
- **Token-based**: Uses JWT tokens for WebSocket authentication
- **Auto-refresh**: Handles token renewal automatically
- **Secure Transport**: WSS in production environment

### Data Privacy
- **Local Storage**: Notifications stored locally only
- **No Sensitive Data**: Only order IDs and status information
- **User Control**: Users can clear all data anytime

## Troubleshooting

### Common Issues

1. **WebSocket Connection Fails**
   - Check network connectivity
   - Verify token validity
   - Check firewall/proxy settings

2. **Notifications Not Showing**
   - Check browser permissions
   - Verify notification settings
   - Check browser compatibility

3. **Audio Not Playing**
   - Check browser autoplay policies
   - Verify audio permissions
   - Test with user interaction

### Debug Information
- Enable debug logging in browser console
- Check WebSocket connection status
- Monitor notification permission state

## Future Enhancements

### Planned Features
- **Push Notifications**: Service Worker integration
- **Custom Sounds**: User-uploadable notification sounds
- **Advanced Filtering**: Notification filtering by company/status
- **Analytics**: Notification engagement metrics

### API Extensions
- **Batch Updates**: Multiple order updates in single message
- **Typing Indicators**: Real-time status change indicators
- **Presence**: Online/offline user status

## Testing

### Manual Testing Checklist
- [ ] WebSocket connection establishment
- [ ] Automatic reconnection on disconnect
- [ ] Browser notification display
- [ ] Audio notification playback
- [ ] Settings persistence
- [ ] Mobile responsiveness
- [ ] Accessibility features

### Automated Testing
- Unit tests for hooks and utilities
- Integration tests for WebSocket communication
- E2E tests for user workflows
- Cross-browser compatibility tests

## Support

For technical support or questions about the notification system:
1. Check this documentation
2. Review browser console for errors
3. Test with different browsers
4. Contact development team with specific error details
