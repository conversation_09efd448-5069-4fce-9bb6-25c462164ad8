# Environment Configuration
VITE_NODE_ENV=development

# API Configuration
# Development API URL (default: http://localhost:8080)
# Production API URL (default: https://api.izymercado.com.br)
VITE_API_URL_DEV=http://localhost:8080
VITE_API_URL_PROD=https://api.izymercado.com.br

# Push Notification Configuration
# Enable/disable push notifications
VITE_PUSH_NOTIFICATIONS_ENABLED=true

# Optional: Custom service worker path
VITE_PUSH_SW_PATH=/push-sw.js

# Note: No Firebase configuration needed in frontend!
# Backend provides VAPID key via API endpoint: /v1/secure-push/vapid-key
# Backend generates FCM tokens using Firebase Admin SDK
# Frontend only sends Web Push subscription data

# Development Fallback (REMOVE IN PRODUCTION!)
# Use this only while implementing backend endpoints
# Get VAPID key from Firebase Console → Project Settings → Cloud Messaging → Web Push certificates
# VITE_FALLBACK_VAPID_KEY=your-vapid-public-key-for-development-only
