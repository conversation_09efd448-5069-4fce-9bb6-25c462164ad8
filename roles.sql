--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4 (Ubuntu 17.4-1.pgdg24.04+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.roles (id, name, description, created_at, updated_at) VALUES (1, 'admin', 'Administrator with full system access', '2025-06-20 13:18:33.388773', '2025-06-20 13:18:33.388773');
INSERT INTO public.roles (id, name, description, created_at, updated_at) VALUES (2, 'partner', 'Company owner with partner-level access', '2025-06-20 13:18:33.388773', '2025-06-20 13:18:33.388773');
INSERT INTO public.roles (id, name, description, created_at, updated_at) VALUES (3, 'user', 'Standard user with basic access', '2025-06-20 13:18:33.388773', '2025-06-20 13:18:33.388773');


--
-- Name: roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.roles_id_seq', 3, true);


--
-- PostgreSQL database dump complete
--

