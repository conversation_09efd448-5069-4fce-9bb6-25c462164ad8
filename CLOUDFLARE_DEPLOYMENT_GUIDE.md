# Cloudflare Pages Deployment Guide

## Environment Variables Configuration

To deploy this application on Cloudflare Pages with Firebase push notifications, you need to configure the following environment variables in your Cloudflare Pages dashboard.

### Required Firebase Environment Variables

Navigate to your Cloudflare Pages project → Settings → Environment Variables and add:

#### Production Environment Variables:

```
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_firebase_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
VITE_FIREBASE_APP_ID=your_firebase_app_id
VITE_FIREBASE_VAPID_KEY=your_vapid_key_for_web_push
```

#### Preview Environment Variables:
Set the same variables for the Preview environment if you want push notifications to work in preview deployments.

### How to Get Firebase Configuration Values

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project**
3. **Go to Project Settings** (gear icon)
4. **Scroll down to "Your apps"** section
5. **Select your web app** or create one if it doesn't exist
6. **Copy the configuration values** from the Firebase SDK snippet

Example Firebase config object:
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX", // → VITE_FIREBASE_API_KEY
  authDomain: "your-project.firebaseapp.com", // → VITE_FIREBASE_AUTH_DOMAIN
  projectId: "your-project-id", // → VITE_FIREBASE_PROJECT_ID
  storageBucket: "your-project.appspot.com", // → VITE_FIREBASE_STORAGE_BUCKET
  messagingSenderId: "123456789", // → VITE_FIREBASE_MESSAGING_SENDER_ID
  appId: "1:123456789:web:abcdef123456" // → VITE_FIREBASE_APP_ID
};
```

### How to Get VAPID Key

1. **In Firebase Console**, go to **Project Settings**
2. **Click on "Cloud Messaging" tab**
3. **Scroll down to "Web configuration"**
4. **Generate or copy your VAPID key**
5. **Use this value for** `VITE_FIREBASE_VAPID_KEY`

### Setting Environment Variables in Cloudflare Pages

1. **Log in to Cloudflare Dashboard**
2. **Go to Pages** → Select your project
3. **Go to Settings** → Environment Variables
4. **Add each variable** with the exact names listed above
5. **Set for both Production and Preview** environments
6. **Save and redeploy** your application

### Troubleshooting

#### "Firebase not configured" Error
- **Check variable names**: Ensure they start with `VITE_` prefix
- **Verify values**: Make sure no values are empty or contain placeholder text
- **Redeploy**: After adding variables, trigger a new deployment
- **Check build logs**: Look for any errors during the build process

#### Service Worker MIME Type Error
If you see: `The script has an unsupported MIME type ('text/html')` or `Unexpected token '<'`

**This means the service worker file is returning HTML instead of JavaScript. Solutions:**

1. **Check file accessibility**: Visit `https://yourdomain.com/firebase-messaging-sw.js` directly
   - Should return JavaScript code, not HTML
   - If you see HTML, the file is not being served correctly

2. **Verify deployment includes service worker**:
   - Check your build output includes `firebase-messaging-sw.js`
   - Ensure `_headers` and `_redirects` files are in your deployment

3. **Clear Cloudflare cache**:
   - Go to Cloudflare Dashboard → Caching → Purge Everything
   - Wait a few minutes and test again

4. **Check Cloudflare Pages build**:
   - Build command: `npm run build`
   - Build output directory: `dist`
   - Verify build logs show service worker creation

5. **Use diagnostic script**:
   ```javascript
   // Add to your HTML or run in browser console
   const script = document.createElement('script');
   script.src = '/deployment-check.js';
   document.head.appendChild(script);
   ```

6. **Manual verification**:
   ```javascript
   // Test in browser console
   fetch('/firebase-messaging-sw.js')
     .then(r => console.log('Status:', r.status, 'Content-Type:', r.headers.get('content-type')))
     .catch(e => console.error('Error:', e));
   ```

#### Push Notifications Not Working
- **Check browser permissions**: Ensure notifications are allowed
- **Verify VAPID key**: Make sure it's the correct web push certificate
- **Check service worker**: Ensure `/firebase-messaging-sw.js` is accessible
- **Test in different browsers**: Some browsers have different FCM support
- **Check console**: Look for Firebase initialization messages

#### Environment Variables Not Loading
- **Prefix check**: All variables must start with `VITE_`
- **Case sensitivity**: Variable names are case-sensitive
- **Deployment**: Variables are only available after deployment, not in local development
- **Build cache**: Clear Cloudflare's build cache if variables aren't updating

### Local Development

For local development, create a `.env.local` file in your project root:

```bash
# .env.local (for local development only)
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_firebase_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
VITE_FIREBASE_APP_ID=your_firebase_app_id
VITE_FIREBASE_VAPID_KEY=your_vapid_key_for_web_push
```

**Note**: Never commit `.env.local` to version control. It's already in `.gitignore`.

### Security Notes

- **Firebase config is safe to expose**: These values are meant to be public
- **VAPID key is safe to expose**: It's designed for client-side use
- **No sensitive data**: These variables don't contain private keys or secrets
- **Domain restrictions**: Configure Firebase to only allow your domains

### Build Configuration

Ensure your Cloudflare Pages build settings are:
- **Build command**: `npm run build`
- **Build output directory**: `dist`
- **Node.js version**: 18 or higher

### Required Files for Deployment

The build process automatically creates these essential files in the `dist` folder:

1. **`firebase-messaging-sw.js`** - The Firebase service worker for push notifications
2. **`_headers`** - Cloudflare Pages headers configuration (ensures correct MIME types)
3. **`_redirects`** - Cloudflare Pages redirects configuration (handles SPA routing)

**Important**: These files must be present in your deployment for push notifications to work correctly.

### Testing Deployment

After setting up environment variables:

1. **Trigger a new deployment**
2. **Check browser console** for Firebase initialization messages
3. **Test push notification registration** in partner dashboard
4. **Verify no "Firebase not configured" errors**

### Support

If you continue to experience issues:
1. Check the browser console for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure Firebase project has Cloud Messaging enabled
4. Test with a fresh browser session (clear cache/cookies)

The application will gracefully disable push notifications if Firebase is not configured, so the app will still function without these variables.
