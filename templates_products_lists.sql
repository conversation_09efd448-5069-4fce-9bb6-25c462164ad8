--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4 (Ubuntu 17.4-1.pgdg24.04+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: templates_products_lists; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.templates_products_lists (id, name, is_active, external_id, created_at, updated_at) VALUES (3, 'Compras do mês', true, '01JR0YDFS87ZNSH4DSCER8DJBA', '2025-05-08 17:40:09.298286', '2025-05-08 17:40:09.298286');


--
-- Name: templates_products_lists_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.templates_products_lists_id_seq', 4, true);


--
-- PostgreSQL database dump complete
--

