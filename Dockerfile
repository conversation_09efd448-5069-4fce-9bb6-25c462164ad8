
FROM golang:1.24.0 AS development
WORKDIR /go/src/main
COPY . .
CMD [ "make" ]


#################################################
# BUILDER
#################################################
FROM golang:1.24.0 AS builder
WORKDIR /app
COPY . ./ 

ARG GIT_TAG  # Accept the Git tag as a build argument

RUN make swagger
RUN CGO_ENABLED=0 go build -ldflags "-s -w -X main.version=$GIT_TAG" -buildvcs=false -o izymercado-api ./cmd

############################
# MULTISTAGE BUILD
############################
FROM golang:1.24.0 AS production
COPY --from=builder /app/izymercado-api /izymercado-api
COPY --from=builder /app/Makefile .
COPY --from=builder /app/scripts/migrate.sh ./scripts/migrate.sh
COPY --from=builder /app/pkg/storage/postgres/migrations ./pkg/storage/postgres/migrations
EXPOSE 8080
CMD [ "make", "start-prod" ]
