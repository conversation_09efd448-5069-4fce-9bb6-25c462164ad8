--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4 (Ubuntu 17.4-1.pgdg24.04+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: templates_products_lists_items; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.templates_products_lists_items (template_id, product_id, quantity) VALUES (3, 2722, 2);
INSERT INTO public.templates_products_lists_items (template_id, product_id, quantity) VALUES (3, 2734, 1);
INSERT INTO public.templates_products_lists_items (template_id, product_id, quantity) VALUES (3, 2719, 1);
INSERT INTO public.templates_products_lists_items (template_id, product_id, quantity) VALUES (3, 2737, 1);
INSERT INTO public.templates_products_lists_items (template_id, product_id, quantity) VALUES (3, 2733, 1);
INSERT INTO public.templates_products_lists_items (template_id, product_id, quantity) VALUES (3, 2736, 1);


--
-- PostgreSQL database dump complete
--

