# Owner Management Feature

## Overview
The Owner Management feature allows administrators to search for users and assign them as owners of companies. This feature is only available in the admin CompanyDetails page and is not accessible to partner users.

## Features Implemented

### 1. User Search Component
- **Location**: `src/components/OwnerManagement.tsx`
- **Search Functionality**: 
  - Search by CPF, email, or name
  - Debounced search with 500ms delay
  - Minimum 3 characters required for search
  - Real-time search results display

### 2. API Integration
- **Search Users**: `GET /v1/user/{query}` - Search users by CPF, email, or name
- **Link Owner**: `PUT /v1/company/{externalID}/owner` - Assign user as company owner
- **Services**: Added `userService` in `src/services/api.ts`

### 3. UI Components
- **Current Owner Display**: Shows current owner information with contact details
- **Search Interface**: Autocomplete dropdown with user search results
- **Confirmation Dialog**: Confirmation modal before linking a new owner
- **Loading States**: Proper loading indicators during search and linking operations
- **Error Handling**: Toast notifications for success/error states

### 4. Access Control
- **Admin Only**: Feature is only visible and accessible to admin users
- **Role-based Rendering**: Uses `useAccessControl` hook to check admin permissions
- **Tab Integration**: Added as a new "Proprietário" tab in CompanyDetails page

## Technical Implementation

### Components Structure
```
src/
├── components/
│   └── OwnerManagement.tsx          # Main owner management component
├── pages/
│   └── CompanyDetails.tsx           # Updated with owner management tab
├── services/
│   └── api.ts                       # Added userService with search and link methods
├── types/
│   └── api.ts                       # Added User types and API response types
└── hooks/
    └── useAccessControl.ts          # Role-based access control
```

### Key Features
1. **Debounced Search**: 500ms delay to prevent excessive API calls
2. **Validation**: Prevents linking the same user who is already the owner
3. **Error Handling**: Comprehensive error handling with user-friendly messages
4. **Loading States**: Visual feedback during API operations
5. **Responsive Design**: Works on both desktop and mobile devices

### API Endpoints Used
- `GET /v1/user/{query}` - Search users by CPF, email, or name
- `PUT /v1/company/{externalID}/owner` - Link user as company owner

### Data Flow
1. Admin navigates to CompanyDetails page
2. Clicks on "Proprietário" tab (only visible to admins)
3. Types in search field to find users
4. Selects a user from search results
5. Confirms the linking in the confirmation dialog
6. System updates the company owner and refreshes the data

## Usage Instructions

### For Administrators
1. Navigate to any company details page
2. Click on the "Proprietário" tab
3. Use the search field to find users by:
   - CPF (with or without formatting)
   - Email address
   - Full or partial name
4. Select a user from the search results
5. Confirm the linking in the dialog
6. The system will update the company owner

### Search Tips
- Type at least 3 characters to start searching
- Search is case-insensitive
- CPF can be searched with or without formatting
- Partial name matches are supported

## Error Handling
- **User Not Found**: Shows "Nenhum usuário encontrado" message
- **API Errors**: Displays toast notifications with error details
- **Validation**: Prevents linking the same user who is already the owner
- **Network Issues**: Graceful handling of connection problems

## Security Considerations
- Feature is only accessible to admin users
- All API calls are authenticated
- Proper validation on both frontend and backend
- Confirmation dialog prevents accidental changes

## Future Enhancements
- Add ability to remove/unlink current owner
- Bulk owner assignment for multiple companies
- Owner history tracking
- Email notifications when ownership changes
- Advanced search filters (by role, status, etc.)
