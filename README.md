# Izy Mercad Backend

[![Deploy from Release](https://github.com/Izy-Mercado/backend/actions/workflows/deploy.production.yml/badge.svg)](https://github.com/Izy-Mercado/backend/actions/workflows/deploy.production.yml)

## Project Overview

Izy Mercado Backend is the server-side application for the Izy Mercado platform, which aims to provide a seamless and efficient marketplace experience. This backend service handles all the core functionalities, including user authentication, product management, order processing, and more.

## Features

- **User Authentication**: Secure login and registration system.
- **Product Management**: CRUD operations for products.
- **Order Processing**: Handle customer orders and payments.
- **API Endpoints**: RESTful API for frontend integration.
- **Database Integration**: Uses PostgreSQL for data storage.
- **Deployment**: Automated deployment with Fly.io.

## Getting Started

### Prerequisites

- Node.js
- PostgreSQL
- Fly.io account

### Installation

1. Clone the repository:
  ```sh
  git clone https://github.com/Izy-Mercado/backend.git
  cd backend
  ```

2. Install dependencies:
  ```sh
  npm install
  ```

3. Set up environment variables:
  ```sh
  cp .env.example .env
  # Edit .env with your configuration
  ```

4. Run database migrations:
  ```sh
  npm run migrate
  ```

5. Start the development server:
  ```sh
  npm run dev
  ```

## Contributing

We welcome contributions! Please read our [Contributing Guidelines](CONTRIBUTING.md) for more information.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## Contact

For any inquiries, please contact <NAME_EMAIL>.
