
# Backoffice de Cadastro de Parceiros

Um painel administrativo para gerenciamento de parceiros (empresas) e produtos associados.

## Funcionalidades Principais

- **Autenticação Segura**: Login com email/senha e opção para enviar código de acesso
- **Gerenciamento de Parceiros**: Cadastro e visualização de empresas parceiras
- **Catálogo de Produtos**: Visualização e seleção de produtos disponíveis
- **Associação de Produtos**: Adição de produtos aos parceiros de forma intuitiva

## Fluxo de Uso

1. Faça login no sistema usando suas credenciais
2. Na dashboard, você pode visualizar os parceiros existentes ou criar novos
3. Para adicionar produtos a um parceiro, primeiro selecione ou crie o parceiro
4. Na página do parceiro, use a opção "Adicionar Produtos" para associar produtos

## Tratamento de Erros

O sistema trata automaticamente erros de autenticação (401) realizando refresh do token quando necessário.

## Segurança

- Tokens de autenticação armazenados de forma segura
- Refresh automático de tokens expirados
- Rotas protegidas para acessos não autorizados

## Tecnologias Utilizadas

- React com TypeScript
- TanStack Query para gerenciamento de estado e requisições
- Shadcn UI para componentes de interface
- Tailwind CSS para estilização
