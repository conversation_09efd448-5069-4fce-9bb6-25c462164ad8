import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Layout } from "@/components/Layout";

export const AdminPrivateRoute = () => {
  const { isAuthenticated, loading, userRole } = useAuth();

  // Wait for auth check to finish
  if (loading) return null;

  // Se não estiver autenticado, redireciona para a página de login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Se for partner, redireciona para o dashboard de parceiro
  if (userRole === 'partner') {
    return <Navigate to="/partner/dashboard" replace />;
  }

  // Se for admin, renderiza o layout admin com o conteúdo da rota
  return (
    <Layout>
      <Outlet />
    </Layout>
  );
};
