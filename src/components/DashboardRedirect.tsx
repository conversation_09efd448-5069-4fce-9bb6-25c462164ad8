import { Navigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

export const DashboardRedirect = () => {
  const { isAuthenticated, loading, dashboardUrl } = useAuth();

  // Wait for auth check to finish
  if (loading) return null;

  // Se não estiver autenticado, redireciona para login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Redireciona para o dashboard apropriado baseado no role do usuário
  return <Navigate to={dashboardUrl} replace />;
};
