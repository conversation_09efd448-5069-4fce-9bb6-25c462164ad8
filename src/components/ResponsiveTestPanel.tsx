import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useScreenSize, useIsMobile, useIsTablet } from '@/hooks/use-mobile';
import { Monitor, Smartphone, Tablet, Laptop, Eye, EyeOff } from 'lucide-react';

/**
 * Responsive Test Panel - Shows current breakpoint and device information
 * Useful for testing responsive design implementations
 */
const ResponsiveTestPanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const screenSize = useScreenSize();
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();

  const getScreenInfo = () => {
    if (typeof window === 'undefined') return { width: 0, height: 0 };
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio || 1
    };
  };

  const screenInfo = getScreenInfo();

  const getBreakpointInfo = () => {
    const breakpoints = {
      mobile: { min: 0, max: 767, color: 'bg-red-500' },
      tablet: { min: 768, max: 1023, color: 'bg-yellow-500' },
      desktop: { min: 1024, max: 1439, color: 'bg-blue-500' },
      large: { min: 1440, max: Infinity, color: 'bg-green-500' }
    };

    const current = screenInfo.width;
    let activeBreakpoint = 'unknown';
    let color = 'bg-gray-500';

    for (const [name, info] of Object.entries(breakpoints)) {
      if (current >= info.min && current <= info.max) {
        activeBreakpoint = name;
        color = info.color;
        break;
      }
    }

    return { activeBreakpoint, color, breakpoints };
  };

  const { activeBreakpoint, color, breakpoints } = getBreakpointInfo();

  const getDeviceIcon = () => {
    switch (screenSize) {
      case 'mobile':
        return <Smartphone className="h-4 w-4" />;
      case 'tablet':
        return <Tablet className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const getTailwindClasses = () => {
    const classes = [];
    if (screenInfo.width >= 640) classes.push('sm');
    if (screenInfo.width >= 768) classes.push('md');
    if (screenInfo.width >= 1024) classes.push('lg');
    if (screenInfo.width >= 1280) classes.push('xl');
    if (screenInfo.width >= 1536) classes.push('2xl');
    return classes;
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          size="sm"
          variant="outline"
          className="shadow-lg bg-white/90 backdrop-blur-sm"
        >
          <Eye className="h-4 w-4 mr-2" />
          Responsive Info
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80 max-w-[calc(100vw-2rem)]">
      <Card className="shadow-xl bg-white/95 backdrop-blur-sm border-2">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              {getDeviceIcon()}
              Responsive Test Panel
            </div>
            <Button
              onClick={() => setIsVisible(false)}
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
            >
              <EyeOff className="h-3 w-3" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-xs">
          {/* Current Screen Info */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="font-medium">Screen Size:</span>
              <Badge variant="outline" className={`${color} text-white border-none`}>
                {screenSize.toUpperCase()}
              </Badge>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>Width: {screenInfo.width}px</div>
              <div>Height: {screenInfo.height}px</div>
              <div>DPR: {screenInfo.devicePixelRatio}</div>
              <div>Breakpoint: {activeBreakpoint}</div>
            </div>
          </div>

          {/* Hook States */}
          <div className="space-y-1">
            <div className="font-medium">Hook States:</div>
            <div className="grid grid-cols-2 gap-1 text-xs">
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${isMobile ? 'bg-green-500' : 'bg-gray-300'}`} />
                isMobile: {isMobile.toString()}
              </div>
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${isTablet ? 'bg-green-500' : 'bg-gray-300'}`} />
                isTablet: {isTablet.toString()}
              </div>
            </div>
          </div>

          {/* Tailwind Classes */}
          <div className="space-y-1">
            <div className="font-medium">Active Tailwind Classes:</div>
            <div className="flex flex-wrap gap-1">
              {getTailwindClasses().map((cls) => (
                <Badge key={cls} variant="secondary" className="text-xs px-1 py-0">
                  {cls}:
                </Badge>
              ))}
              {getTailwindClasses().length === 0 && (
                <span className="text-gray-500">base (no prefixes)</span>
              )}
            </div>
          </div>

          {/* Breakpoint Reference */}
          <div className="space-y-1">
            <div className="font-medium">Breakpoint Reference:</div>
            <div className="space-y-1">
              {Object.entries(breakpoints).map(([name, info]) => (
                <div key={name} className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${info.color} ${activeBreakpoint === name ? 'ring-2 ring-offset-1 ring-gray-400' : ''}`} />
                    <span className={activeBreakpoint === name ? 'font-medium' : ''}>{name}</span>
                  </div>
                  <span className="text-gray-500">
                    {info.min}px - {info.max === Infinity ? '∞' : `${info.max}px`}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Test Elements */}
          <div className="space-y-2 pt-2 border-t">
            <div className="font-medium">Responsive Test Elements:</div>
            
            {/* Visibility Test */}
            <div className="space-y-1">
              <div className="text-xs font-medium">Visibility Tests:</div>
              <div className="grid grid-cols-4 gap-1 text-xs">
                <div className="hidden sm:block bg-blue-100 p-1 rounded text-center">sm+</div>
                <div className="hidden md:block bg-green-100 p-1 rounded text-center">md+</div>
                <div className="hidden lg:block bg-yellow-100 p-1 rounded text-center">lg+</div>
                <div className="hidden xl:block bg-purple-100 p-1 rounded text-center">xl+</div>
              </div>
            </div>

            {/* Grid Test */}
            <div className="space-y-1">
              <div className="text-xs font-medium">Grid Test:</div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-1">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="bg-gray-100 p-1 rounded text-center text-xs">
                    {i}
                  </div>
                ))}
              </div>
            </div>

            {/* Touch Target Test */}
            <div className="space-y-1">
              <div className="text-xs font-medium">Touch Target Test:</div>
              <Button size="sm" className="min-h-[44px] w-full touch-manipulation">
                44px Min Height (Touch-friendly)
              </Button>
            </div>
          </div>

          {/* Performance Info */}
          <div className="space-y-1 pt-2 border-t">
            <div className="font-medium">Performance:</div>
            <div className="text-xs text-gray-600">
              Use browser DevTools to test different screen sizes and orientations.
              Test touch interactions on actual devices when possible.
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ResponsiveTestPanel;
