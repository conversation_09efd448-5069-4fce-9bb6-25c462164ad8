import React from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Bell, 
  BellOff, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  RefreshCw,
  Smartphone
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FCMState {
  isInitialized: boolean;
  isRegistered: boolean;
  hasPermission: boolean;
  error: string | null;
  deviceId: string | null;
}

interface FCMStatusIndicatorProps {
  status: FCMState;
  onInitialize?: () => void;
  onRefresh?: () => void;
  variant?: 'badge' | 'button' | 'icon';
  showDetails?: boolean;
  className?: string;
}

const FCMStatusIndicator: React.FC<FCMStatusIndicatorProps> = ({
  status,
  onInitialize,
  onRefresh,
  variant = 'badge',
  showDetails = false,
  className
}) => {
  const getStatusInfo = () => {
    if (status.error) {
      // Check if it's a configuration error
      const isConfigError = status.error.includes('not configured') ||
                           status.error.includes('environment variables') ||
                           status.error.includes('VAPID key');

      return {
        icon: AlertCircle,
        text: isConfigError ? 'FCM Não Configurado' : 'Erro FCM',
        description: isConfigError ? 'Configure as variáveis de ambiente Firebase' : status.error,
        color: isConfigError ? 'bg-orange-100 text-orange-800 border-orange-300' : 'bg-red-100 text-red-800 border-red-300',
        variant: 'destructive' as const,
        pulse: false,
      };
    }

    if (!status.hasPermission) {
      return {
        icon: BellOff,
        text: 'Sem Permissão',
        description: 'Permissão de notificação não concedida',
        color: 'bg-yellow-100 text-yellow-800 border-yellow-300',
        variant: 'secondary' as const,
        pulse: false,
      };
    }

    if (status.isRegistered && status.isInitialized) {
      return {
        icon: CheckCircle,
        text: 'FCM Ativo',
        description: 'Notificações FCM configuradas',
        color: 'bg-green-100 text-green-800 border-green-300',
        variant: 'default' as const,
        pulse: false,
      };
    }

    if (status.isInitialized && !status.isRegistered) {
      return {
        icon: Loader2,
        text: 'Registrando FCM',
        description: 'Registrando token FCM...',
        color: 'bg-blue-100 text-blue-800 border-blue-300',
        variant: 'secondary' as const,
        pulse: true,
      };
    }

    return {
      icon: Smartphone,
      text: 'FCM Inativo',
      description: 'Notificações FCM não configuradas',
      color: 'bg-gray-100 text-gray-800 border-gray-300',
      variant: 'secondary' as const,
      pulse: false,
    };
  };

  const statusInfo = getStatusInfo();
  const IconComponent = statusInfo.icon;

  const StatusDetails = () => (
    <Card className="w-80">
      <CardHeader>
        <CardTitle className="flex items-center text-sm">
          <Smartphone className="h-4 w-4 mr-2" />
          Status FCM (Firebase Cloud Messaging)
        </CardTitle>
        <CardDescription>
          Notificações push para pagamentos PIX e pedidos
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <Badge className={statusInfo.color}>
            <IconComponent className={cn(
              "h-3 w-3 mr-1",
              statusInfo.pulse && "animate-spin"
            )} />
            {statusInfo.text}
          </Badge>
        </div>

        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Inicializado:</span>
            <span className={status.isInitialized ? 'text-green-600' : 'text-red-600'}>
              {status.isInitialized ? 'Sim' : 'Não'}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-muted-foreground">Registrado:</span>
            <span className={status.isRegistered ? 'text-green-600' : 'text-red-600'}>
              {status.isRegistered ? 'Sim' : 'Não'}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-muted-foreground">Permissão:</span>
            <span className={status.hasPermission ? 'text-green-600' : 'text-red-600'}>
              {status.hasPermission ? 'Concedida' : 'Negada'}
            </span>
          </div>

          {status.deviceId && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">Device ID:</span>
              <span className="font-mono text-xs">
                {status.deviceId.slice(-8)}
              </span>
            </div>
          )}
        </div>

        {status.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-sm">
              {status.error}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2">
          {onInitialize && !status.isInitialized && (
            <Button
              onClick={onInitialize}
              size="sm"
              variant="outline"
              className="flex-1"
            >
              <Smartphone className="h-4 w-4 mr-2" />
              Inicializar FCM
            </Button>
          )}

          {onRefresh && status.isInitialized && (
            <Button
              onClick={onRefresh}
              size="sm"
              variant="outline"
              className="flex-1"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Atualizar Token
            </Button>
          )}
        </div>

        <div className="text-xs text-muted-foreground">
          <p><strong>FCM</strong> gerencia notificações de:</p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Pagamentos PIX confirmados</li>
            <li>Novos pedidos (parceiros)</li>
            <li>Atualizações de status de pedidos</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );

  if (variant === 'icon') {
    const content = (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={cn(
                "inline-flex items-center justify-center w-6 h-6 rounded-full",
                statusInfo.color,
                className
              )}
              role="status"
              aria-label={`Status FCM: ${statusInfo.text} - ${statusInfo.description}`}
            >
              <IconComponent
                className={cn(
                  "h-3 w-3",
                  statusInfo.pulse && "animate-spin"
                )}
                aria-hidden="true"
              />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{statusInfo.text}: {statusInfo.description}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );

    return showDetails ? (
      <Popover>
        <PopoverTrigger asChild>
          {content}
        </PopoverTrigger>
        <PopoverContent align="end">
          <StatusDetails />
        </PopoverContent>
      </Popover>
    ) : content;
  }

  if (variant === 'button') {
    const content = (
      <Button
        variant="ghost"
        size="sm"
        className={cn("h-8 px-2", className)}
      >
        <IconComponent className={cn(
          "h-4 w-4 mr-2",
          statusInfo.pulse && "animate-spin"
        )} />
        <span className="hidden sm:inline">{statusInfo.text}</span>
      </Button>
    );

    return showDetails ? (
      <Popover>
        <PopoverTrigger asChild>
          {content}
        </PopoverTrigger>
        <PopoverContent align="end">
          <StatusDetails />
        </PopoverContent>
      </Popover>
    ) : content;
  }

  // Default badge variant
  const content = (
    <Badge className={cn(statusInfo.color, className)}>
      <IconComponent className={cn(
        "h-3 w-3 mr-1",
        statusInfo.pulse && "animate-spin"
      )} />
      {statusInfo.text}
    </Badge>
  );

  return showDetails ? (
    <Popover>
      <PopoverTrigger asChild>
        {content}
      </PopoverTrigger>
      <PopoverContent align="end">
        <StatusDetails />
      </PopoverContent>
    </Popover>
  ) : content;
};

export default FCMStatusIndicator;
