import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const ServiceWorkerTest: React.FC = () => {
  const [status, setStatus] = useState<string>('Not tested');
  const [error, setError] = useState<string>('');

  const testServiceWorker = async () => {
    setStatus('Testing...');
    setError('');

    try {
      // Check if service workers are supported
      if (!('serviceWorker' in navigator)) {
        throw new Error('Service workers not supported');
      }

      // Test simple service worker registration
      console.log('Testing service worker registration...');
      const registration = await navigator.serviceWorker.register('/sw-test.js', {
        scope: '/'
      });

      console.log('Service worker registered:', registration);
      setStatus(`✅ Service worker registered successfully! Scope: ${registration.scope}`);

      // Test message communication
      const messageChannel = new MessageChannel();
      messageChannel.port1.onmessage = (event) => {
        console.log('Response from service worker:', event.data);
        setStatus(prev => prev + `\n✅ Communication test: ${event.data.message}`);
      };

      if (registration.active) {
        registration.active.postMessage({ type: 'TEST' }, [messageChannel.port2]);
      } else {
        setStatus(prev => prev + '\n⚠️ Service worker not active yet');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Service worker test failed:', err);
      setError(errorMessage);
      setStatus('❌ Service worker test failed');
    }
  };

  const testPushServiceWorker = async () => {
    setStatus('Testing push service worker...');
    setError('');

    try {
      if (!('serviceWorker' in navigator)) {
        throw new Error('Service workers not supported');
      }

      console.log('Testing push service worker registration...');
      const registration = await navigator.serviceWorker.register('/push-sw.js', {
        scope: '/'
      });

      console.log('Push service worker registered:', registration);
      setStatus(`✅ Push service worker registered! Scope: ${registration.scope}`);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Push service worker test failed:', err);
      setError(errorMessage);
      setStatus('❌ Push service worker test failed');
    }
  };

  const checkCurrentRegistrations = async () => {
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      console.log('Current service worker registrations:', registrations);
      
      if (registrations.length === 0) {
        setStatus('No service workers currently registered');
      } else {
        const info = registrations.map((reg, index) => 
          `${index + 1}. Scope: ${reg.scope}, State: ${reg.active?.state || 'inactive'}`
        ).join('\n');
        setStatus(`Current registrations:\n${info}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Service Worker Debug Tool</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button onClick={testServiceWorker} variant="outline">
            Test Simple SW
          </Button>
          <Button onClick={testPushServiceWorker} variant="outline">
            Test Push SW
          </Button>
          <Button onClick={checkCurrentRegistrations} variant="outline">
            Check Registrations
          </Button>
        </div>

        {status && (
          <div className="p-3 bg-gray-100 rounded-md">
            <h4 className="font-medium mb-2">Status:</h4>
            <pre className="text-sm whitespace-pre-wrap">{status}</pre>
          </div>
        )}

        {error && (
          <div className="p-3 bg-red-100 border border-red-300 rounded-md">
            <h4 className="font-medium text-red-800 mb-2">Error:</h4>
            <pre className="text-sm text-red-700 whitespace-pre-wrap">{error}</pre>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <p><strong>Debug Info:</strong></p>
          <p>Current URL: {window.location.href}</p>
          <p>Service Worker Support: {('serviceWorker' in navigator) ? '✅' : '❌'}</p>
          <p>Push Manager Support: {('PushManager' in window) ? '✅' : '❌'}</p>
        </div>
      </CardContent>
    </Card>
  );
};
