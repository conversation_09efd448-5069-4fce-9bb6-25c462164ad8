import React, { useState } from 'react';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Bell,
  Package,
  Clock,
  CheckCircle,
  Trash2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
// Define the NotificationItem type to match AuthContext structure
interface NotificationItem {
  id: string;
  type: string;
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  data?: any;
}

interface NotificationPanelProps {
  notifications: NotificationItem[];
  unreadCount: number;
  onMarkAsRead: (notificationId: string) => void;
  onMarkAllAsRead: () => void;
  onClearAll: () => void;
  trigger?: React.ReactNode;
  className?: string;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({
  notifications,
  unreadCount,
  onMarkAsRead,
  onMarkAllAsRead,
  onClearAll,
  trigger,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d atrás`;
    if (hours > 0) return `${hours}h atrás`;
    if (minutes > 0) return `${minutes}m atrás`;
    return 'Agora';
  };

  const getNotificationIcon = (type: string) => {
    if (type === 'new_order_received' || type === 'new-order') {
      return <Package className="h-4 w-4 text-blue-600" />;
    } else if (type === 'payment_confirmed') {
      return <Package className="h-4 w-4 text-green-600" />;
    } else {
      return <Clock className="h-4 w-4 text-orange-600" />;
    }
  };

  const getNotificationTitle = (item: NotificationItem) => {
    // Use the title from FCM notification, or fallback to type-based title
    if (item.title) {
      return item.title;
    }

    // Fallback based on type
    if (item.type === 'new_order_received' || item.type === 'new-order') {
      return '🆕 Novo Pedido Recebido';
    } else if (item.type === 'payment_confirmed') {
      return '💰 Pagamento Confirmado';
    } else {
      return '📦 Status Atualizado';
    }
  };

  const getNotificationDescription = (item: NotificationItem) => {
    console.log({item});

    // Use the message from FCM notification if available
    if (item.message) {
      return item.message;
    }

    // Fallback to constructing description from data
    const orderId = item.data?.order_id?.slice(-8) || 'N/A';
    const companyName = item.data?.company_name || 'Empresa';

    if (item.type === 'new_order_received' || item.type === 'new-order') {
      return `Pedido ${orderId} - ${companyName}`;
    } else {
      const statusDescription = item.data?.status_description || 'Status atualizado';
      return `Pedido ${orderId} - ${statusDescription}`;
    }
  };

  const NotificationList = () => (
    <div className="space-y-4">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="font-semibold">Notificações</h3>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {unreadCount}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onMarkAllAsRead}
              className="text-xs"
              aria-label={`Marcar todas as ${unreadCount} notificações como lidas`}
            >
              <CheckCircle className="h-3 w-3 mr-1" aria-hidden="true" />
              Marcar todas
            </Button>
          )}
          {notifications.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearAll}
              className="text-xs text-red-600 hover:text-red-700"
              aria-label={`Limpar todas as ${notifications.length} notificações`}
            >
              <Trash2 className="h-3 w-3 mr-1" aria-hidden="true" />
              Limpar
            </Button>
          )}
        </div>
      </div>

      <Separator />

      {/* Notifications List */}
      <ScrollArea
        className="h-[400px] md:h-[500px]"
        aria-label="Lista de notificações"
      >
        {notifications.length === 0 ? (
          <div
            className="flex flex-col items-center justify-center py-8 text-center"
            role="status"
            aria-live="polite"
          >
            <Bell className="h-12 w-12 text-muted-foreground mb-4" aria-hidden="true" />
            <p className="text-muted-foreground">Nenhuma notificação</p>
            <p className="text-sm text-muted-foreground">
              Você será notificado sobre novos pedidos aqui
            </p>
          </div>
        ) : (
          <div
            className="space-y-3"
            role="list"
            aria-label={`${notifications.length} notificações`}
          >
            {notifications.map((item, index) => (
              <Card
                key={item.id}
                className={cn(
                  "cursor-pointer transition-colors hover:bg-muted/50 focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
                  !item.isRead && "border-l-4 border-l-blue-500 bg-blue-50/50"
                )}
                onClick={() => !item.isRead && onMarkAsRead(item.id)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    if (!item.isRead) onMarkAsRead(item.id);
                  }
                }}
                tabIndex={0}
                role="listitem"
                aria-label={`Notificação ${index + 1} de ${notifications.length}: ${getNotificationTitle(item)} - ${getNotificationDescription(item)}${!item.isRead ? ' - Não lida' : ' - Lida'}`}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(item.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1">
                          <p className="font-medium text-sm leading-tight">
                            {getNotificationTitle(item)}
                          </p>
                          <p className="text-sm text-muted-foreground mt-1">
                            {getNotificationDescription(item)}
                          </p>
                          {item.data?.message && item.data.message !== item.message && (
                            <p className="text-xs text-muted-foreground mt-2 italic">
                              "{item.data.message}"
                            </p>
                          )}
                        </div>
                        
                        <div className="flex flex-col items-end gap-1">
                          <span className="text-xs text-muted-foreground">
                            {formatTimeAgo(item.timestamp)}
                          </span>
                          {!item.isRead && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full" />
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 mt-3 text-xs text-muted-foreground">
                        {item.data?.order_id && (
                          <span>Pedido: {item.data.order_id.slice(-8)}</span>
                        )}
                        {item.data?.updater_name && (
                          <>
                            <span>•</span>
                            <span>Por: {item.data.updater_name}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );

  const defaultTrigger = (
    <Button
      variant="outline"
      size="sm"
      className={cn("relative", className)}
      aria-label={`Notificações${unreadCount > 0 ? ` - ${unreadCount} não lidas` : ''}`}
      aria-describedby="notification-count"
    >
      <Bell className="h-4 w-4 mr-2" aria-hidden="true" />
      <span className="hidden sm:inline">Notificações</span>
      {unreadCount > 0 && (
        <Badge
          className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center bg-red-500 text-white text-xs"
          id="notification-count"
          aria-label={`${unreadCount} notificações não lidas`}
        >
          {unreadCount > 99 ? '99+' : unreadCount}
        </Badge>
      )}
    </Button>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerTrigger asChild>
          {trigger || defaultTrigger}
        </DrawerTrigger>
        <DrawerContent className="max-h-[85vh]">
          <DrawerHeader className="text-left">
            <DrawerTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notificações
            </DrawerTitle>
            <DrawerDescription>
              Acompanhe atualizações dos seus pedidos
            </DrawerDescription>
          </DrawerHeader>
          <div className="px-4 pb-4">
            <NotificationList />
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        {trigger || defaultTrigger}
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notificações
          </SheetTitle>
          <SheetDescription>
            Acompanhe atualizações dos seus pedidos em tempo real
          </SheetDescription>
        </SheetHeader>
        <div className="mt-6">
          <NotificationList />
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default NotificationPanel;
