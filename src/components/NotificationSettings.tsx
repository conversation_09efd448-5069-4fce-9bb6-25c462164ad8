import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Bell, 
  BellOff, 
  Volume2, 
  VolumeX, 
  Settings, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Play,
  Pause
} from 'lucide-react';
import useBrowserNotifications from '@/hooks/useBrowserNotifications';
import useAudioNotifications from '@/hooks/useAudioNotifications';

interface NotificationSettingsProps {
  trigger?: React.ReactNode;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ trigger }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isTestingAudio, setIsTestingAudio] = useState(false);

  const {
    permission,
    isSupported: browserSupported,
    settings: browserSettings,
    canShowNotifications,
    requestPermission,
    updateSettings: updateBrowserSettings,
  } = useBrowserNotifications();

  const {
    isSupported: audioSupported,
    canPlay: audioCanPlay,
    settings: audioSettings,
    isPlaying: audioIsPlaying,
    updateSettings: updateAudioSettings,
    testAudio,
    enableAudio,
  } = useAudioNotifications();

  const handleRequestPermission = async () => {
    await requestPermission();
  };

  const handleTestAudio = async () => {
    if (audioIsPlaying) return;
    
    setIsTestingAudio(true);
    try {
      // Enable audio first if needed
      if (!audioCanPlay) {
        await enableAudio();
      }
      await testAudio('new-order');
    } finally {
      setIsTestingAudio(false);
    }
  };

  const handleVolumeChange = (value: number[]) => {
    updateAudioSettings({ volume: value[0] });
  };

  const getPermissionStatus = () => {
    switch (permission) {
      case 'granted':
        return {
          icon: <CheckCircle className="h-4 w-4 text-green-500" />,
          text: 'Permitido',
          variant: 'default' as const,
          color: 'bg-green-100 text-green-800 border-green-300',
        };
      case 'denied':
        return {
          icon: <XCircle className="h-4 w-4 text-red-500" />,
          text: 'Negado',
          variant: 'destructive' as const,
          color: 'bg-red-100 text-red-800 border-red-300',
        };
      default:
        return {
          icon: <AlertCircle className="h-4 w-4 text-yellow-500" />,
          text: 'Pendente',
          variant: 'secondary' as const,
          color: 'bg-yellow-100 text-yellow-800 border-yellow-300',
        };
    }
  };

  const permissionStatus = getPermissionStatus();

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Settings className="h-4 w-4 mr-2" />
      Configurar Notificações
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            Configurações de Notificação
          </DialogTitle>
          <DialogDescription>
            Configure como você deseja receber notificações sobre pedidos
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Browser Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <Bell className="h-4 w-4 mr-2" />
                  Notificações do Navegador
                </span>
                <Badge className={permissionStatus.color}>
                  {permissionStatus.icon}
                  <span className="ml-1">{permissionStatus.text}</span>
                </Badge>
              </CardTitle>
              <CardDescription>
                Receba notificações nativas do navegador quando novos pedidos chegarem
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!browserSupported && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Seu navegador não suporta notificações nativas.
                  </AlertDescription>
                </Alert>
              )}

              {browserSupported && permission === 'denied' && (
                <Alert>
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    As notificações foram negadas. Para habilitar, acesse as configurações do seu navegador.
                  </AlertDescription>
                </Alert>
              )}

              {browserSupported && permission === 'default' && (
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Permitir Notificações</p>
                    <p className="text-sm text-muted-foreground">
                      Clique para solicitar permissão para mostrar notificações
                    </p>
                  </div>
                  <Button onClick={handleRequestPermission}>
                    Permitir
                  </Button>
                </div>
              )}

              {canShowNotifications && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Ativar Notificações</p>
                      <p className="text-sm text-muted-foreground">
                        Receber notificações do navegador
                      </p>
                    </div>
                    <Switch
                      checked={browserSettings.enabled}
                      onCheckedChange={(enabled) => updateBrowserSettings({ enabled })}
                    />
                  </div>

                  {browserSettings.enabled && (
                    <>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Novos Pedidos</p>
                          <p className="text-sm text-muted-foreground">
                            Notificar quando novos pedidos chegarem
                          </p>
                        </div>
                        <Switch
                          checked={browserSettings.showNewOrders}
                          onCheckedChange={(showNewOrders) => updateBrowserSettings({ showNewOrders })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Atualizações de Status</p>
                          <p className="text-sm text-muted-foreground">
                            Notificar quando o status dos pedidos mudar
                          </p>
                        </div>
                        <Switch
                          checked={browserSettings.showOrderUpdates}
                          onCheckedChange={(showOrderUpdates) => updateBrowserSettings({ showOrderUpdates })}
                        />
                      </div>
                    </>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Audio Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <Volume2 className="h-4 w-4 mr-2" />
                  Notificações Sonoras
                </span>
                <Badge variant={audioSupported ? 'default' : 'secondary'}>
                  {audioSupported ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="ml-1">
                    {audioSupported ? 'Suportado' : 'Não Suportado'}
                  </span>
                </Badge>
              </CardTitle>
              <CardDescription>
                Configure alertas sonoros para notificações de pedidos
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!audioSupported && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Seu navegador não suporta notificações sonoras.
                  </AlertDescription>
                </Alert>
              )}

              {audioSupported && !audioCanPlay && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Para reproduzir sons, é necessário interagir com a página primeiro. 
                    Clique no botão "Testar Som" para habilitar.
                  </AlertDescription>
                </Alert>
              )}

              {audioSupported && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Ativar Sons</p>
                      <p className="text-sm text-muted-foreground">
                        Reproduzir alertas sonoros para notificações
                      </p>
                    </div>
                    <Switch
                      checked={audioSettings.enabled}
                      onCheckedChange={(enabled) => updateAudioSettings({ enabled })}
                    />
                  </div>

                  {audioSettings.enabled && (
                    <>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">Volume</p>
                          <span className="text-sm text-muted-foreground">
                            {Math.round(audioSettings.volume * 100)}%
                          </span>
                        </div>
                        <Slider
                          value={[audioSettings.volume]}
                          onValueChange={handleVolumeChange}
                          max={1}
                          min={0}
                          step={0.1}
                          className="w-full"
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Testar Som</p>
                          <p className="text-sm text-muted-foreground">
                            Reproduzir som de exemplo
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleTestAudio}
                          disabled={isTestingAudio || audioIsPlaying}
                        >
                          {audioIsPlaying ? (
                            <Pause className="h-4 w-4 mr-2" />
                          ) : (
                            <Play className="h-4 w-4 mr-2" />
                          )}
                          {isTestingAudio ? 'Testando...' : 'Testar'}
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Status Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Status das Notificações
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <span className="text-sm font-medium">Notificações do Navegador</span>
                  {canShowNotifications && browserSettings.enabled ? (
                    <Badge className="bg-green-100 text-green-800 border-green-300">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Ativo
                    </Badge>
                  ) : (
                    <Badge variant="secondary">
                      <XCircle className="h-3 w-3 mr-1" />
                      Inativo
                    </Badge>
                  )}
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <span className="text-sm font-medium">Notificações Sonoras</span>
                  {audioSupported && audioSettings.enabled ? (
                    <Badge className="bg-green-100 text-green-800 border-green-300">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Ativo
                    </Badge>
                  ) : (
                    <Badge variant="secondary">
                      <XCircle className="h-3 w-3 mr-1" />
                      Inativo
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NotificationSettings;
