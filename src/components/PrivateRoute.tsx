import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Layout } from "@/components/Layout";

export const PrivateRoute = () => {
  const { isAuthenticated, loading } = useAuth();

  // Wait for auth check to finish
  if (loading) return null;

  // Se não estiver autenticado, redireciona para a página de login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Se estiver autenticado, renderiza o layout com o conteúdo da rota
  return (
    <Layout>
      <Outlet />
    </Layout>
  );
};
