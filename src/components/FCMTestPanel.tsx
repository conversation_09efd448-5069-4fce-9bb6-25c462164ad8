import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  TestTube, 
  Send, 
  CheckCircle, 
  AlertCircle, 
  Copy,
  Smartphone
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

const FCMTestPanel: React.FC = () => {
  const { notifications } = useAuth();
  const [testMessage, setTestMessage] = useState({
    title: 'Teste FCM - Izy Mercado',
    body: 'Esta é uma notificação de teste do sistema FCM',
    type: 'test',
    data: JSON.stringify({
      type: 'test',
      timestamp: new Date().toISOString()
    }, null, 2)
  });

  const fcmStatus = notifications.fcmNotifications;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copiado para a área de transferência');
  };

  const sendTestNotification = () => {
    // Simulate a test notification
    const testNotification = new Notification(testMessage.title, {
      body: testMessage.body,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: 'fcm-test',
      data: JSON.parse(testMessage.data),
      requireInteraction: true
    });

    testNotification.onclick = () => {
      console.log('Test notification clicked');
      testNotification.close();
    };

    toast.success('Notificação de teste enviada');
  };

  const getStatusBadge = () => {
    if (fcmStatus.error) {
      return <Badge variant="destructive">Erro</Badge>;
    }
    if (fcmStatus.isRegistered && fcmStatus.isInitialized) {
      return <Badge variant="default">Ativo</Badge>;
    }
    if (fcmStatus.isInitialized) {
      return <Badge variant="secondary">Inicializado</Badge>;
    }
    return <Badge variant="outline">Inativo</Badge>;
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center">
          <TestTube className="h-5 w-5 mr-2" />
          Painel de Teste FCM
        </CardTitle>
        <CardDescription>
          Teste e monitore o status das notificações Firebase Cloud Messaging
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Overview */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Status FCM</Label>
            {getStatusBadge()}
          </div>
          <div className="space-y-2">
            <Label className="text-sm font-medium">Permissão</Label>
            <Badge variant={fcmStatus.hasPermission ? "default" : "destructive"}>
              {fcmStatus.hasPermission ? "Concedida" : "Negada"}
            </Badge>
          </div>
        </div>

        {/* Device Information */}
        {fcmStatus.deviceId && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Device ID</Label>
            <div className="flex items-center gap-2">
              <code className="flex-1 p-2 bg-muted rounded text-xs font-mono">
                {fcmStatus.deviceId}
              </code>
              <Button
                size="sm"
                variant="outline"
                onClick={() => copyToClipboard(fcmStatus.deviceId!)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Error Display */}
        {fcmStatus.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Erro FCM:</strong> {fcmStatus.error}
            </AlertDescription>
          </Alert>
        )}

        {/* Test Notification Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Teste de Notificação</Label>
            <Button
              size="sm"
              onClick={sendTestNotification}
              disabled={!fcmStatus.hasPermission}
            >
              <Send className="h-4 w-4 mr-2" />
              Enviar Teste
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="test-title">Título</Label>
              <input
                id="test-title"
                className="w-full p-2 border rounded"
                value={testMessage.title}
                onChange={(e) => setTestMessage(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="test-body">Mensagem</Label>
              <input
                id="test-body"
                className="w-full p-2 border rounded"
                value={testMessage.body}
                onChange={(e) => setTestMessage(prev => ({ ...prev, body: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="test-data">Dados (JSON)</Label>
              <Textarea
                id="test-data"
                className="font-mono text-xs"
                rows={4}
                value={testMessage.data}
                onChange={(e) => setTestMessage(prev => ({ ...prev, data: e.target.value }))}
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          {!fcmStatus.isInitialized && (
            <Button
              onClick={fcmStatus.initialize}
              variant="outline"
            >
              <Smartphone className="h-4 w-4 mr-2" />
              Inicializar FCM
            </Button>
          )}

          {fcmStatus.isInitialized && (
            <Button
              onClick={fcmStatus.refreshToken}
              variant="outline"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Atualizar Token
            </Button>
          )}
        </div>

        {/* Instructions */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Como testar:</strong>
            <ol className="list-decimal list-inside mt-2 space-y-1 text-sm">
              <li>Verifique se o status FCM está "Ativo"</li>
              <li>Clique em "Enviar Teste" para testar notificações locais</li>
              <li>Use o Firebase Console para enviar notificações reais</li>
              <li>Teste pagamentos PIX para verificar notificações automáticas</li>
            </ol>
          </AlertDescription>
        </Alert>

        {/* Debug Information */}
        <details className="space-y-2">
          <summary className="cursor-pointer text-sm font-medium">
            Informações de Debug
          </summary>
          <div className="space-y-2 text-xs">
            <div>
              <strong>Inicializado:</strong> {fcmStatus.isInitialized ? 'Sim' : 'Não'}
            </div>
            <div>
              <strong>Registrado:</strong> {fcmStatus.isRegistered ? 'Sim' : 'Não'}
            </div>
            <div>
              <strong>Permissão:</strong> {fcmStatus.hasPermission ? 'Concedida' : 'Negada'}
            </div>
            <div>
              <strong>Device ID:</strong> {fcmStatus.deviceId || 'N/A'}
            </div>
            <div>
              <strong>Erro:</strong> {fcmStatus.error || 'Nenhum'}
            </div>
          </div>
        </details>
      </CardContent>
    </Card>
  );
};

export default FCMTestPanel;
