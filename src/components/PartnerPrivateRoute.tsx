import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { PartnerLayout } from "@/components/PartnerLayout";

export const PartnerPrivateRoute = () => {
  const { isAuthenticated, loading, userRole } = useAuth();

  // Wait for auth check to finish
  if (loading) return null;

  // Se não estiver autenticado, redireciona para a página de login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Se for admin, redireciona para o dashboard admin
  if (userRole === 'admin') {
    return <Navigate to="/admin/dashboard" replace />;
  }

  // Se for partner, renderiza o layout de parceiro com o conteúdo da rota
  return (
    <PartnerLayout>
      <Outlet />
    </PartnerLayout>
  );
};
