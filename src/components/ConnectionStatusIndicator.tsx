import React, { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw,
  Clock,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ConnectionStatus {
  isRegistered: boolean;
  registrationAttempts: number;
  lastError?: string;
  pushToken?: string;
  deviceId?: string;
  lastRegistered?: Date;
}

interface ConnectionStatusIndicatorProps {
  status: ConnectionStatus;
  onReconnect?: () => void;
  className?: string;
  showDetails?: boolean;
  variant?: 'badge' | 'button' | 'icon';
}

const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({
  status,
  onReconnect,
  className,
  showDetails = false,
  variant = 'badge',
}) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastConnectedTime, setLastConnectedTime] = useState<Date | null>(null);

  // Track online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Track last registered time
  useEffect(() => {
    if (status.isRegistered) {
      setLastConnectedTime(status.lastRegistered || new Date());
    }
  }, [status.isRegistered, status.lastRegistered]);

  const getStatusInfo = () => {
    if (!isOnline) {
      return {
        icon: WifiOff,
        text: 'Offline',
        description: 'Sem conexão com a internet',
        color: 'bg-gray-100 text-gray-800 border-gray-300',
        variant: 'secondary' as const,
        pulse: false,
      };
    }

    if (status.isRegistered) {
      return {
        icon: CheckCircle,
        text: 'Notificações Ativas',
        description: 'Push notifications configuradas',
        color: 'bg-green-100 text-green-800 border-green-300',
        variant: 'default' as const,
        pulse: false,
      };
    }

    if (status.registrationAttempts > 0 && status.registrationAttempts < 3) {
      return {
        icon: Loader2,
        text: 'Registrando',
        description: `Tentativa ${status.registrationAttempts}/3`,
        color: 'bg-yellow-100 text-yellow-800 border-yellow-300',
        variant: 'secondary' as const,
        pulse: true,
      };
    }

    if (status.registrationAttempts >= 3) {
      return {
        icon: AlertCircle,
        text: 'Falha no Registro',
        description: 'Máximo de tentativas atingido',
        color: 'bg-orange-100 text-orange-800 border-orange-300',
        variant: 'destructive' as const,
        pulse: false,
      };
    }

    return {
      icon: AlertCircle,
      text: 'Não Registrado',
      description: status.lastError || 'Push notifications não configuradas',
      color: 'bg-red-100 text-red-800 border-red-300',
      variant: 'destructive' as const,
      pulse: false,
    };
  };

  const statusInfo = getStatusInfo();
  const IconComponent = statusInfo.icon;

  const formatLastConnected = () => {
    if (!lastConnectedTime) return 'Nunca';
    
    const now = new Date();
    const diff = now.getTime() - lastConnectedTime.getTime();
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes}m atrás`;
    }
    return `${seconds}s atrás`;
  };

  const StatusDetails = () => (
    <div className="space-y-3 min-w-[250px]">
      <div className="flex items-center justify-between">
        <span className="font-medium">Status das Notificações</span>
        <Badge className={statusInfo.color}>
          <IconComponent className={cn(
            "h-3 w-3 mr-1",
            statusInfo.pulse && "animate-spin"
          )} />
          {statusInfo.text}
        </Badge>
      </div>

      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-muted-foreground">Internet:</span>
          <span className={isOnline ? 'text-green-600' : 'text-red-600'}>
            {isOnline ? 'Online' : 'Offline'}
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-muted-foreground">Push:</span>
          <span className={status.isRegistered ? 'text-green-600' : 'text-red-600'}>
            {status.isRegistered ? 'Registrado' : 'Não Registrado'}
          </span>
        </div>

        {status.registrationAttempts > 0 && (
          <div className="flex justify-between">
            <span className="text-muted-foreground">Tentativas:</span>
            <span className="text-yellow-600">
              {status.registrationAttempts}/3
            </span>
          </div>
        )}

        {status.pushToken && (
          <div className="flex justify-between">
            <span className="text-muted-foreground">Token:</span>
            <span className="font-mono text-xs">
              {status.pushToken.slice(-8)}
            </span>
          </div>
        )}

        {status.deviceId && (
          <div className="flex justify-between">
            <span className="text-muted-foreground">Device ID:</span>
            <span className="font-mono text-xs">
              {status.deviceId.slice(-8)}
            </span>
          </div>
        )}

        {!status.isRegistered && lastConnectedTime && (
          <div className="flex justify-between">
            <span className="text-muted-foreground">Último registro:</span>
            <span>{formatLastConnected()}</span>
          </div>
        )}
      </div>

      {status.lastError && (
        <div className="p-2 bg-red-50 border border-red-200 rounded text-sm">
          <div className="flex items-start">
            <AlertCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
            <span className="text-red-700">{status.lastError}</span>
          </div>
        </div>
      )}

      {onReconnect && !status.isRegistered && isOnline && (
        <Button
          onClick={() => {
            console.log("🔔 Manual registration retry requested");
            onReconnect();
          }}
          size="sm"
          variant="outline"
          className="w-full"
          disabled={status.registrationAttempts > 0}
        >
          <RefreshCw className={cn(
            "h-4 w-4 mr-2",
            status.registrationAttempts > 0 && "animate-spin"
          )} />
          {status.registrationAttempts > 0 ? 'Registrando...' : 'Tentar Registrar'}
        </Button>
      )}

      {status.registrationAttempts >= 3 && !status.isRegistered && (
        <div className="p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
          <div className="flex items-start">
            <AlertCircle className="h-4 w-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
            <div className="text-yellow-700">
              <p className="font-medium">Máximo de tentativas atingido</p>
              <p className="text-xs mt-1">Clique em "Tentar Registrar" para tentar novamente</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  if (variant === 'icon') {
    const content = (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={cn(
                "inline-flex items-center justify-center w-6 h-6 rounded-full",
                statusInfo.color,
                className
              )}
              role="status"
              aria-label={`Status da conexão: ${statusInfo.text} - ${statusInfo.description}`}
            >
              <IconComponent
                className={cn(
                  "h-3 w-3",
                  statusInfo.pulse && "animate-spin"
                )}
                aria-hidden="true"
              />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{statusInfo.text}: {statusInfo.description}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );

    return showDetails ? (
      <Popover>
        <PopoverTrigger asChild>
          {content}
        </PopoverTrigger>
        <PopoverContent align="end">
          <StatusDetails />
        </PopoverContent>
      </Popover>
    ) : content;
  }

  if (variant === 'button') {
    const content = (
      <Button
        variant="ghost"
        size="sm"
        className={cn("h-8 px-2", className)}
      >
        <IconComponent className={cn(
          "h-4 w-4 mr-2",
          statusInfo.pulse && "animate-spin"
        )} />
        <span className="hidden sm:inline">{statusInfo.text}</span>
      </Button>
    );

    return showDetails ? (
      <Popover>
        <PopoverTrigger asChild>
          {content}
        </PopoverTrigger>
        <PopoverContent align="end">
          <StatusDetails />
        </PopoverContent>
      </Popover>
    ) : content;
  }

  // Default badge variant
  const content = (
    <Badge className={cn(statusInfo.color, className)}>
      <IconComponent className={cn(
        "h-3 w-3 mr-1",
        statusInfo.pulse && "animate-spin"
      )} />
      {statusInfo.text}
    </Badge>
  );

  return showDetails ? (
    <Popover>
      <PopoverTrigger asChild>
        {content}
      </PopoverTrigger>
      <PopoverContent align="end">
        <StatusDetails />
      </PopoverContent>
    </Popover>
  ) : content;
};

export default ConnectionStatusIndicator;
