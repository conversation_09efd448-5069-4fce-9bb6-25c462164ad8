import React from 'react';
import { useAuthErrorHandler } from '@/hooks/useAuthErrorHandler';

interface AuthErrorHandlerProps {
  children: React.ReactNode;
}

/**
 * Componente que ativa o tratamento de erros de autenticação
 * Deve ser usado dentro do AuthProvider
 */
export const AuthErrorHandler: React.FC<AuthErrorHandlerProps> = ({ children }) => {
  useAuthErrorHandler();
  return <>{children}</>;
};
