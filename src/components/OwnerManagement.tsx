import React, { useState, useCallback, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

import { userService } from "@/services/api";
import { User, SearchUsersSuccessResponse } from "@/types/api";
import { toast } from "sonner";
import { Users, Search, Loader2, Check, UserPlus, UserX } from "lucide-react";
import { cn } from "@/lib/utils";

interface OwnerManagementProps {
  companyExternalId: string;
  currentOwner?: User | null;
}

const OwnerManagement: React.FC<OwnerManagementProps> = ({
  companyExternalId,
  currentOwner
}) => {
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Current owner is passed as prop from parent component

  // Mutation para vincular usuário à empresa
  const linkUserMutation = useMutation({
    mutationFn: (userExternalId: string) =>
      userService.linkUserToCompany(companyExternalId, userExternalId),
    onSuccess: () => {
      toast.success("Usuário vinculado como proprietário com sucesso!");
      // Invalidate multiple related queries to ensure data refresh
      queryClient.invalidateQueries({ queryKey: ["company", companyExternalId] });
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      // Force refetch the company data to get updated owner information
      queryClient.refetchQueries({ queryKey: ["company", companyExternalId] });
      setConfirmDialogOpen(false);
      setSelectedUser(null);
      setSearchQuery("");
      setSearchResults([]);
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao vincular usuário";
      toast.error(errorMsg);
    },
  });

  // Função para buscar usuários
  const searchUsers = useCallback(async (query: string) => {
    if (!query.trim() || query.length < 3) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await userService.searchUsers(query);
      // Handle paginated response structure
      const responseData = response.data;

      // Check if it's a paginated response or direct array
      if (responseData.data && Array.isArray(responseData.data)) {
        setSearchResults(responseData.data);
      } else if (Array.isArray(responseData)) {
        setSearchResults(responseData);
      } else {
        setSearchResults([]);
      }
    } catch (error: any) {
      console.error("Erro ao buscar usuários:", error);
      if (error.response?.status === 404) {
        setSearchResults([]);
      } else {
        toast.error("Erro ao buscar usuários");
      }
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Effect para busca com debounce
  useEffect(() => {
    // Clear previous timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Set new timer
    const timer = setTimeout(() => {
      searchUsers(searchQuery);
    }, 500);

    setDebounceTimer(timer);

    // Cleanup function
    return () => {
      clearTimeout(timer);
    };
  }, [searchQuery]); // Only depend on searchQuery

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, []);

  // Função para formatar CPF
  const formatCPF = (cpf: string) => {
    if (!cpf) return '';
    const cleanCPF = cpf.replace(/\D/g, '');
    if (cleanCPF.length !== 11) return cpf;
    return cleanCPF.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  };

  // Função para selecionar usuário
  const handleSelectUser = (user: User) => {
    // Verificar se o usuário já é o proprietário atual
    if (currentOwner && user.external_id === currentOwner.external_id) {
      toast.info("Este usuário já é o proprietário atual da empresa");
      return;
    }

    setSelectedUser(user);
    setConfirmDialogOpen(true);
  };

  // Função para confirmar vinculação
  const handleConfirmLink = () => {
    if (selectedUser) {
      linkUserMutation.mutate(selectedUser.external_id);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2" size={20} />
          Gerenciamento de Proprietário
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Owner Display */}
        {currentOwner ? (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-green-900 mb-2">Proprietário Atual</h4>
                <div className="space-y-1">
                  <p className="text-sm text-green-700 font-medium">{currentOwner.name}</p>
                  <p className="text-xs text-green-600">{currentOwner.email}</p>
                  {(currentOwner.cpf || currentOwner.document) && (
                    <p className="text-xs text-green-600">CPF: {formatCPF(currentOwner.cpf || currentOwner.document || '')}</p>
                  )}
                  {currentOwner.phone_numbers && currentOwner.phone_numbers.length > 0 && (
                    <p className="text-xs text-green-600">Tel: {currentOwner.phone_numbers[0]}</p>
                  )}
                </div>
              </div>
              <div className="flex flex-col items-end space-y-2">
                <Badge variant="default" className="bg-green-600">
                  <Check size={12} className="mr-1" />
                  Vinculado
                </Badge>
              </div>
            </div>
          </div>
        ) : (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-center text-gray-500">
              <UserX size={24} className="mr-2" />
              <span>Nenhum proprietário vinculado</span>
            </div>
          </div>
        )}

        {/* User Search */}
        <div className="space-y-2">
          <label className="text-sm font-medium">
            Buscar e Vincular Novo Proprietário
          </label>

          {/* Search Input */}
          <div className="relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Digite CPF, email ou nome para buscar..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-10"
            />
            {isSearching && (
              <Loader2 size={16} className="absolute right-3 top-1/2 transform -translate-y-1/2 animate-spin text-gray-400" />
            )}
          </div>

          {/* Search Results */}
          {searchQuery.length >= 3 && (
            <div className="border rounded-md bg-white shadow-sm max-h-60 overflow-y-auto">
              {isSearching ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 size={20} className="animate-spin mr-2" />
                  <span className="text-sm text-gray-500">Buscando usuários...</span>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="p-2">
                  {searchResults.map((user) => (
                    <div
                      key={user.external_id}
                      onClick={() => handleSelectUser(user)}
                      className="flex items-center justify-between p-3 rounded-md hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                    >
                      <div className="flex-1">
                        <p className="font-medium text-sm">{user.name}</p>
                        <p className="text-xs text-gray-500">{user.email}</p>
                        {(user.cpf || user.document) && (
                          <p className="text-xs text-gray-400">
                            CPF: {formatCPF(user.cpf || user.document || '')}
                          </p>
                        )}
                        {user.phone_numbers && user.phone_numbers.length > 0 && (
                          <p className="text-xs text-gray-400">
                            Tel: {user.phone_numbers[0]}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center ml-3">
                        {currentOwner && user.external_id === currentOwner.external_id ? (
                          <Badge variant="default" className="text-xs">
                            Atual
                          </Badge>
                        ) : (
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <UserPlus size={14} className="text-gray-400" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <Search size={24} className="mx-auto text-gray-400 mb-2" />
                    <p className="text-sm text-gray-500">Nenhum usuário encontrado</p>
                    <p className="text-xs text-gray-400">Tente buscar por CPF, email ou nome</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {searchQuery.length > 0 && searchQuery.length < 3 && (
            <p className="text-xs text-gray-500">
              Digite pelo menos 3 caracteres para buscar usuários
            </p>
          )}
        </div>

        {/* Confirmation Dialog */}
        <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirmar Vinculação de Proprietário</DialogTitle>
              <DialogDescription>
                Tem certeza que deseja vincular este usuário como proprietário da empresa?
                {currentOwner && " Isso substituirá o proprietário atual."}
              </DialogDescription>
            </DialogHeader>
            
            {selectedUser && (
              <div className="py-4">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900">Usuário Selecionado</h4>
                  <p className="text-sm text-blue-700">{selectedUser.name}</p>
                  <p className="text-xs text-blue-600">{selectedUser.email}</p>
                  {(selectedUser.cpf || selectedUser.document) && (
                    <p className="text-xs text-blue-600">
                      CPF: {formatCPF(selectedUser.cpf || selectedUser.document || '')}
                    </p>
                  )}
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setConfirmDialogOpen(false)}
                disabled={linkUserMutation.isPending}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleConfirmLink}
                disabled={linkUserMutation.isPending}
              >
                {linkUserMutation.isPending ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Vinculando...
                  </>
                ) : (
                  <>
                    <UserPlus size={16} className="mr-2" />
                    Confirmar Vinculação
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default OwnerManagement;
