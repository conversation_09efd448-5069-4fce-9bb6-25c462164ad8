import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Bell, BellRing } from 'lucide-react';

interface BackgroundNotificationIndicatorProps {
  className?: string;
}

/**
 * Component to show when background notifications are being processed
 */
const BackgroundNotificationIndicator: React.FC<BackgroundNotificationIndicatorProps> = ({ 
  className = "" 
}) => {
  const [hasUnprocessedNotifications, setHasUnprocessedNotifications] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Check for unprocessed background notifications
  const checkBackgroundNotifications = () => {
    try {
      const storedNotifications = localStorage.getItem('fcm_background_notifications');
      if (!storedNotifications) {
        setHasUnprocessedNotifications(false);
        return;
      }

      const notifications = JSON.parse(storedNotifications);
      const unprocessedCount = notifications.filter((n: any) => !n.processed).length;
      setHasUnprocessedNotifications(unprocessedCount > 0);
      
      return unprocessedCount;
    } catch (error) {
      console.error('Error checking background notifications:', error);
      setHasUnprocessedNotifications(false);
      return 0;
    }
  };

  // Listen for visibility changes to update indicator
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        setIsProcessing(true);
        // Check after a short delay to allow processing
        setTimeout(() => {
          checkBackgroundNotifications();
          setIsProcessing(false);
        }, 500);
      } else {
        // When tab becomes hidden, check for notifications
        checkBackgroundNotifications();
      }
    };

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'fcm_background_notifications') {
        checkBackgroundNotifications();
      }
    };

    // Initial check
    checkBackgroundNotifications();

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Listen for localStorage changes
    window.addEventListener('storage', handleStorageChange);

    // Periodic check every 30 seconds
    const interval = setInterval(checkBackgroundNotifications, 30000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  // Don't render if no unprocessed notifications and not processing
  if (!hasUnprocessedNotifications && !isProcessing) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {isProcessing ? (
        <Badge variant="secondary" className="animate-pulse">
          <BellRing className="w-3 h-3 mr-1" />
          Processando...
        </Badge>
      ) : hasUnprocessedNotifications ? (
        <Badge variant="destructive">
          <Bell className="w-3 h-3 mr-1" />
          Novas notificações
        </Badge>
      ) : null}
    </div>
  );
};

export default BackgroundNotificationIndicator;
