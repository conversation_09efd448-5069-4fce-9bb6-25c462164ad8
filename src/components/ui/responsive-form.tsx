import React from 'react';
import { cn } from '@/lib/utils';
import { useScreenSize } from '@/hooks/use-mobile';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface ResponsiveFormProps {
  children: React.ReactNode;
  className?: string;
  onSubmit?: (e: React.FormEvent) => void;
}

interface ResponsiveFormSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

interface ResponsiveFormFieldProps {
  label: string;
  children: React.ReactNode;
  error?: string;
  required?: boolean;
  className?: string;
  fullWidth?: boolean;
}

interface ResponsiveFormActionsProps {
  children: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right';
}

/**
 * Responsive form container with proper spacing and layout
 */
export const ResponsiveForm: React.FC<ResponsiveFormProps> = ({ 
  children, 
  className,
  onSubmit 
}) => {
  const screenSize = useScreenSize();
  
  return (
    <form 
      onSubmit={onSubmit}
      className={cn(
        "space-y-6 sm:space-y-8",
        screenSize === 'mobile' ? "px-4" : "px-0",
        className
      )}
    >
      {children}
    </form>
  );
};

/**
 * Form section with responsive title and description
 */
export const ResponsiveFormSection: React.FC<ResponsiveFormSectionProps> = ({ 
  title,
  description,
  children,
  className 
}) => {
  return (
    <div className={cn("space-y-4 sm:space-y-6", className)}>
      {(title || description) && (
        <div className="space-y-1 sm:space-y-2">
          {title && (
            <h3 className="text-lg sm:text-xl font-medium text-gray-900">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm sm:text-base text-gray-600">
              {description}
            </p>
          )}
        </div>
      )}
      <div className="space-y-4 sm:space-y-6">
        {children}
      </div>
    </div>
  );
};

/**
 * Responsive form field with label, input, and error handling
 */
export const ResponsiveFormField: React.FC<ResponsiveFormFieldProps> = ({ 
  label,
  children,
  error,
  required = false,
  className,
  fullWidth = true
}) => {
  const screenSize = useScreenSize();
  
  return (
    <div className={cn(
      "space-y-2",
      !fullWidth && screenSize !== 'mobile' && "max-w-md",
      className
    )}>
      <Label className="text-sm sm:text-base font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      <div className="relative">
        {children}
      </div>
      
      {error && (
        <p className="text-sm text-red-600 mt-1">
          {error}
        </p>
      )}
    </div>
  );
};

/**
 * Responsive form actions with proper button layout
 */
export const ResponsiveFormActions: React.FC<ResponsiveFormActionsProps> = ({ 
  children,
  className,
  align = 'right'
}) => {
  const screenSize = useScreenSize();
  
  const getAlignmentClasses = () => {
    if (screenSize === 'mobile') {
      return "flex flex-col space-y-3";
    }
    
    switch (align) {
      case 'left':
        return "flex flex-row space-x-3 justify-start";
      case 'center':
        return "flex flex-row space-x-3 justify-center";
      case 'right':
      default:
        return "flex flex-row space-x-3 justify-end";
    }
  };

  return (
    <div className={cn(
      "pt-6 sm:pt-8 border-t border-gray-200",
      getAlignmentClasses(),
      className
    )}>
      {children}
    </div>
  );
};

/**
 * Responsive input with proper sizing
 */
interface ResponsiveInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
}

export const ResponsiveInput: React.FC<ResponsiveInputProps> = ({ 
  className,
  error,
  ...props 
}) => {
  return (
    <Input
      className={cn(
        "h-10 sm:h-11 text-sm sm:text-base",
        error && "border-red-500 focus:border-red-500 focus:ring-red-500",
        className
      )}
      {...props}
    />
  );
};

/**
 * Responsive textarea with proper sizing
 */
interface ResponsiveTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
}

export const ResponsiveTextarea: React.FC<ResponsiveTextareaProps> = ({ 
  className,
  error,
  ...props 
}) => {
  return (
    <Textarea
      className={cn(
        "min-h-[80px] sm:min-h-[100px] text-sm sm:text-base",
        error && "border-red-500 focus:border-red-500 focus:ring-red-500",
        className
      )}
      {...props}
    />
  );
};

/**
 * Responsive button with proper sizing and touch targets
 */
interface ResponsiveButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'default' | 'lg';
  fullWidth?: boolean;
  loading?: boolean;
}

export const ResponsiveButton: React.FC<ResponsiveButtonProps> = ({ 
  children,
  className,
  fullWidth = false,
  loading = false,
  disabled,
  ...props 
}) => {
  const screenSize = useScreenSize();
  
  return (
    <Button
      className={cn(
        "min-h-[44px] touch-manipulation", // Ensure minimum touch target
        screenSize === 'mobile' && fullWidth && "w-full",
        screenSize === 'mobile' ? "text-base px-6 py-3" : "text-sm sm:text-base",
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
      )}
      {children}
    </Button>
  );
};

/**
 * Responsive form grid for side-by-side fields
 */
interface ResponsiveFormGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3;
  className?: string;
}

export const ResponsiveFormGrid: React.FC<ResponsiveFormGridProps> = ({ 
  children,
  columns = 2,
  className 
}) => {
  const getGridClasses = () => {
    switch (columns) {
      case 1:
        return "grid grid-cols-1 gap-4 sm:gap-6";
      case 2:
        return "grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6";
      case 3:
        return "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6";
      default:
        return "grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6";
    }
  };

  return (
    <div className={cn(getGridClasses(), className)}>
      {children}
    </div>
  );
};

/**
 * Responsive form card wrapper
 */
interface ResponsiveFormCardProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
}

export const ResponsiveFormCard: React.FC<ResponsiveFormCardProps> = ({ 
  children,
  title,
  description,
  className 
}) => {
  return (
    <div className={cn(
      "bg-white shadow-sm border border-gray-200 rounded-lg overflow-hidden",
      className
    )}>
      {(title || description) && (
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
          {title && (
            <h3 className="text-lg sm:text-xl font-medium text-gray-900">
              {title}
            </h3>
          )}
          {description && (
            <p className="mt-1 text-sm sm:text-base text-gray-600">
              {description}
            </p>
          )}
        </div>
      )}
      <div className="px-4 py-5 sm:p-6">
        {children}
      </div>
    </div>
  );
};
