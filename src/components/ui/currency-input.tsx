import React, { useState, useEffect, forwardRef } from 'react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface CurrencyInputProps {
  value?: number; // Value in centavos
  onValueChange?: (value: number) => void; // Returns value in centavos
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  id?: string;
  name?: string;
}

/**
 * Brazilian Currency Input Component
 * 
 * Features:
 * - Right-to-left input (digits inserted from rightmost position)
 * - Automatic decimal placement (2 digits from right)
 * - Brazilian currency format (R$ X.XXX,XX)
 * - Stores value as integer in centavos
 * 
 * Example behavior:
 * - User types "1" → Display "R$ 0,01" → Returns 1 centavo
 * - User types "12" → Display "R$ 0,12" → Returns 12 centavos
 * - User types "123" → Display "R$ 1,23" → Returns 123 centavos
 * - User types "1234" → Display "R$ 12,34" → Returns 1234 centavos
 */
export const CurrencyInput = forwardRef<HTMLInputElement, CurrencyInputProps>(
  ({ value = 0, onValueChange, placeholder = "R$ 0,00", disabled, className, id, name }, ref) => {
    const [displayValue, setDisplayValue] = useState('');
    const [internalValue, setInternalValue] = useState(0); // Value in centavos

    // Format centavos to Brazilian currency display
    const formatCurrency = (centavos: number): string => {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(centavos / 100);
    };

    // Update display when value prop changes
    useEffect(() => {
      setInternalValue(value);
      setDisplayValue(formatCurrency(value));
    }, [value]);

    // Handle key press for right-to-left input
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Allow navigation keys
      if (['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
        if (e.key === 'Backspace') {
          e.preventDefault();
          // Remove rightmost digit (divide by 10 and floor)
          const newValue = Math.floor(internalValue / 10);
          setInternalValue(newValue);
          setDisplayValue(formatCurrency(newValue));
          onValueChange?.(newValue);
        }
        return;
      }

      // Allow Ctrl+A, Ctrl+C, Ctrl+V, etc.
      if (e.ctrlKey || e.metaKey) {
        return;
      }

      // Only allow digits
      if (!/^\d$/.test(e.key)) {
        e.preventDefault();
        return;
      }

      e.preventDefault();

      // Add digit to the right (multiply by 10 and add new digit)
      const digit = parseInt(e.key);
      const newValue = (internalValue * 10) + digit;
      
      // Limit to reasonable maximum (R$ 999.999,99 = 99999999 centavos)
      if (newValue > 99999999) {
        return;
      }

      setInternalValue(newValue);
      setDisplayValue(formatCurrency(newValue));
      onValueChange?.(newValue);
    };

    // Handle paste events
    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
      e.preventDefault();
      
      const pastedText = e.clipboardData.getData('text');
      const digitsOnly = pastedText.replace(/\D/g, '');
      
      if (digitsOnly) {
        const newValue = parseInt(digitsOnly);
        if (newValue <= 99999999) {
          setInternalValue(newValue);
          setDisplayValue(formatCurrency(newValue));
          onValueChange?.(newValue);
        }
      }
    };

    // Handle focus to select all
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      // Position cursor at the end
      setTimeout(() => {
        e.target.setSelectionRange(e.target.value.length, e.target.value.length);
      }, 0);
    };

    // Prevent manual text selection/editing
    const handleSelect = (e: React.SyntheticEvent<HTMLInputElement>) => {
      const target = e.target as HTMLInputElement;
      // Always position cursor at the end
      setTimeout(() => {
        target.setSelectionRange(target.value.length, target.value.length);
      }, 0);
    };

    return (
      <Input
        ref={ref}
        id={id}
        name={name}
        type="text"
        value={displayValue}
        placeholder={placeholder}
        disabled={disabled}
        className={cn("text-right font-mono", className)}
        onKeyDown={handleKeyDown}
        onPaste={handlePaste}
        onFocus={handleFocus}
        onSelect={handleSelect}
        onChange={() => {}} // Controlled by keyDown handler
        autoComplete="off"
      />
    );
  }
);

CurrencyInput.displayName = 'CurrencyInput';
