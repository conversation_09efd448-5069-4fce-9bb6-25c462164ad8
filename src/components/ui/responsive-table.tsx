import React from 'react';
import { cn } from '@/lib/utils';
import { useScreenSize } from '@/hooks/use-mobile';

interface ResponsiveTableProps {
  children: React.ReactNode;
  className?: string;
}

interface ResponsiveTableHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface ResponsiveTableCellProps {
  children: React.ReactNode;
  className?: string;
  hideOnMobile?: boolean;
  hideOnTablet?: boolean;
  priority?: 'high' | 'medium' | 'low';
  label?: string; // For mobile card view
}

interface ResponsiveTableRowProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

/**
 * Responsive table wrapper that handles overflow and mobile optimization
 */
export const ResponsiveTable: React.FC<ResponsiveTableProps> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={cn("overflow-x-auto -mx-4 sm:mx-0", className)}>
      <div className="inline-block min-w-full align-middle">
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          {children}
        </div>
      </div>
    </div>
  );
};

/**
 * Responsive table header with proper sizing
 */
export const ResponsiveTableHeader: React.FC<ResponsiveTableHeaderProps> = ({ 
  children, 
  className 
}) => {
  return (
    <thead className={cn("bg-gray-50", className)}>
      {children}
    </thead>
  );
};

/**
 * Responsive table cell that can hide on different screen sizes
 */
export const ResponsiveTableCell: React.FC<ResponsiveTableCellProps> = ({ 
  children, 
  className,
  hideOnMobile = false,
  hideOnTablet = false,
  priority = 'medium',
  label
}) => {
  const screenSize = useScreenSize();
  
  // Determine visibility based on screen size and priority
  const getVisibilityClasses = () => {
    if (hideOnMobile && screenSize === 'mobile') return 'hidden';
    if (hideOnTablet && screenSize === 'tablet') return 'hidden';
    
    // Priority-based hiding for mobile
    if (screenSize === 'mobile') {
      if (priority === 'low') return 'hidden';
      if (priority === 'medium') return 'hidden sm:table-cell';
    }
    
    // Priority-based hiding for tablet
    if (screenSize === 'tablet') {
      if (priority === 'low') return 'hidden md:table-cell';
    }
    
    return '';
  };

  return (
    <td className={cn(
      "px-3 py-4 text-sm text-gray-900 sm:px-6",
      getVisibilityClasses(),
      className
    )}>
      {label && screenSize === 'mobile' && (
        <span className="block text-xs font-medium text-gray-500 mb-1">
          {label}:
        </span>
      )}
      {children}
    </td>
  );
};

/**
 * Responsive table row with touch-friendly interactions
 */
export const ResponsiveTableRow: React.FC<ResponsiveTableRowProps> = ({ 
  children, 
  className,
  onClick
}) => {
  return (
    <tr 
      className={cn(
        "hover:bg-gray-50 transition-colors",
        onClick && "cursor-pointer touch-manipulation",
        className
      )}
      onClick={onClick}
    >
      {children}
    </tr>
  );
};

/**
 * Mobile-optimized card view for table data
 */
interface MobileCardViewProps {
  data: Array<{
    label: string;
    value: React.ReactNode;
    priority?: 'high' | 'medium' | 'low';
  }>;
  actions?: React.ReactNode;
  className?: string;
}

export const MobileCardView: React.FC<MobileCardViewProps> = ({ 
  data, 
  actions,
  className 
}) => {
  const screenSize = useScreenSize();
  
  if (screenSize !== 'mobile') return null;
  
  return (
    <div className={cn(
      "bg-white border border-gray-200 rounded-lg p-4 space-y-3 shadow-sm",
      className
    )}>
      {data
        .filter(item => item.priority !== 'low') // Hide low priority items on mobile
        .map((item, index) => (
          <div key={index} className="flex justify-between items-start">
            <span className="text-sm font-medium text-gray-500 min-w-0 flex-1">
              {item.label}:
            </span>
            <span className="text-sm text-gray-900 ml-2 text-right">
              {item.value}
            </span>
          </div>
        ))}
      {actions && (
        <div className="pt-3 border-t border-gray-100">
          {actions}
        </div>
      )}
    </div>
  );
};

/**
 * Responsive pagination component
 */
interface ResponsivePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export const ResponsivePagination: React.FC<ResponsivePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  className
}) => {
  const screenSize = useScreenSize();
  
  const getVisiblePages = () => {
    const maxVisible = screenSize === 'mobile' ? 3 : 7;
    const pages = [];
    
    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
      const end = Math.min(totalPages, start + maxVisible - 1);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  };

  return (
    <div className={cn("flex items-center justify-between px-4 sm:px-0", className)}>
      <div className="flex-1 flex justify-between sm:hidden">
        <button
          onClick={() => onPageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Anterior
        </button>
        <span className="text-sm text-gray-700 self-center">
          {currentPage} de {totalPages}
        </span>
        <button
          onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Próximo
        </button>
      </div>
      
      <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p className="text-sm text-gray-700">
            Página <span className="font-medium">{currentPage}</span> de{' '}
            <span className="font-medium">{totalPages}</span>
          </p>
        </div>
        <div>
          <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
            <button
              onClick={() => onPageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            
            {getVisiblePages().map((page) => (
              <button
                key={page}
                onClick={() => onPageChange(page)}
                className={cn(
                  "relative inline-flex items-center px-4 py-2 border text-sm font-medium",
                  page === currentPage
                    ? "z-10 bg-primary border-primary text-white"
                    : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                )}
              >
                {page}
              </button>
            ))}
            
            <button
              onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Próximo
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
};
