
import React, { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { companyService } from "@/services/api";

import { formatCurrency, formatDateTime } from "@/utils/formatters";
import {
  DollarSign,
  CreditCard,
  TrendingUp,
  RefreshCw,
  Loader2,
  AlertCircle,
  Calendar,
  Hash,
  Target
} from "lucide-react";
import WithdrawalModal from "./WithdrawalModal";
import { useAuth } from "@/contexts/AuthContext";

interface CompanyBillingTabProps {
  companyId?: string; // Optional for future use when we need company-specific data
}

const CompanyBillingTab: React.FC<CompanyBillingTabProps> = ({ companyId }) => {
  const [withdrawalModalOpen, setWithdrawalModalOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isAutoRefreshing, setIsAutoRefreshing] = useState(false);
  const { userRole } = useAuth();
  const isPartner = userRole === 'partner';
  const queryClient = useQueryClient();

  // Fetch balance data
  const {
    data: balanceResponse,
    isLoading: isLoadingBalance,
    error: balanceError,
    refetch: refetchBalance
  } = useQuery({
    queryKey: ["company-balance", companyId],
    queryFn: async () => {
      const response = await companyService.getBalance();
      return response;
    },
    staleTime: 30000, // 30 seconds
  });

  // Fetch withdrawal history
  const {
    data: withdrawalHistory,
    isLoading: isLoadingHistory,
    error: historyError,
    refetch: refetchHistory
  } = useQuery({
    queryKey: ["withdrawal-history", companyId],
    queryFn: async () => {
      return await companyService.getWithdrawalHistory(1, 10);
    },
    staleTime: 60000, // 1 minute
  });

  // Calculate summary statistics
  const summaryStats = React.useMemo(() => {
    if (!withdrawalHistory?.data) {
      return {
        totalWithdrawn: 0,
        totalWithdrawals: 0,
        averageWithdrawal: 0,
        completedWithdrawals: 0
      };
    }

    const allWithdrawals = withdrawalHistory.data.withdrawals;
    const completedWithdrawals = allWithdrawals.filter(w => w.status === 'CONFIRMED');
    const completedTotalAmount = completedWithdrawals.reduce((sum, w) => sum + w.amount, 0);

    // Calculate totals based on completed withdrawals only
    const totalWithdrawn = withdrawalHistory.data.total_amount;
    const totalWithdrawals = allWithdrawals.length;
    const completedCount = completedWithdrawals.length;
    const averageWithdrawal = completedCount > 0 ? completedTotalAmount / completedCount : 0;

    return {
      totalWithdrawn,
      totalWithdrawals,
      averageWithdrawal,
      completedWithdrawals: completedCount
    };
  }, [withdrawalHistory]);

  // Handle refresh with loading feedback
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([
        refetchBalance(),
        refetchHistory()
      ]);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle successful withdrawal - automatically refresh data
  const handleWithdrawalSuccess = async () => {
    console.log('🔄 [CompanyBillingTab] Withdrawal successful, refreshing data...');
    setIsAutoRefreshing(true);

    try {
      // Add a small delay to ensure backend has processed the withdrawal
      await new Promise(resolve => setTimeout(resolve, 500));

      // First, invalidate all related queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: ["company-balance"] });
      queryClient.invalidateQueries({ queryKey: ["withdrawal-history"] });

      // If we have a specific companyId, also invalidate those specific queries
      if (companyId) {
        queryClient.invalidateQueries({ queryKey: ["company-balance", companyId] });
        queryClient.invalidateQueries({ queryKey: ["withdrawal-history", companyId] });
      }

      // Then trigger immediate refetch of both balance and history data
      const refreshResults = await Promise.allSettled([
        refetchBalance(),
        refetchHistory()
      ]);

      // Log any individual refresh failures
      refreshResults.forEach((result, index) => {
        const queryName = index === 0 ? 'balance' : 'history';
        if (result.status === 'rejected') {
          console.error(`❌ [CompanyBillingTab] Failed to refresh ${queryName} data:`, result.reason);
        }
      });

      console.log('✅ [CompanyBillingTab] Data refresh completed after withdrawal');
    } catch (error) {
      console.error('❌ [CompanyBillingTab] Error refreshing data after withdrawal:', error);
      // Don't show error to user as the withdrawal was successful
      // The data will be refreshed on next manual refresh or page reload
    } finally {
      setIsAutoRefreshing(false);
    }
  };

  // Get status badge variant based on withdrawal status
  const getStatusBadgeVariant = (status: 'CREATED' | 'CONFIRMED' | 'FAILED'): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'CONFIRMED':
        return 'default'; // green for completed
      case 'CREATED':
        return 'secondary'; // gray for pending
      case 'FAILED':
        return 'destructive'; // red for failed
      default:
        return 'outline';
    }
  };

  // Get status label in Portuguese
  const getStatusLabel = (status: 'CREATED' | 'CONFIRMED' | 'FAILED') => {
    switch (status) {
      case 'CONFIRMED':
        return 'Concluído';
      case 'CREATED':
        return 'Pendente';
      case 'FAILED':
        return 'Falhou';
      default:
        return 'Desconhecido';
    }
  };

  // Process balance data - find the specific company or use aggregated data
  const balanceData = companyId
    ? balanceResponse?.data?.find(company => company.external_id === companyId)
    : balanceResponse?.data?.[0]; // Fallback to first company if no specific companyId

  const totalBalance = balanceResponse?.data?.reduce((sum, company) => sum + company.balance, 0) || 0;

  // For display: show individual company balance when viewing specific company, total when general view
  const displayBalance = companyId && balanceData ? balanceData.balance : totalBalance;

  // For withdrawal eligibility: use company-specific balance when companyId provided, total otherwise
  const withdrawalBalance = companyId && balanceData ? balanceData.balance : totalBalance;
  const canWithdraw = withdrawalBalance >= 50000;

  const partnerName = balanceData?.name || '';
  const pixKey = balanceData?.pix_key || '';

  return (
    <div className="space-y-6">
      {/* Header with Refresh Button */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Faturamento</h3>
          <p className="text-sm text-muted-foreground">
            Gerencie seu saldo e histórico de saques
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoadingBalance || isLoadingHistory || isRefreshing || isAutoRefreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${(isLoadingBalance || isLoadingHistory || isRefreshing || isAutoRefreshing) ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Atualizando...' : isAutoRefreshing ? 'Atualizando automaticamente...' : 'Atualizar'}
        </Button>
      </div>

      {/* Section A: Current Balance Display */}
      <Card>
        <CardHeader>
        </CardHeader>
        <CardContent>
          {isLoadingBalance || isAutoRefreshing ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>{isAutoRefreshing ? 'Atualizando dados após saque...' : 'Carregando saldo...'}</span>
            </div>
          ) : balanceError ? (
            <div className="flex items-center justify-center py-8 text-red-500">
              <AlertCircle className="h-6 w-6 mr-2" />
              <span>Erro ao carregar saldo</span>
            </div>
          ) : companyId && !balanceData ? (
            <div className="flex items-center justify-center py-8 text-yellow-600">
              <AlertCircle className="h-6 w-6 mr-2" />
              <span>Empresa não encontrada nos dados de saldo</span>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">Parceiro</p>
                  <p className="text-lg font-semibold">{partnerName}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">Chave PIX</p>
                  <p className="text-sm font-mono break-all">{pixKey}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">
                    {companyId ? 'Saldo desta Empresa' : 'Saldo Total Disponível'}
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(displayBalance / 100)}
                  </p>
                </div>
              </div>

              {/* Withdrawal Button - Only for partners */}
              {isPartner && (
                <div className="pt-4 border-t">
                  <Button
                    onClick={() => setWithdrawalModalOpen(true)}
                    disabled={!canWithdraw}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <DollarSign className="mr-2 h-4 w-4" />
                    Sacar Saldo
                  </Button>
                  {!canWithdraw && (
                    <p className="text-sm text-muted-foreground mt-2">
                      Saldo mínimo para saque: R$ 500,00
                    </p>
                  )}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      {/* Section C: Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Total Sacado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(summaryStats.totalWithdrawn / 100)}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {summaryStats.completedWithdrawals} saques concluídos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
              <Hash className="h-4 w-4" />
              Total de Saques
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{summaryStats.totalWithdrawals}</p>
            <p className="text-xs text-muted-foreground mt-1">
              Incluindo pendentes e falhados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
              <Target className="h-4 w-4" />
              Saque Médio
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(summaryStats.averageWithdrawal / 100)}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Média dos saques concluídos
            </p>
          </CardContent>
        </Card>
      </div>
      {/* Section B: Withdrawal History Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Histórico de Saques
          </CardTitle>
          <CardDescription>
            Últimos saques realizados
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingHistory ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Carregando histórico...</span>
            </div>
          ) : historyError ? (
            <div className="flex items-center justify-center py-8 text-red-500">
              <AlertCircle className="h-6 w-6 mr-2" />
              <span>Erro ao carregar histórico</span>
            </div>
          ) : !withdrawalHistory?.data?.withdrawals.length ? (
            <div className="flex items-center justify-center py-8 text-muted-foreground">
              <Calendar className="h-6 w-6 mr-2" />
              <span>Nenhum saque realizado ainda</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-4 font-medium">Data/Hora</th>
                    <th className="text-left py-2 px-4 font-medium">Valor</th>
                    <th className="text-left py-2 px-4 font-medium">Chave Pix</th>
                    <th className="text-left py-2 px-4 font-medium">Status</th>
                    <th className="text-left py-2 px-4 font-medium">ID</th>
                  </tr>
                </thead>
                <tbody>
                  {withdrawalHistory.data.withdrawals.map((withdrawal) => (
                    <tr key={withdrawal.correlation_id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {formatDateTime(withdrawal.created_at)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-semibold">
                          {formatCurrency(withdrawal.amount / 100)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm font-mono">
                          {withdrawal.destination_pix_key}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge variant={getStatusBadgeVariant(withdrawal.status)}>
                          {getStatusLabel(withdrawal.status)}
                        </Badge>
                        {withdrawal.status === 'CONFIRMED' && withdrawal.finished_at && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Finalizado: {formatDateTime(withdrawal.finished_at)}
                          </div>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-xs text-muted-foreground font-mono">
                          {withdrawal.end_to_end_id}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Withdrawal Modal - Only for partners */}
      {isPartner && (
        <WithdrawalModal
          open={withdrawalModalOpen}
          onOpenChange={setWithdrawalModalOpen}
          companyId={companyId}
          onWithdrawalSuccess={handleWithdrawalSuccess}
        />
      )}
    </div>
  );
};

export default CompanyBillingTab;
