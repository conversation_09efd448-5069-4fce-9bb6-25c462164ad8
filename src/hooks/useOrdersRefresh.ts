import { useEffect, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { orderService } from '@/services/api';

interface UseOrdersRefreshProps {
  enabled?: boolean;
  queryKey: (string | number)[];
}

/**
 * Hook specifically for handling orders list refresh
 * Provides multiple mechanisms to ensure orders are always up to date
 */
export const useOrdersRefresh = ({ enabled = true, queryKey }: UseOrdersRefreshProps) => {
  const queryClient = useQueryClient();
  const lastRefreshRef = useRef<number>(0);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Force refresh orders with multiple strategies
   */
  const forceRefreshOrders = useCallback(async () => {
    if (!enabled) return;

    const now = Date.now();
    // Prevent too frequent refreshes (minimum 1 second between refreshes)
    if (now - lastRefreshRef.current < 1000) {
      console.log("🔄 Refresh throttled, too frequent");
      return;
    }

    lastRefreshRef.current = now;
    console.log("🔄 Force refreshing orders with multiple strategies");

    try {
      // Strategy 1: Invalidate and refetch
      await queryClient.invalidateQueries({
        queryKey,
        refetchType: 'all'
      });

      // Strategy 2: Force refetch
      await queryClient.refetchQueries({
        queryKey,
        type: 'all'
      });

      // Strategy 3: Remove and prefetch (nuclear option)
      queryClient.removeQueries({ queryKey });
      
      // Prefetch fresh data
      if (queryKey[0] === 'partner-orders') {
        await queryClient.prefetchQuery({
          queryKey,
          queryFn: () => orderService.getOrders(queryKey[1] as number || 1, 10),
          staleTime: 0
        });
      }

      console.log("🔄 Orders force refresh completed successfully");
    } catch (error) {
      console.error("🔄 Orders force refresh failed:", error);
    }
  }, [enabled, queryClient, queryKey]);

  /**
   * Handle visibility change with debouncing
   */
  const handleVisibilityChange = useCallback(() => {
    if (!enabled) return;

    console.log("🔄 [OrdersRefresh] Visibility change:", {
      visibilityState: document.visibilityState,
      hidden: document.hidden,
      timestamp: new Date().toISOString()
    });

    if (document.visibilityState === 'visible') {
      // Clear any existing timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      // Debounced refresh
      refreshTimeoutRef.current = setTimeout(() => {
        console.log("🔄 [OrdersRefresh] Tab became visible, refreshing orders");
        forceRefreshOrders();
      }, 200);
    }
  }, [enabled, forceRefreshOrders]);

  /**
   * Handle window focus
   */
  const handleWindowFocus = useCallback(() => {
    if (!enabled) return;

    console.log("🔄 [OrdersRefresh] Window focus detected");
    
    // Clear any existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Debounced refresh
    refreshTimeoutRef.current = setTimeout(() => {
      console.log("🔄 [OrdersRefresh] Window focused, refreshing orders");
      forceRefreshOrders();
    }, 200);
  }, [enabled, forceRefreshOrders]);

  /**
   * Check for background notifications and refresh if needed
   */
  const checkBackgroundNotifications = useCallback(() => {
    if (!enabled) return;

    try {
      const stored = localStorage.getItem('fcm_background_notifications');
      if (!stored) return;

      const notifications = JSON.parse(stored);
      const unprocessed = notifications.filter((n: any) => !n.processed);
      
      if (unprocessed.length > 0) {
        console.log("🔄 [OrdersRefresh] Found unprocessed background notifications, refreshing orders");
        forceRefreshOrders();
        
        // Mark notifications as processed
        notifications.forEach((n: any) => n.processed = true);
        localStorage.setItem('fcm_background_notifications', JSON.stringify(notifications));
      }
    } catch (error) {
      console.error("🔄 [OrdersRefresh] Error checking background notifications:", error);
    }
  }, [enabled, forceRefreshOrders]);

  // Set up event listeners
  useEffect(() => {
    if (!enabled) return;

    // Check for background notifications on mount
    checkBackgroundNotifications();

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Listen for window focus
    window.addEventListener('focus', handleWindowFocus);

    // Listen for storage changes (background notifications)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'fcm_background_notifications') {
        console.log("🔄 [OrdersRefresh] Background notifications storage changed");
        setTimeout(checkBackgroundNotifications, 100);
      }
    };
    
    window.addEventListener('storage', handleStorageChange);

    // Periodic check for background notifications (every 30 seconds)
    const interval = setInterval(checkBackgroundNotifications, 30000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleWindowFocus);
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
      
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [enabled, handleVisibilityChange, handleWindowFocus, checkBackgroundNotifications]);

  return {
    forceRefreshOrders,
    checkBackgroundNotifications
  };
};

export default useOrdersRefresh;
