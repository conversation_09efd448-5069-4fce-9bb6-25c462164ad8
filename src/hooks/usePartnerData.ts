import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { companyService } from '@/services/api';
import { GetMyCompaniesResponse, GetActiveCompanySuccessResponse } from '@/types/api';
import { useAuth } from '@/contexts/AuthContext';

interface PartnerCompanyData {
  external_id: string;
  cnpj?: string;
  name: string;
  email?: string;
  bio?: string;
  picture?: string;
  is_active: boolean;
  products?: Array<{
    external_id: string;
    name: string;
    brand: string;
    image?: string;
    price: number;
    discount: number;
    stock: number;
  }>;
  address?: {
    street: string;
    number: string;
    city: string;
    state: string;
    zip_code: string;
  };
  phone_numbers?: string[];
  delivery_modes?: string[];
  shipping_fee?: number;
}

export const usePartnerData = () => {
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const queryClient = useQueryClient();
  const [cachedCompanies, setCachedCompanies] = useState<PartnerCompanyData[]>([]);
  const [isLoadingFromCache, setIsLoadingFromCache] = useState(true);

  // Cache key based on partner external ID
  const cacheKey = user?.external_id ? `${user.external_id}-companies` : null;

  // Debug logging function
  const logDebug = useCallback((message: string, data?: any) => {
    console.log(`🔍 [usePartnerData] ${message}`, data || '');
  }, []);

  // Load cached data on mount - only when user is available and stable
  useEffect(() => {
    if (!authLoading && isAuthenticated && cacheKey) {
      logDebug('Loading cached data', { cacheKey });
      const cached = localStorage.getItem(cacheKey);
      if (cached) {
        try {
          const parsedData = JSON.parse(cached);
          setCachedCompanies(parsedData);
          logDebug('Cached data loaded successfully', { count: parsedData.length });
        } catch (error) {
          logDebug('Error parsing cached company data', error);
          localStorage.removeItem(cacheKey); // Remove corrupted cache
        }
      } else {
        logDebug('No cached data found');
      }
    }
    setIsLoadingFromCache(false);
  }, [cacheKey, authLoading, isAuthenticated, logDebug]);

  // Step 1: Get company external IDs - only when user is authenticated and stable
  const { data: myCompaniesResponse, isLoading: isLoadingCompanyIds, error: companyIdsError } = useQuery({
    queryKey: ["my-companies", user?.external_id],
    queryFn: async () => {
      logDebug('🚀 Calling GET /v1/company/my-companies', { userExternalId: user?.external_id });
      const response = await companyService.getMyCompanies();
      logDebug('✅ GET /v1/company/my-companies RAW response', response);
      logDebug('✅ GET /v1/company/my-companies response.data', response.data);
      logDebug('✅ GET /v1/company/my-companies response.data.data', response.data?.data);

      // Check if the response structure is correct
      if (response.data?.data) {
        logDebug('✅ Using response.data.data structure');
        return response.data.data as GetMyCompaniesResponse;
      } else if (response.data) {
        logDebug('✅ Using response.data structure');
        return response.data as GetMyCompaniesResponse;
      } else {
        logDebug('❌ Unexpected response structure', response);
        throw new Error('Invalid response structure from /v1/company/my-companies');
      }
    },
    enabled: !authLoading && isAuthenticated && !!user?.external_id,
    staleTime: 10 * 60 * 1000, // 10 minutes - longer stale time to prevent duplicate calls
    gcTime: 15 * 60 * 1000, // 15 minutes garbage collection time
    retry: 1, // Only retry once on failure
  });

  const companyIds = myCompaniesResponse?.company_external_ids || [];
  logDebug('🔍 Extracted company IDs', {
    myCompaniesResponse,
    company_external_ids: myCompaniesResponse?.company_external_ids,
    companyIds,
    companyIdsLength: companyIds.length
  });

  // Step 2: Fetch detailed company data for each ID
  const { data: companiesDetails, isLoading: isLoadingCompanies, error: companiesError } = useQuery({
    queryKey: ["companies-details", companyIds, user?.external_id],
    queryFn: async () => {
      if (!companyIds.length) {
        logDebug('No company IDs to fetch details for');
        return [];
      }

      logDebug('🚀 Fetching company details', { companyIds, count: companyIds.length });

      const companiesPromises = companyIds.map(async (externalId) => {
        logDebug(`🚀 Calling GET /v1/company/${externalId}`);
        const response = await companyService.getCompany(externalId);
        logDebug(`✅ GET /v1/company/${externalId} response`, {
          name: response.data.data.name,
          productsCount: response.data.data.products?.length || 0
        });
        return response.data as GetActiveCompanySuccessResponse;
      });

      const results = await Promise.all(companiesPromises);
      const companies = results.map(result => result.data);
      logDebug('✅ All company details fetched', { totalCompanies: companies.length });
      return companies;
    },
    enabled: !authLoading && isAuthenticated && companyIds.length > 0,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 1,
  });

  // Step 3: Cache the fetched data locally
  useEffect(() => {
    if (companiesDetails && companiesDetails.length > 0 && cacheKey) {
      logDebug('💾 Caching company data', { count: companiesDetails.length, cacheKey });

      const dataToCache: PartnerCompanyData[] = companiesDetails.map(company => ({
        external_id: company.external_id,
        name: company.name,
        picture: company.picture,
        is_active: company.is_active,
        products: company.products || [],
        address: company.address,
        delivery_modes: company.delivery_modes,
        shipping_fee: company.shipping_fee,
      }));

      try {
        localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
        setCachedCompanies(dataToCache);
        logDebug('✅ Data cached successfully');
      } catch (error) {
        logDebug('❌ Error caching data', error);
      }
    }
  }, [companiesDetails, cacheKey, logDebug]);

  // Use fresh data if available, otherwise use cached data
  const companies = companiesDetails || cachedCompanies;
  const isLoading = authLoading || isLoadingFromCache || isLoadingCompanyIds || isLoadingCompanies;

  // Calculate metrics from company data
  const metrics = {
    totalCompanies: companies.length,
    totalProducts: companies.reduce((total, company) => total + (company.products?.length || 0), 0),
    activeCompanies: companies.filter(company => company.is_active).length,
    companiesWithProducts: companies.filter(company => (company.products?.length || 0) > 0).length,
  };

  // Refresh function to force refetch
  const refreshData = useCallback(async () => {
    logDebug('🔄 Refreshing data - clearing cache and invalidating queries');

    if (cacheKey) {
      localStorage.removeItem(cacheKey);
      setCachedCompanies([]);
    }

    // Invalidate React Query cache
    await queryClient.invalidateQueries({ queryKey: ["my-companies"] });
    await queryClient.invalidateQueries({ queryKey: ["companies-details"] });

    logDebug('✅ Data refresh completed');
  }, [cacheKey, queryClient, logDebug]);

  // Debug effect to track state changes
  useEffect(() => {
    logDebug('State update', {
      authLoading,
      isAuthenticated,
      userExternalId: user?.external_id,
      isLoadingCompanyIds,
      isLoadingCompanies,
      companyIdsCount: companyIds.length,
      companiesCount: companies.length,
      hasCache: cachedCompanies.length > 0,
      errors: {
        companyIdsError: companyIdsError?.message,
        companiesError: companiesError?.message,
      }
    });
  }, [
    authLoading, isAuthenticated, user?.external_id, isLoadingCompanyIds,
    isLoadingCompanies, companyIds.length, companies.length,
    cachedCompanies.length, companyIdsError, companiesError, logDebug
  ]);

  return {
    companies,
    metrics,
    isLoading,
    refreshData,
    hasCache: cachedCompanies.length > 0,
    companyIds,
    dashboardUrl: myCompaniesResponse?.dashboard_url,
    isAdmin: myCompaniesResponse?.dashboard_url === "/admin/dashboard",
    // Debug information
    debug: {
      authLoading,
      isAuthenticated,
      userExternalId: user?.external_id,
      isLoadingCompanyIds,
      isLoadingCompanies,
      companyIdsError: companyIdsError?.message,
      companiesError: companiesError?.message,
      cacheKey,
    }
  };
};
