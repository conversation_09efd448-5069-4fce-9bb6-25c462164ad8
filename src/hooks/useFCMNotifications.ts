import { useEffect, useState, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { fcmService, FCMNotificationPayload } from '@/services/fcmService';
import { isFirebaseConfigured } from '@/config/firebase';

export interface FCMState {
  isInitialized: boolean;
  isRegistered: boolean;
  hasPermission: boolean;
  permissionDenied: boolean;
  error: string | null;
  deviceId: string | null;
}

export interface FCMNotification {
  id: string;
  title: string;
  body: string;
  type: string;
  data: any;
  timestamp: Date;
  read: boolean;
}

interface UseFCMNotificationsProps {
  enabled?: boolean;
  onNotificationReceived?: (notification: FCMNotification) => void;
  onError?: (error: string) => void;
  onPermissionGranted?: () => void;
}

/**
 * FCM Notifications Hook - Partner Users Only
 * Handles PIX payment notifications and order updates for partner dashboard
 */
export const useFCMNotifications = ({
  enabled = true,
  onNotificationReceived,
  onError,
  onPermissionGranted
}: UseFCMNotificationsProps = {}) => {
  const navigate = useNavigate();
  
  const [state, setState] = useState<FCMState>({
    isInitialized: false,
    isRegistered: false,
    hasPermission: false,
    permissionDenied: false,
    error: null,
    deviceId: null
  });

  const [notifications, setNotifications] = useState<FCMNotification[]>([]);
  const initializationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitializingRef = useRef(false);

  /**
   * Handle foreground notifications
   */
  const handleForegroundNotification = useCallback((payload: FCMNotificationPayload) => {
    console.log('🔔 Handling foreground notification:', payload);
    console.log('🔔 Payload structure:', {
      hasNotification: !!payload.notification,
      notificationTitle: payload.notification?.title,
      notificationBody: payload.notification?.body,
      hasData: !!payload.data,
      dataKeys: payload.data ? Object.keys(payload.data) : [],
      dataType: payload.data?.type,
      fullData: payload.data
    });

    const notification: FCMNotification = {
      id: `fcm_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      title: payload.notification?.title || 'Izy Mercado',
      body: payload.notification?.body || 'Nova notificação',
      type: payload.data?.type || 'general',
      data: payload.data || {},
      timestamp: new Date(),
      read: false
    };

    console.log('🔔 Created FCM notification object:', notification);

    // Add to notifications list
    setNotifications(prev => [notification, ...prev]);

    // Show toast notification
    toast(notification.title, {
      description: notification.body,
      action: {
        label: 'Ver',
        onClick: () => handleNotificationClick(notification)
      },
      duration: 5000
    });

    // Call callback if provided
    onNotificationReceived?.(notification);
  }, [onNotificationReceived]);

  /**
   * Handle notification click navigation
   */
  const handleNotificationClick = useCallback((notification: FCMNotification) => {
    console.log('Handling notification click:', notification);

    // Mark as read
    setNotifications(prev => 
      prev.map(n => n.id === notification.id ? { ...n, read: true } : n)
    );

    // Navigate based on notification type
    switch (notification.type) {
      case 'payment_confirmed':
        navigate('/partner/orders');
        break;
      case 'new_order':
        navigate('/partner/orders');
        break;
      case 'order_status_update':
        if (notification.data.order_id) {
          navigate(`/partner/orders?order=${notification.data.order_id}`);
        } else {
          navigate('/partner/orders');
        }
        break;
      default:
        navigate('/partner/orders');
    }
  }, [navigate]);

  /**
   * Initialize FCM service
   */
  const initialize = useCallback(async (forceRetry: boolean = false) => {
    if (!enabled) {
      console.log('FCM notifications disabled');
      return;
    }

    // Don't attempt initialization if permissions were already denied, unless it's a forced retry
    if (state.permissionDenied && !forceRetry) {
      console.log('🔔 FCM initialization skipped - permissions previously denied. Use forceRetry=true to override.');
      return;
    }

    // If this is a forced retry, reset the permission denied state
    if (forceRetry && state.permissionDenied) {
      console.log('🔔 Forcing FCM initialization retry - resetting permission state');
      setState(prev => ({
        ...prev,
        permissionDenied: false,
        error: null
      }));
    }

    // Check if Firebase is properly configured
    if (!isFirebaseConfigured()) {
      const configWarning = 'Firebase not configured - push notifications disabled. See CLOUDFLARE_DEPLOYMENT_GUIDE.md for setup instructions.';
      console.warn(configWarning);
      setState(prev => ({
        ...prev,
        error: configWarning,
        isInitialized: false,
        isRegistered: false
      }));
      // Don't call onError for missing config - it's not a critical error
      return;
    }

    // Prevent multiple simultaneous initializations
    if (isInitializingRef.current) {
      console.log('🔔 FCM initialization already in progress, skipping');
      return;
    }

    // Check if already initialized
    if (state.isInitialized && state.isRegistered) {
      console.log('🔔 FCM already initialized, skipping');
      return;
    }

    // Clear any existing timeout
    if (initializationTimeoutRef.current) {
      clearTimeout(initializationTimeoutRef.current);
      initializationTimeoutRef.current = null;
    }

    isInitializingRef.current = true;

    try {
      setState(prev => ({ ...prev, error: null }));

      // Check permission
      const permission = await fcmService.requestPermission();
      const hasPermission = permission === 'granted';

      if (!hasPermission) {
        setState(prev => ({
          ...prev,
          hasPermission: false,
          permissionDenied: true,
          error: 'Notification permission not granted'
        }));
        onError?.('Notification permission not granted');
        return;
      }

      // Initialize FCM service
      const initialized = await fcmService.initialize();
      const status = fcmService.getRegistrationStatus();

      setState(prev => ({
        ...prev,
        isInitialized: initialized,
        isRegistered: status.isRegistered,
        hasPermission: true,
        permissionDenied: false, // Reset permission denied flag on successful initialization
        deviceId: status.deviceId,
        error: initialized ? null : 'Failed to initialize FCM service'
      }));

      if (initialized) {
        // Setup foreground listener
        fcmService.setupForegroundListener(handleForegroundNotification);
        console.log('🔔 FCM notifications ready');

        // Call success callback when permission is granted and FCM is initialized
        onPermissionGranted?.();
      } else {
        onError?.('Failed to initialize FCM service');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error initializing FCM:', error);
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isInitialized: false,
        isRegistered: false
      }));
      
      onError?.(errorMessage);
    } finally {
      isInitializingRef.current = false;
    }
  }, [enabled, handleForegroundNotification, onError, onPermissionGranted, state.permissionDenied]);

  /**
   * Manual initialization (for button clicks) - always retries even if permissions were denied
   */
  const initializeManually = useCallback(async () => {
    console.log('🔔 Manual FCM initialization requested');
    await initialize(true); // Force retry
  }, [initialize]);

  /**
   * Reset permission denied state and retry initialization
   */
  const resetPermissionState = useCallback(() => {
    console.log('🔔 Resetting FCM permission state');
    setState(prev => ({
      ...prev,
      permissionDenied: false,
      error: null
    }));
  }, []);

  /**
   * Refresh FCM token
   */
  const refreshToken = useCallback(async () => {
    try {
      const success = await fcmService.refreshToken();
      if (success) {
        const status = fcmService.getRegistrationStatus();
        setState(prev => ({
          ...prev,
          isRegistered: status.isRegistered,
          error: null
        }));
        console.log('🔔 FCM token refreshed');
      } else {
        throw new Error('Failed to refresh FCM token');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error refreshing FCM token:', error);
      setState(prev => ({ ...prev, error: errorMessage }));
      onError?.(errorMessage);
    }
  }, [onError]);

  /**
   * Cleanup FCM service
   */
  const cleanup = useCallback(async () => {
    try {
      await fcmService.deleteToken();
      setState({
        isInitialized: false,
        isRegistered: false,
        hasPermission: false,
        error: null,
        deviceId: null
      });
      setNotifications([]);
      console.log('🔔 FCM service cleaned up');
    } catch (error) {
      console.error('Error cleaning up FCM service:', error);
    }
  }, []);

  /**
   * Mark notification as read
   */
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
    );
  }, []);

  /**
   * Mark all notifications as read
   */
  const markAllAsRead = useCallback(() => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  }, []);

  /**
   * Clear all notifications
   */
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  /**
   * Get unread count
   */
  const unreadCount = notifications.filter(n => !n.read).length;

  // Initialize on mount with debounce
  useEffect(() => {
    if (enabled && !state.permissionDenied && !state.isInitialized) {
      // Clear any existing timeout
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
      }

      // Debounce initialization to prevent multiple rapid calls
      initializationTimeoutRef.current = setTimeout(() => {
        initialize();
      }, 500); // 500ms debounce
    }

    // Cleanup on unmount
    return () => {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
        initializationTimeoutRef.current = null;
      }

      if (enabled) {
        // Don't cleanup tokens on unmount, only on explicit logout
        console.log('🔔 FCM hook unmounting');
      }
    };
  }, [enabled, state.permissionDenied, state.isInitialized]);

  // Check for token refresh periodically
  useEffect(() => {
    if (!state.isRegistered) return;

    const checkTokenRefresh = () => {
      const status = fcmService.getRegistrationStatus();
      if (status.needsRefresh) {
        console.log('🔔 FCM token needs refresh');
        refreshToken();
      }
    };

    // Check immediately
    checkTokenRefresh();

    // Check every hour
    const interval = setInterval(checkTokenRefresh, 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, [state.isRegistered, refreshToken]);

  return {
    // State
    ...state,
    notifications,
    unreadCount,

    // Actions
    initialize: initializeManually, // Export manual initialization for UI buttons
    resetPermissionState,
    refreshToken,
    cleanup,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    handleNotificationClick
  };
};

export default useFCMNotifications;
