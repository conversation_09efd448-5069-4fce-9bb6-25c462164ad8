
import * as React from "react"

// Enhanced breakpoint system matching Tailwind CSS
const BREAKPOINTS = {
  mobile: 768,    // sm: 640px, md: 768px
  tablet: 1024,   // lg: 1024px
  desktop: 1440,  // xl: 1280px, 2xl: 1536px
} as const

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean>(
    typeof window !== 'undefined' ? window.innerWidth < BREAKPOINTS.mobile : false
  )

  React.useEffect(() => {
    if (typeof window === 'undefined') return

    const checkMobile = () => {
      setIsMobile(window.innerWidth < BREAKPOINTS.mobile)
    }

    // Verificação inicial
    checkMobile()

    // Adicionar listener para redimensionamento
    window.addEventListener('resize', checkMobile)

    // Limpar listener
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return isMobile
}

export function useIsTablet() {
  const [isTablet, setIsTablet] = React.useState<boolean>(
    typeof window !== 'undefined' ?
      window.innerWidth >= BREAKPOINTS.mobile && window.innerWidth < BREAKPOINTS.tablet : false
  )

  React.useEffect(() => {
    if (typeof window === 'undefined') return

    const checkTablet = () => {
      const width = window.innerWidth
      setIsTablet(width >= BREAKPOINTS.mobile && width < BREAKPOINTS.tablet)
    }

    checkTablet()
    window.addEventListener('resize', checkTablet)
    return () => window.removeEventListener('resize', checkTablet)
  }, [])

  return isTablet
}

export function useScreenSize() {
  const [screenSize, setScreenSize] = React.useState<'mobile' | 'tablet' | 'desktop'>(() => {
    if (typeof window === 'undefined') return 'desktop'
    const width = window.innerWidth
    if (width < BREAKPOINTS.mobile) return 'mobile'
    if (width < BREAKPOINTS.tablet) return 'tablet'
    return 'desktop'
  })

  React.useEffect(() => {
    if (typeof window === 'undefined') return

    const checkScreenSize = () => {
      const width = window.innerWidth
      if (width < BREAKPOINTS.mobile) setScreenSize('mobile')
      else if (width < BREAKPOINTS.tablet) setScreenSize('tablet')
      else setScreenSize('desktop')
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  return screenSize
}

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = React.useState<boolean>(
    typeof window !== 'undefined' ? window.matchMedia(query).matches : false
  )

  React.useEffect(() => {
    if (typeof window === 'undefined') return

    const media = window.matchMedia(query)
    
    const listener = () => {
      setMatches(media.matches)
    }
    
    // Verificação inicial
    listener()
    
    // Adicionar listener para mudanças
    if (media.addEventListener) {
      media.addEventListener('change', listener)
      return () => media.removeEventListener('change', listener)
    } else {
      // Fallback para navegadores mais antigos
      media.addListener(listener)
      return () => media.removeListener(listener)
    }
  }, [query])

  return matches
}
