import { useMutation, useQueryClient } from "@tanstack/react-query";
import { couponService } from "@/services/api";

interface UpdateCouponStatusParams {
  couponId: string;
  status: boolean;
}

export const useUpdateCouponStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ couponId, status }: UpdateCouponStatusParams) =>
      couponService.updateStatus(couponId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["coupons"] });
    },
  });
}; 