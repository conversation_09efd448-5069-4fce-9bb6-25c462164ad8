import { useCallback, useEffect, useRef, useState } from 'react';

export interface AudioNotificationSettings {
  enabled: boolean;
  volume: number; // 0 to 1
  newOrderSound: string;
  statusUpdateSound: string;
}

export interface AudioNotificationState {
  isSupported: boolean;
  canPlay: boolean;
  settings: AudioNotificationSettings;
  isPlaying: boolean;
}

const DEFAULT_SETTINGS: AudioNotificationSettings = {
  enabled: true,
  volume: 0.7,
  newOrderSound: 'new-order',
  statusUpdateSound: 'status-update',
};

const AUDIO_SETTINGS_STORAGE_KEY = 'audioNotificationSettings';

// Audio file paths - these would need to be added to the public folder
const SOUND_FILES = {
  'new-order': {
    mp3: '/sounds/new-order.mp3',
    ogg: '/sounds/new-order.ogg',
  },
  'status-update': {
    mp3: '/sounds/status-update.mp3',
    ogg: '/sounds/status-update.ogg',
  },
  'connection': {
    mp3: '/sounds/connection.mp3',
    ogg: '/sounds/connection.ogg',
  },
  'error': {
    mp3: '/sounds/error.mp3',
    ogg: '/sounds/error.ogg',
  },
};

const useAudioNotifications = () => {
  const [state, setState] = useState<AudioNotificationState>({
    isSupported: false,
    canPlay: false,
    settings: DEFAULT_SETTINGS,
    isPlaying: false,
  });

  const audioContextRef = useRef<AudioContext | null>(null);
  const audioBuffersRef = useRef<Map<string, AudioBuffer>>(new Map());
  const currentSourceRef = useRef<AudioBufferSourceNode | null>(null);

  const logDebug = useCallback((message: string, data?: any) => {
    console.log(`🔊 [Audio] ${message}`, data || '');
  }, []);

  // Check if Web Audio API is supported
  const checkSupport = useCallback(() => {
    const isSupported = !!(window.AudioContext || (window as any).webkitAudioContext);
    logDebug('Web Audio API support', { isSupported });
    return isSupported;
  }, [logDebug]);

  // Initialize Audio Context
  const initializeAudioContext = useCallback(async () => {
    if (audioContextRef.current) return audioContextRef.current;

    try {
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
      audioContextRef.current = new AudioContextClass();
      
      // Resume context if it's suspended (required by some browsers)
      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }

      logDebug('Audio context initialized', { state: audioContextRef.current.state });
      return audioContextRef.current;
    } catch (error) {
      logDebug('Failed to initialize audio context', error);
      return null;
    }
  }, [logDebug]);

  // Generate audio buffer programmatically as fallback
  const generateAudioBuffer = useCallback((soundKey: string): AudioBuffer | null => {
    if (!audioContextRef.current) return null;

    const sampleRate = audioContextRef.current.sampleRate;
    let duration = 0.5; // Default duration
    let frequency = 440; // Default frequency

    // Configure sound parameters based on type
    switch (soundKey) {
      case 'new-order':
        duration = 0.8;
        frequency = 800; // Higher pitch for new orders
        break;
      case 'status-update':
        duration = 0.4;
        frequency = 600; // Medium pitch for updates
        break;
      case 'connection':
        duration = 0.3;
        frequency = 1000; // High pitch for connection
        break;
      case 'error':
        duration = 1.0;
        frequency = 300; // Low pitch for errors
        break;
    }

    const frameCount = sampleRate * duration;
    const audioBuffer = audioContextRef.current.createBuffer(1, frameCount, sampleRate);
    const channelData = audioBuffer.getChannelData(0);

    // Generate a pleasant notification sound (sine wave with envelope)
    for (let i = 0; i < frameCount; i++) {
      const t = i / sampleRate;
      const envelope = Math.exp(-t * 3); // Exponential decay

      let sample = 0;
      if (soundKey === 'new-order') {
        // Two-tone chime for new orders
        sample = (Math.sin(2 * Math.PI * frequency * t) +
                 Math.sin(2 * Math.PI * (frequency * 1.5) * t)) * 0.5;
      } else if (soundKey === 'error') {
        // Buzzer-like sound for errors
        sample = Math.sin(2 * Math.PI * frequency * t) * (t < 0.1 ? 1 : 0.3);
      } else {
        // Simple sine wave for other notifications
        sample = Math.sin(2 * Math.PI * frequency * t);
      }

      channelData[i] = sample * envelope * 0.3; // Keep volume moderate
    }

    return audioBuffer;
  }, []);

  // Load audio file and decode it, with programmatic fallback
  const loadAudioBuffer = useCallback(async (soundKey: string): Promise<AudioBuffer | null> => {
    if (!audioContextRef.current) return null;

    // Check if already loaded
    const cached = audioBuffersRef.current.get(soundKey);
    if (cached) return cached;

    const soundFiles = SOUND_FILES[soundKey as keyof typeof SOUND_FILES];
    if (!soundFiles) {
      logDebug('Sound file not found, generating programmatically', { soundKey });
      const generatedBuffer = generateAudioBuffer(soundKey);
      if (generatedBuffer) {
        audioBuffersRef.current.set(soundKey, generatedBuffer);
      }
      return generatedBuffer;
    }

    // Try MP3 first, then OGG as fallback
    const urls = [soundFiles.mp3, soundFiles.ogg];

    for (const url of urls) {
      try {
        logDebug('Loading audio file', { url });
        const response = await fetch(url);

        if (!response.ok) {
          logDebug('Failed to fetch audio file', { url, status: response.status });
          continue;
        }

        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);

        // Cache the buffer
        audioBuffersRef.current.set(soundKey, audioBuffer);
        logDebug('Audio file loaded successfully', { url, duration: audioBuffer.duration });

        return audioBuffer;
      } catch (error) {
        logDebug('Failed to load audio file', { url, error });
        continue;
      }
    }

    logDebug('All audio file formats failed to load, generating programmatically', { soundKey });
    const generatedBuffer = generateAudioBuffer(soundKey);
    if (generatedBuffer) {
      audioBuffersRef.current.set(soundKey, generatedBuffer);
    }
    return generatedBuffer;
  }, [logDebug, generateAudioBuffer]);

  // Play audio buffer
  const playAudioBuffer = useCallback(async (audioBuffer: AudioBuffer, volume: number = 1) => {
    if (!audioContextRef.current || !audioBuffer) return false;

    try {
      // Stop any currently playing sound
      if (currentSourceRef.current) {
        currentSourceRef.current.stop();
        currentSourceRef.current = null;
      }

      // Create source and gain nodes
      const source = audioContextRef.current.createBufferSource();
      const gainNode = audioContextRef.current.createGain();

      // Configure nodes
      source.buffer = audioBuffer;
      gainNode.gain.value = Math.max(0, Math.min(1, volume));

      // Connect nodes
      source.connect(gainNode);
      gainNode.connect(audioContextRef.current.destination);

      // Set up event handlers
      source.onended = () => {
        setState(prev => ({ ...prev, isPlaying: false }));
        currentSourceRef.current = null;
      };

      // Play the sound
      setState(prev => ({ ...prev, isPlaying: true }));
      currentSourceRef.current = source;
      source.start(0);

      logDebug('Audio played successfully', { duration: audioBuffer.duration, volume });
      return true;
    } catch (error) {
      logDebug('Failed to play audio', error);
      setState(prev => ({ ...prev, isPlaying: false }));
      return false;
    }
  }, [logDebug]);

  // Load settings from localStorage
  const loadSettings = useCallback((): AudioNotificationSettings => {
    try {
      const stored = localStorage.getItem(AUDIO_SETTINGS_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...DEFAULT_SETTINGS, ...parsed };
      }
    } catch (error) {
      logDebug('Failed to load audio settings', error);
    }
    return DEFAULT_SETTINGS;
  }, [logDebug]);

  // Save settings to localStorage
  const saveSettings = useCallback((settings: AudioNotificationSettings) => {
    try {
      localStorage.setItem(AUDIO_SETTINGS_STORAGE_KEY, JSON.stringify(settings));
      logDebug('Audio settings saved', settings);
    } catch (error) {
      logDebug('Failed to save audio settings', error);
    }
  }, [logDebug]);

  // Update audio settings
  const updateSettings = useCallback((newSettings: Partial<AudioNotificationSettings>) => {
    setState(prev => {
      const updatedSettings = { ...prev.settings, ...newSettings };
      saveSettings(updatedSettings);
      return { ...prev, settings: updatedSettings };
    });
  }, [saveSettings]);

  // Check if audio can be played (user interaction required)
  const checkCanPlay = useCallback(async () => {
    if (!checkSupport()) return false;

    try {
      const audioContext = await initializeAudioContext();
      if (!audioContext) return false;

      const canPlay = audioContext.state === 'running';
      logDebug('Audio playback capability', { canPlay, state: audioContext.state });

      setState(prev => ({ ...prev, canPlay }));
      return canPlay;
    } catch (error) {
      logDebug('Failed to check audio capability', error);
      return false;
    }
  }, [checkSupport, initializeAudioContext, logDebug]);

  // Play notification sound
  const playNotificationSound = useCallback(async (soundType: 'new-order' | 'status-update' | 'connection' | 'error') => {
    if (!state.settings.enabled) {
      logDebug('Audio notifications disabled');
      return false;
    }

    try {
      // Check audio capability when first needed (lazy check)
      if (!state.canPlay) {
        const canPlay = await checkCanPlay();
        if (!canPlay) {
          logDebug('Audio cannot be played (user interaction required)');
          return false;
        }
      }

      // Initialize audio context if needed
      const audioContext = await initializeAudioContext();
      if (!audioContext) {
        logDebug('Audio context not available');
        return false;
      }

      // Load audio buffer
      const audioBuffer = await loadAudioBuffer(soundType);
      if (!audioBuffer) {
        logDebug('Audio buffer not available', { soundType });
        return false;
      }

      // Play the sound
      return await playAudioBuffer(audioBuffer, state.settings.volume);
    } catch (error) {
      logDebug('Failed to play notification sound', { soundType, error });
      return false;
    }
  }, [state.settings.enabled, state.canPlay, state.settings.volume, checkCanPlay, initializeAudioContext, loadAudioBuffer, playAudioBuffer, logDebug]);

  // Test audio playback
  const testAudio = useCallback(async (soundType: 'new-order' | 'status-update' | 'connection' | 'error' = 'new-order') => {
    logDebug('Testing audio playback', { soundType });
    return await playNotificationSound(soundType);
  }, [playNotificationSound, logDebug]);

  // Stop currently playing audio
  const stopAudio = useCallback(() => {
    if (currentSourceRef.current) {
      try {
        currentSourceRef.current.stop();
        currentSourceRef.current = null;
        setState(prev => ({ ...prev, isPlaying: false }));
        logDebug('Audio stopped');
      } catch (error) {
        logDebug('Failed to stop audio', error);
      }
    }
  }, [logDebug]);



  // Enable audio after user interaction
  const enableAudio = useCallback(async () => {
    try {
      const audioContext = await initializeAudioContext();
      if (audioContext && audioContext.state === 'suspended') {
        await audioContext.resume();
        logDebug('Audio context resumed');
      }
      
      const canPlay = await checkCanPlay();
      return canPlay;
    } catch (error) {
      logDebug('Failed to enable audio', error);
      return false;
    }
  }, [initializeAudioContext, checkCanPlay, logDebug]);

  // Initialize state
  useEffect(() => {
    const isSupported = checkSupport();
    const settings = loadSettings();

    setState(prev => ({
      ...prev,
      isSupported,
      settings,
    }));

    logDebug('Audio notification system initialized', {
      isSupported,
      settings,
    });

    // Don't check audio capability during initialization to avoid autoplay policy issues
    // Audio capability will be checked when first needed
  }, [checkSupport, loadSettings, logDebug]);

  // Preload audio files when settings change
  useEffect(() => {
    if (state.isSupported && state.settings.enabled) {
      // Preload commonly used sounds
      const soundsToPreload = ['new-order', 'status-update'];
      soundsToPreload.forEach(sound => {
        loadAudioBuffer(sound).catch(() => {
          // Ignore preload errors
        });
      });
    }
  }, [state.isSupported, state.settings.enabled, loadAudioBuffer]);

  // Comprehensive cleanup function
  const cleanup = useCallback(() => {
    console.log('🧹 Cleaning up audio notifications');

    try {
      // Stop any playing audio
      stopAudio();

      // Close audio context
      if (audioContextRef.current) {
        audioContextRef.current.close().catch((error) => {
          console.warn('Error closing audio context:', error);
        });
        audioContextRef.current = null;
      }

      // Clear audio buffers
      audioBuffersRef.current.clear();

      console.log('✅ Audio cleanup completed');
    } catch (error) {
      console.warn('Error during audio cleanup:', error);
    }
  }, [stopAudio]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    ...state,
    updateSettings,
    playNotificationSound,
    testAudio,
    stopAudio,
    enableAudio,
    checkCanPlay,
    cleanup,
  };
};

export default useAudioNotifications;
