import { useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';

interface BackgroundNotification {
  id: string;
  title: string;
  body: string;
  type: string;
  data: any;
  timestamp: string;
  processed: boolean;
}

interface UseBackgroundNotificationsProps {
  enabled?: boolean;
  onNotificationProcessed?: (notification: BackgroundNotification) => void;
  forceRefresh?: () => void;
}

/**
 * Hook to handle background notifications when user returns to tab
 * Processes FCM notifications that were received while tab was inactive
 */
export const useBackgroundNotifications = ({
  enabled = true,
  onNotificationProcessed,
  forceRefresh
}: UseBackgroundNotificationsProps = {}) => {
  const queryClient = useQueryClient();

  /**
   * Process pending background notifications
   */
  const processBackgroundNotifications = useCallback(() => {
    if (!enabled) return;

    try {
      const storedNotifications = localStorage.getItem('fcm_background_notifications');
      if (!storedNotifications) return;

      const notifications: BackgroundNotification[] = JSON.parse(storedNotifications);
      const unprocessedNotifications = notifications.filter(n => !n.processed);

      if (unprocessedNotifications.length === 0) return;

      console.log('🔄 Processing background notifications:', {
        total: notifications.length,
        unprocessed: unprocessedNotifications.length,
        notifications: unprocessedNotifications
      });

      // Process each unprocessed notification
      unprocessedNotifications.forEach(notification => {
        console.log('🔄 Processing background notification:', notification);

        // Check if notification is order-related
        const isOrderRelated = notification.type === 'new-order' ||
                               notification.type === 'new_order_received' ||
                               notification.type === 'order_status_update' ||
                               notification.type === 'payment_confirmed';

        if (isOrderRelated) {
          console.log('🔄 Invalidating queries for background order notification:', notification.type);

          // Get all queries before invalidation for debugging
          const allQueries = queryClient.getQueryCache().getAll();
          const orderQueries = allQueries.filter(query =>
            query.queryKey[0] === 'orders' ||
            query.queryKey[0] === 'partner-orders' ||
            query.queryKey[0] === 'partner-orders-metrics' ||
            query.queryKey[0] === 'admin-orders'
          );

          console.log("🔄 Found order queries to invalidate from background:", orderQueries.map(q => ({
            key: q.queryKey,
            state: q.state.status,
            dataUpdatedAt: q.state.dataUpdatedAt
          })));

          // Invalidate order-related queries with force refetch
          const invalidationPromise = queryClient.invalidateQueries({
            predicate: (query) => {
              const key = query.queryKey[0];
              const shouldInvalidate = key === 'orders' ||
                     key === 'partner-orders' ||
                     key === 'partner-orders-metrics' ||
                     key === 'admin-orders';

              if (shouldInvalidate) {
                console.log("🔄 Invalidating query from background notification:", query.queryKey);
              }

              return shouldInvalidate;
            },
            refetchType: 'active' // Force refetch of active queries
          });

          // Also force refetch partner-orders specifically
          const refetchPromise = queryClient.refetchQueries({
            predicate: (query) => {
              const key = query.queryKey[0];
              return key === 'partner-orders';
            }
          });

          // Wait for refetch to complete and log results
          refetchPromise.then(() => {
            console.log("🔄 Background notification query refetch completed");

            // Double-check that queries were actually refetched
            const updatedQueries = queryClient.getQueryCache().getAll().filter(query =>
              query.queryKey[0] === 'partner-orders'
            );

            console.log("🔄 Updated partner-orders queries after background processing:",
              updatedQueries.map(q => ({
                key: q.queryKey,
                state: q.state.status,
                dataUpdatedAt: new Date(q.state.dataUpdatedAt || 0).toLocaleTimeString(),
                isFetching: q.state.isFetching
              }))
            );

            // Fallback: if forceRefresh is provided and queries seem stale, call it
            if (forceRefresh && updatedQueries.length === 0) {
              console.log("🔄 No partner-orders queries found, calling forceRefresh fallback");
              setTimeout(forceRefresh, 500);
            }
          });

          console.log("🔄 Background notification query invalidation completed");
        }

        // Mark as processed
        notification.processed = true;
        
        // Call callback if provided
        onNotificationProcessed?.(notification);
      });

      // Update localStorage with processed notifications
      localStorage.setItem('fcm_background_notifications', JSON.stringify(notifications));

      // Clean up old processed notifications (keep only last 10)
      const recentNotifications = notifications
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 10);
      
      localStorage.setItem('fcm_background_notifications', JSON.stringify(recentNotifications));

    } catch (error) {
      console.error('🔄 Error processing background notifications:', error);
    }
  }, [enabled, queryClient, onNotificationProcessed]);

  /**
   * Handle visibility change events
   */
  const handleVisibilityChange = useCallback(() => {
    if (!enabled) return;

    console.log('🔄 Visibility change detected:', {
      visibilityState: document.visibilityState,
      hidden: document.hidden,
      timestamp: new Date().toISOString()
    });

    if (document.visibilityState === 'visible') {
      console.log('🔄 Tab became visible, checking for background notifications');
      // Small delay to ensure everything is ready
      setTimeout(() => {
        console.log('🔄 Processing background notifications after visibility change');
        processBackgroundNotifications();
      }, 100);
    } else {
      console.log('🔄 Tab became hidden');
    }
  }, [enabled, processBackgroundNotifications]);

  /**
   * Handle window focus events (additional safety net)
   */
  const handleWindowFocus = useCallback(() => {
    if (!enabled) return;

    console.log('🔄 Window focused, checking for background notifications', {
      hasFocus: document.hasFocus(),
      visibilityState: document.visibilityState,
      timestamp: new Date().toISOString()
    });
    // Small delay to ensure everything is ready
    setTimeout(() => {
      console.log('🔄 Processing background notifications after window focus');
      processBackgroundNotifications();
    }, 100);
  }, [enabled, processBackgroundNotifications]);

  /**
   * Handle service worker messages
   */
  const handleServiceWorkerMessage = useCallback((event: MessageEvent) => {
    if (!enabled) return;

    if (event.data && event.data.type === 'BACKGROUND_NOTIFICATION_STORED') {
      console.log('🔄 Service worker notified about background notification storage');
      // If tab is currently visible, process immediately
      if (document.visibilityState === 'visible') {
        setTimeout(processBackgroundNotifications, 100);
      }
    }
  }, [enabled, processBackgroundNotifications]);

  // Set up event listeners
  useEffect(() => {
    if (!enabled) return;

    // Process any existing notifications on mount
    processBackgroundNotifications();

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Listen for window focus (additional safety net)
    window.addEventListener('focus', handleWindowFocus);

    // Listen for service worker messages
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
    }

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleWindowFocus);

      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      }
    };
  }, [enabled, handleVisibilityChange, handleWindowFocus, handleServiceWorkerMessage, processBackgroundNotifications]);

  // Expose test function to global scope for debugging
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).testBackgroundNotification = () => {
        const testNotification = {
          id: `test_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          title: 'Teste - Novo Pedido',
          body: 'Pedido de teste recebido - R$ 50,00',
          type: 'new_order_received',
          data: { order_id: 'test-123', amount: 5000 },
          timestamp: new Date().toISOString(),
          processed: false
        };

        const existing = JSON.parse(localStorage.getItem('fcm_background_notifications') || '[]');
        existing.push(testNotification);
        localStorage.setItem('fcm_background_notifications', JSON.stringify(existing));

        console.log('🧪 Test background notification added:', testNotification);
        console.log('🧪 Current visibility state:', document.visibilityState);
        console.log('🧪 Switch to another tab and back to test background processing');
        console.log('🧪 Or call processBackgroundNotifications() to test immediately');

        // Trigger storage event to simulate cross-tab communication
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'fcm_background_notifications',
          newValue: JSON.stringify(existing),
          storageArea: localStorage
        }));
      };

      (window as any).processBackgroundNotifications = () => {
        console.log('🧪 Manually triggering background notification processing');
        processBackgroundNotifications();
      };

      (window as any).clearBackgroundNotifications = () => {
        localStorage.removeItem('fcm_background_notifications');
        console.log('🧪 Background notifications cleared');
      };

      (window as any).testCompleteBackgroundFlow = () => {
        console.log('🧪 Testing complete background notification flow...');

        // Step 1: Add background notification
        (window as any).testBackgroundNotification();

        // Step 2: Simulate tab becoming hidden
        Object.defineProperty(document, 'visibilityState', {
          writable: true,
          value: 'hidden'
        });
        Object.defineProperty(document, 'hidden', {
          writable: true,
          value: true
        });

        console.log('🧪 Simulated tab becoming hidden');

        // Step 3: Wait a moment, then simulate tab becoming visible
        setTimeout(() => {
          Object.defineProperty(document, 'visibilityState', {
            writable: true,
            value: 'visible'
          });
          Object.defineProperty(document, 'hidden', {
            writable: true,
            value: false
          });

          console.log('🧪 Simulated tab becoming visible');

          // Trigger visibility change event
          document.dispatchEvent(new Event('visibilitychange'));

          console.log('🧪 Complete background flow test completed');
        }, 1000);
      };

      (window as any).checkBackgroundNotifications = () => {
        const stored = localStorage.getItem('fcm_background_notifications');
        const notifications = stored ? JSON.parse(stored) : [];
        console.log('🧪 Current background notifications:', notifications);
        return notifications;
      };

      (window as any).debugQueryCache = () => {
        const allQueries = queryClient.getQueryCache().getAll();
        const orderQueries = allQueries.filter(query =>
          query.queryKey[0] === 'orders' ||
          query.queryKey[0] === 'partner-orders' ||
          query.queryKey[0] === 'admin-orders'
        );

        console.log('🧪 Current order-related queries in cache:', orderQueries.map(q => ({
          key: q.queryKey,
          state: q.state.status,
          dataUpdatedAt: new Date(q.state.dataUpdatedAt || 0).toLocaleTimeString(),
          isFetching: q.state.isFetching,
          isStale: q.isStale(),
          data: q.state.data ? 'Has data' : 'No data'
        })));

        return orderQueries;
      };
    }
  }, [processBackgroundNotifications]);

  return {
    processBackgroundNotifications
  };
};

export default useBackgroundNotifications;
