import { useCallback, useEffect, useState } from 'react';
import { OrderStatusNotification } from './useOrderWebSocket';

export type NotificationPermission = 'default' | 'granted' | 'denied';

export interface NotificationSettings {
  enabled: boolean;
  soundEnabled: boolean;
  showOrderUpdates: boolean;
  showNewOrders: boolean;
}

export interface BrowserNotificationState {
  permission: NotificationPermission;
  isSupported: boolean;
  settings: NotificationSettings;
}

const DEFAULT_SETTINGS: NotificationSettings = {
  enabled: true,
  soundEnabled: true,
  showOrderUpdates: true,
  showNewOrders: true,
};

const SETTINGS_STORAGE_KEY = 'orderNotificationSettings';

const useBrowserNotifications = () => {
  const [state, setState] = useState<BrowserNotificationState>({
    permission: 'default',
    isSupported: false,
    settings: DEFAULT_SETTINGS,
  });

  const logDebug = useCallback((message: string, data?: any) => {
    console.log(`🔔 [Notifications] ${message}`, data || '');
  }, []);

  // Check if browser supports notifications
  const checkSupport = useCallback(() => {
    const isSupported = 'Notification' in window;
    logDebug('Browser notification support', { isSupported });
    return isSupported;
  }, [logDebug]);

  // Get current permission status
  const getPermission = useCallback((): NotificationPermission => {
    if (!checkSupport()) return 'denied';
    return Notification.permission as NotificationPermission;
  }, [checkSupport]);

  // Load settings from localStorage
  const loadSettings = useCallback((): NotificationSettings => {
    try {
      const stored = localStorage.getItem(SETTINGS_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...DEFAULT_SETTINGS, ...parsed };
      }
    } catch (error) {
      logDebug('Failed to load notification settings', error);
    }
    return DEFAULT_SETTINGS;
  }, [logDebug]);

  // Save settings to localStorage
  const saveSettings = useCallback((settings: NotificationSettings) => {
    try {
      localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(settings));
      logDebug('Notification settings saved', settings);
    } catch (error) {
      logDebug('Failed to save notification settings', error);
    }
  }, [logDebug]);

  // Request notification permission
  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    if (!checkSupport()) {
      logDebug('Notifications not supported');
      return 'denied';
    }

    if (Notification.permission === 'granted') {
      logDebug('Permission already granted');
      return 'granted';
    }

    try {
      logDebug('Requesting notification permission');
      const permission = await Notification.requestPermission();
      logDebug('Permission result', { permission });
      
      setState(prev => ({ ...prev, permission: permission as NotificationPermission }));
      return permission as NotificationPermission;
    } catch (error) {
      logDebug('Failed to request permission', error);
      return 'denied';
    }
  }, [checkSupport, logDebug]);

  // Update notification settings
  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    setState(prev => {
      const updatedSettings = { ...prev.settings, ...newSettings };
      saveSettings(updatedSettings);
      return { ...prev, settings: updatedSettings };
    });
  }, [saveSettings]);

  // Show a browser notification
  const showNotification = useCallback((
    title: string,
    options: NotificationOptions = {}
  ): Notification | null => {
    if (!checkSupport() || getPermission() !== 'granted') {
      logDebug('Cannot show notification - not supported or permission denied');
      return null;
    }

    try {
      const notification = new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        requireInteraction: false,
        silent: !state.settings.soundEnabled,
        ...options,
      });

      logDebug('Notification shown', { title });

      // Auto-close after 5 seconds if not requiring interaction
      if (!options.requireInteraction) {
        const autoCloseTimeout = setTimeout(() => {
          try {
            notification.close();
          } catch (error) {
            // Ignore errors if notification is already closed
            logDebug('Error closing notification (likely already closed)', error);
          }
        }, 5000);

        // Store timeout reference for potential cleanup
        (notification as any)._autoCloseTimeout = autoCloseTimeout;
      }

      return notification;
    } catch (error) {
      logDebug('Failed to show notification', error);
      return null;
    }
  }, [checkSupport, getPermission, state.settings.soundEnabled, logDebug]);

  // Show notification for order status update
  const showOrderNotification = useCallback((notification: OrderStatusNotification) => {
    if (!state.settings.enabled) {
      logDebug('Notifications disabled in settings');
      return null;
    }

    // Determine if this is a new order or status update
    const isNewOrder = notification.old_status === 'pending' && notification.new_status !== 'pending';
    const isStatusUpdate = notification.old_status && notification.old_status !== 'pending';

    // Check settings for notification type
    if (isNewOrder && !state.settings.showNewOrders) {
      logDebug('New order notifications disabled');
      return null;
    }

    if (isStatusUpdate && !state.settings.showOrderUpdates) {
      logDebug('Order update notifications disabled');
      return null;
    }

    // Create notification title and body
    let title: string;
    let body: string;
    let tag: string;

    if (isNewOrder) {
      title = '🆕 Novo Pedido Recebido';
      body = `Pedido ${notification.order_id.slice(-8)} - ${notification.company_name || 'Empresa'}`;
      tag = `new-order-${notification.order_id}`;
    } else {
      title = '📦 Status do Pedido Atualizado';
      body = `Pedido ${notification.order_id.slice(-8)} - ${notification.status_description}`;
      tag = `order-update-${notification.order_id}`;
    }

    if (notification.message) {
      body += `\n${notification.message}`;
    }

    const options: NotificationOptions = {
      body,
      tag, // This prevents duplicate notifications for the same order
      data: {
        orderId: notification.order_id,
        companyId: notification.company_id,
        type: isNewOrder ? 'new-order' : 'status-update',
      },
      actions: [
        {
          action: 'view',
          title: 'Ver Pedido',
        },
        {
          action: 'dismiss',
          title: 'Dispensar',
        },
      ],
    };

    return showNotification(title, options);
  }, [state.settings, showNotification, logDebug]);

  // Handle notification click events
  const handleNotificationClick = useCallback((event: Event) => {
    const notification = event.target as Notification;
    const data = notification.data;

    logDebug('Notification clicked', data);

    if (data?.orderId) {
      // Focus the window
      window.focus();
      
      // Navigate to orders page or specific order
      // This could be enhanced to navigate to specific order details
      const ordersPath = '/partner/orders';
      if (window.location.pathname !== ordersPath) {
        window.location.href = ordersPath;
      }
    }

    notification.close();
  }, [logDebug]);

  // Initialize state and event listeners
  useEffect(() => {
    const isSupported = checkSupport();
    const permission = getPermission();
    const settings = loadSettings();

    setState({
      permission,
      isSupported,
      settings,
    });

    logDebug('Notification system initialized', {
      isSupported,
      permission,
      settings,
    });

    // Add global notification click handler
    if (isSupported) {
      // Note: This is a global handler, but individual notifications
      // can also have their own click handlers
      const handleGlobalNotificationClick = (event: Event) => {
        handleNotificationClick(event);
      };

      // Listen for notification clicks (this is a bit tricky in browsers)
      // Most browsers handle this automatically, but we can add custom logic
      document.addEventListener('notificationclick', handleGlobalNotificationClick);

      return () => {
        document.removeEventListener('notificationclick', handleGlobalNotificationClick);
      };
    }
  }, [checkSupport, getPermission, loadSettings, logDebug, handleNotificationClick]);

  // Listen for permission changes
  useEffect(() => {
    if (!checkSupport()) return;

    const handlePermissionChange = () => {
      const newPermission = getPermission();
      logDebug('Permission changed', { newPermission });
      setState(prev => ({ ...prev, permission: newPermission }));
    };

    // Some browsers support permission change events
    if ('permissions' in navigator) {
      navigator.permissions.query({ name: 'notifications' as PermissionName })
        .then(permissionStatus => {
          permissionStatus.addEventListener('change', handlePermissionChange);
          return () => {
            permissionStatus.removeEventListener('change', handlePermissionChange);
          };
        })
        .catch(() => {
          // Ignore errors - not all browsers support this
        });
    }
  }, [checkSupport, getPermission, logDebug]);

  // Cleanup function to clear any pending notification timeouts
  const cleanup = useCallback(() => {
    logDebug('Cleaning up browser notifications');

    // Close any open notifications and clear their timeouts
    if ('serviceWorker' in navigator && navigator.serviceWorker.ready) {
      navigator.serviceWorker.ready.then(registration => {
        registration.getNotifications().then(notifications => {
          notifications.forEach(notification => {
            try {
              // Clear auto-close timeout if it exists
              if ((notification as any)._autoCloseTimeout) {
                clearTimeout((notification as any)._autoCloseTimeout);
              }
              notification.close();
            } catch (error) {
              // Ignore errors during cleanup
            }
          });
        }).catch(() => {
          // Ignore errors - service worker might not be available
        });
      }).catch(() => {
        // Ignore errors - service worker might not be available
      });
    }
  }, [logDebug]);

  return {
    ...state,
    requestPermission,
    updateSettings,
    showNotification,
    showOrderNotification,
    canShowNotifications: state.isSupported && state.permission === 'granted' && state.settings.enabled,
    cleanup,
  };
};

export default useBrowserNotifications;
