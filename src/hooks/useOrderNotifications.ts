import { useCallback, useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import useOrderWebSocket, { OrderStatusNotification } from './useOrderWebSocket';
import useBrowserNotifications from './useBrowserNotifications';
import useAudioNotifications from './useAudioNotifications';


export interface NotificationItem {
  id: string;
  data?: any;
  notification: OrderStatusNotification;
  timestamp: Date;
  isRead: boolean;
  type: 'new-order' | 'status-update';
}

export interface NotificationState {
  items: NotificationItem[];
  unreadCount: number;
  lastNotificationTime?: Date;
}

const NOTIFICATIONS_STORAGE_KEY = 'orderNotifications';
const MAX_STORED_NOTIFICATIONS = 50;

const useOrderNotifications = (token: string, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const [notificationState, setNotificationState] = useState<NotificationState>({
    items: [],
    unreadCount: 0,
  });

  const browserNotifications = useBrowserNotifications();
  const audioNotifications = useAudioNotifications();

  const logDebug = useCallback((message: string, data?: any) => {
    console.log(`🔔 [OrderNotifications] ${message}`, data || '');
  }, []);

  // Load notifications from localStorage
  const loadStoredNotifications = useCallback((): NotificationState => {
    try {
      const stored = localStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert timestamp strings back to Date objects
        const items = parsed.items.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp),
        }));
        return {
          ...parsed,
          items,
          lastNotificationTime: parsed.lastNotificationTime 
            ? new Date(parsed.lastNotificationTime) 
            : undefined,
        };
      }
    } catch (error) {
      logDebug('Failed to load stored notifications', error);
    }
    return { items: [], unreadCount: 0 };
  }, [logDebug]);

  // Save notifications to localStorage
  const saveNotifications = useCallback((state: NotificationState) => {
    try {
      // Keep only the most recent notifications
      const itemsToSave = state.items.slice(0, MAX_STORED_NOTIFICATIONS);
      const stateToSave = {
        ...state,
        items: itemsToSave,
      };
      localStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(stateToSave));
      logDebug('Notifications saved to storage', { count: itemsToSave.length });
    } catch (error) {
      logDebug('Failed to save notifications', error);
    }
  }, [logDebug]);

  // Add new notification
  const addNotification = useCallback((orderNotification: OrderStatusNotification) => {
    const notificationId = `${orderNotification.order_id}-${orderNotification.timestamp}`;
    
    // Determine notification type
    const isNewOrder = orderNotification.old_status === 'pending' && orderNotification.new_status !== 'pending';
    const type: 'new-order' | 'status-update' = isNewOrder ? 'new-order' : 'status-update';

    const newItem: NotificationItem = {
      id: notificationId,
      data: orderNotification,
      notification: orderNotification,
      timestamp: new Date(orderNotification.timestamp),
      isRead: false,
      type,
    };

    setNotificationState(prev => {
      // Check if notification already exists
      const existingIndex = prev.items.findIndex(item => item.id === notificationId);
      if (existingIndex !== -1) {
        logDebug('Notification already exists, skipping', { id: notificationId });
        return prev;
      }

      // Add new notification at the beginning
      const newItems = [newItem, ...prev.items];
      const newState = {
        items: newItems,
        unreadCount: prev.unreadCount + 1,
        lastNotificationTime: newItem.timestamp,
      };

      // Save to localStorage
      saveNotifications(newState);

      logDebug('New notification added', {
        id: notificationId,
        type,
        orderId: orderNotification.order_id,
        status: orderNotification.new_status,
      });

      return newState;
    });

    return newItem;
  }, [saveNotifications, logDebug]);

  // Mark notification as read
  const markAsRead = useCallback((notificationId: string) => {
    setNotificationState(prev => {
      const itemIndex = prev.items.findIndex(item => item.id === notificationId);
      if (itemIndex === -1 || prev.items[itemIndex].isRead) {
        return prev;
      }

      const newItems = [...prev.items];
      newItems[itemIndex] = { ...newItems[itemIndex], isRead: true };

      const newState = {
        ...prev,
        items: newItems,
        unreadCount: Math.max(0, prev.unreadCount - 1),
      };

      saveNotifications(newState);
      logDebug('Notification marked as read', { id: notificationId });

      return newState;
    });
  }, [saveNotifications, logDebug]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    setNotificationState(prev => {
      if (prev.unreadCount === 0) return prev;

      const newItems = prev.items.map(item => ({ ...item, isRead: true }));
      const newState = {
        ...prev,
        items: newItems,
        unreadCount: 0,
      };

      saveNotifications(newState);
      logDebug('All notifications marked as read');

      return newState;
    });
  }, [saveNotifications, logDebug]);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    const newState: NotificationState = {
      items: [],
      unreadCount: 0,
    };

    setNotificationState(newState);
    saveNotifications(newState);
    logDebug('All notifications cleared');
  }, [saveNotifications, logDebug]);

  // Handle incoming WebSocket notifications
  const handleOrderStatusUpdate = useCallback((orderNotification: OrderStatusNotification) => {
    logDebug('Order status update received', {
      orderId: orderNotification.order_id,
      oldStatus: orderNotification.old_status,
      newStatus: orderNotification.new_status,
    });

    // Add to notification state
    const notificationItem = addNotification(orderNotification);

    // Show browser notification
    if (browserNotifications.canShowNotifications) {
      browserNotifications.showOrderNotification(orderNotification);
    }

    // Play audio notification
    if (audioNotifications.isSupported && audioNotifications.settings.enabled) {
      const soundType = notificationItem.type === 'new-order' ? 'new-order' : 'status-update';
      audioNotifications.playNotificationSound(soundType);
    }

    // Show toast notification as fallback
    const isNewOrder = notificationItem.type === 'new-order';
    const title = isNewOrder ? '🆕 Novo Pedido' : '📦 Status Atualizado';
    const description = `Pedido ${orderNotification.order_id.slice(-8)} - ${orderNotification.status_description}`;

    toast(title, {
      description,
      duration: 6000,
      dismissible: true,
      action: {
        label: 'Ver',
        onClick: () => {
          // Navigate to orders page
          window.location.href = '/partner/orders';
        },
      },
    });

    // Invalidate and refetch order queries to update the UI
    queryClient.invalidateQueries({ queryKey: ['orders'] });
    queryClient.invalidateQueries({ queryKey: ['partner-orders'] });

  }, [
    addNotification,
    browserNotifications,
    audioNotifications,
    queryClient,
    logDebug,
  ]);

  // Handle WebSocket connection changes
  const handleConnectionChange = useCallback((connected: boolean) => {
    logDebug('WebSocket connection changed', { connected });

    if (connected) {
      // Play connection sound
      if (audioNotifications.isSupported && audioNotifications.settings.enabled) {
        audioNotifications.playNotificationSound('connection');
      }

      toast('Conectado', {
        description: 'Notificações em tempo real ativadas',
        duration: 3000,
      });
    } else {
      toast('Desconectado', {
        description: 'Tentando reconectar...',
        duration: 3000,
      });
    }
  }, [audioNotifications, logDebug]);

  // Handle WebSocket errors
  const handleWebSocketError = useCallback((error: string) => {
    logDebug('WebSocket error', { error });

    // Play error sound
    if (audioNotifications.isSupported && audioNotifications.settings.enabled) {
      audioNotifications.playNotificationSound('error');
    }

    toast('Erro de Conexão', {
      description: error,
      duration: 5000,
    });
  }, [audioNotifications, logDebug]);

  // Initialize WebSocket connection
  const webSocket = useOrderWebSocket({
    token,
    enabled,
    onStatusUpdate: handleOrderStatusUpdate,
    onConnectionChange: handleConnectionChange,
    onError: handleWebSocketError,
  });

  // Load stored notifications on mount
  useEffect(() => {
    const storedState = loadStoredNotifications();
    setNotificationState(storedState);
    logDebug('Stored notifications loaded', {
      count: storedState.items.length,
      unread: storedState.unreadCount,
    });
  }, [loadStoredNotifications, logDebug]);

  // Cleanup old notifications periodically
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      setNotificationState(prev => {
        const now = new Date();
        const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
        
        const filteredItems = prev.items.filter(item => {
          const age = now.getTime() - item.timestamp.getTime();
          return age < maxAge;
        });

        if (filteredItems.length !== prev.items.length) {
          const newState = {
            ...prev,
            items: filteredItems,
            unreadCount: filteredItems.filter(item => !item.isRead).length,
          };

          saveNotifications(newState);
          logDebug('Old notifications cleaned up', {
            removed: prev.items.length - filteredItems.length,
            remaining: filteredItems.length,
          });

          return newState;
        }

        return prev;
      });
    }, 60 * 60 * 1000); // Check every hour

    return () => clearInterval(cleanupInterval);
  }, [saveNotifications, logDebug]);

  return {
    // WebSocket connection state
    connectionState: {
      isConnected: webSocket.isConnected,
      reconnectAttempts: webSocket.reconnectAttempts,
      lastError: webSocket.lastError,
      connectionId: webSocket.connectionId,
    },
    
    // Notification state
    notifications: notificationState.items,
    unreadCount: notificationState.unreadCount,
    lastNotificationTime: notificationState.lastNotificationTime,

    // Actions
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    reconnect: webSocket.connect,

    // Settings
    browserNotifications,
    audioNotifications,
  };
};

export default useOrderNotifications;
