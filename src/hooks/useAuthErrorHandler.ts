import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { api } from '@/services/api';

/**
 * Hook para lidar com erros de autenticação em toda a aplicação
 * Escuta por erros AUTH_REQUIRED e força logout quando necessário
 */
export const useAuthErrorHandler = () => {
  const { forceLogout } = useAuth();

  useEffect(() => {
    // Interceptor para capturar erros AUTH_REQUIRED
    const responseInterceptor = api.interceptors.response.use(
      (response) => response,
      (error) => {
        // Se o erro for AUTH_REQUIRED, força logout
        if (error.message === "AUTH_REQUIRED") {
          console.log("🔒 Erro AUTH_REQUIRED detectado - forçando logout");
          forceLogout();
        }
        return Promise.reject(error);
      }
    );

    // Cleanup do interceptor quando o componente for desmontado
    return () => {
      api.interceptors.response.eject(responseInterceptor);
    };
  }, [forceLogout]);
};
