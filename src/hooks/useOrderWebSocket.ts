import { useCallback, useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

export interface OrderStatusNotification {
  order_id: string;
  invoice_id: number;
  new_status: string;
  old_status?: string;
  status_description: string;
  message: string;
  updated_by: string;
  updater_name?: string;
  user_id: number;
  company_id: number;
  company_name?: string;
  timestamp: string;
}

export interface WebSocketMessage {
  type: string;
  action?: string;
  data?: any;
  timestamp: string;
  user_id?: number;
  company_id?: number;
}

export interface UseOrderWebSocketProps {
  token: string;
  wsUrl?: string;
  onStatusUpdate?: (notification: OrderStatusNotification) => void;
  onConnectionChange?: (connected: boolean) => void;
  onError?: (error: string) => void;
  enabled?: boolean;
}

export interface WebSocketConnectionState {
  isConnected: boolean;
  reconnectAttempts: number;
  lastError?: string;
  connectionId?: string;
}

const useOrderWebSocket = ({
  token,
  wsUrl = import.meta.env.VITE_NODE_ENV === "development"
    ? "ws://localhost:8080/v1/ws/connect"
    : "wss://api.izymercado.com.br/v1/ws/connect",
  onStatusUpdate,
  onConnectionChange,
  onError,
  enabled = true,
}: UseOrderWebSocketProps) => {
  const ws = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  const [connectionState, setConnectionState] = useState<WebSocketConnectionState>({
    isConnected: false,
    reconnectAttempts: 0,
  });

  const logDebug = useCallback((message: string, data?: any) => {
    console.log(`🔌 [WebSocket] ${message}`, data || '');
  }, []);

  const clearTimeouts = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
  }, []);

  const startPingInterval = useCallback(() => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
    }

    pingIntervalRef.current = setInterval(() => {
      if (ws.current && ws.current.readyState === WebSocket.OPEN) {
        try {
          ws.current.send(JSON.stringify({
            type: 'ping',
            timestamp: new Date().toISOString(),
          }));
          logDebug('Ping sent');
        } catch (error) {
          logDebug('Failed to send ping', error);
        }
      }
    }, 30000); // 30 seconds
  }, [logDebug]);

  const connect = useCallback(() => {
    if (!enabled || !token) {
      logDebug('Connection disabled or no token provided');
      return;
    }

    if (ws.current && ws.current.readyState === WebSocket.CONNECTING) {
      logDebug('Connection already in progress');
      return;
    }

    try {
      // Clean up existing connection
      if (ws.current) {
        ws.current.close();
        ws.current = null;
      }

      // Remove "Bearer " prefix if present
      let cleanToken = token;
      if (cleanToken.startsWith('Bearer ')) {
        cleanToken = cleanToken.substring(7);
      }

      const url = `${wsUrl}?token=${encodeURIComponent(cleanToken)}`;
      logDebug('Connecting to WebSocket', { url: wsUrl });

      ws.current = new WebSocket(url);

      ws.current.onopen = () => {
        logDebug('WebSocket connected successfully');
        reconnectAttemptsRef.current = 0; // Reset attempts on successful connection
        setConnectionState({
          isConnected: true,
          reconnectAttempts: 0,
          lastError: undefined,
        });
        onConnectionChange?.(true);
        startPingInterval();
      };

      ws.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          logDebug('Message received', { type: message.type });

          switch (message.type) {
            case 'invoice_status_update':
              if (message.data && onStatusUpdate) {
                const notification = message.data as OrderStatusNotification;
                logDebug('Order status update received', {
                  orderId: notification.order_id,
                  status: notification.new_status,
                });
                onStatusUpdate(notification);
              }
              break;
            case 'connection':
              logDebug('Connection established', message.data);
              setConnectionState(prev => ({
                ...prev,
                connectionId: message.data?.connection_id,
              }));
              break;
            case 'error':
              const errorMsg = message.data?.error || 'Unknown WebSocket error';
              logDebug('WebSocket error received', errorMsg);
              setConnectionState(prev => ({ ...prev, lastError: errorMsg }));
              onError?.(errorMsg);
              break;
            case 'pong':
              logDebug('Pong received');
              break;
            default:
              logDebug('Unknown message type received', message.type);
          }
        } catch (error) {
          logDebug('Failed to parse WebSocket message', error);
          onError?.('Failed to parse server message');
        }
      };

      ws.current.onclose = (event) => {
        logDebug(`WebSocket connection closed (code: ${event.code})`, { reason: event.reason });

        setConnectionState({
          isConnected: false,
          reconnectAttempts: reconnectAttemptsRef.current,
          lastError: event.reason || `Connection closed (${event.code})`,
        });
        onConnectionChange?.(false);
        clearTimeouts();

        // Attempt to reconnect only if enabled and haven't exceeded max attempts
        if (enabled && reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++;
          const delay = 2000 * reconnectAttemptsRef.current; // Linear backoff: 2s, 4s, 6s, 8s, 10s

          logDebug(`Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts}) in ${delay}ms`);

          setConnectionState(prev => ({ ...prev, reconnectAttempts: reconnectAttemptsRef.current }));

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, delay);
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          logDebug('Max reconnection attempts reached');
          const errorMsg = 'Unable to reconnect to the server. Please check your connection and refresh the page.';
          setConnectionState(prev => ({ ...prev, lastError: errorMsg }));
          onError?.(errorMsg);
          toast('Connection Lost', {
            description: errorMsg,
            duration: 10000,
          });
        }
      };

      ws.current.onerror = (error) => {
        logDebug('WebSocket error occurred', error);
        setConnectionState(prev => ({ ...prev, lastError: 'Connection error occurred' }));
      };

    } catch (error) {
      logDebug('Failed to create WebSocket connection', error);
      const errorMsg = 'Failed to establish WebSocket connection';
      setConnectionState(prev => ({ ...prev, lastError: errorMsg }));
      onError?.(errorMsg);
    }
  }, [enabled, token, wsUrl]); // Keep minimal dependencies to avoid infinite loops

  const disconnect = useCallback(() => {
    logDebug('Disconnecting WebSocket');

    // Clear all timers and intervals first
    clearTimeouts();
    reconnectAttemptsRef.current = maxReconnectAttempts; // Prevent reconnection

    if (ws.current) {
      try {
        // Remove all event listeners to prevent memory leaks
        ws.current.onopen = null;
        ws.current.onmessage = null;
        ws.current.onclose = null;
        ws.current.onerror = null;

        // Close the connection
        ws.current.close(1000, 'Manual disconnect');
        ws.current = null;

        logDebug('WebSocket connection closed and cleaned up');
      } catch (error) {
        logDebug('Error during WebSocket cleanup', error);
        // Force null the reference even if close fails
        ws.current = null;
      }
    }

    // Reset connection state
    setConnectionState({
      isConnected: false,
      reconnectAttempts: 0,
      lastError: undefined,
      connectionId: undefined,
    });
    onConnectionChange?.(false);

    logDebug('WebSocket disconnect completed');
  }, [logDebug, clearTimeouts, onConnectionChange]);

  const sendMessage = useCallback((message: Omit<WebSocketMessage, 'timestamp'>) => {
    if (!ws.current || ws.current.readyState !== WebSocket.OPEN) {
      const errorMsg = 'WebSocket is not connected';
      logDebug(errorMsg);
      onError?.(errorMsg);
      return false;
    }

    try {
      const fullMessage: WebSocketMessage = {
        ...message,
        timestamp: new Date().toISOString(),
      };
      
      ws.current.send(JSON.stringify(fullMessage));
      logDebug('Message sent', { type: message.type });
      return true;
    } catch (error) {
      logDebug('Failed to send message', error);
      onError?.('Failed to send message');
      return false;
    }
  }, [logDebug, onError]);

  // Initialize connection when enabled and token is available
  useEffect(() => {
    console.log('🔌 [WebSocket] useEffect triggered', { enabled, hasToken: !!token });

    if (enabled && token) {
      console.log('🔌 [WebSocket] Conditions met, attempting to connect');
      reconnectAttemptsRef.current = 0; // Reset attempts when starting fresh
      connect();
    } else {
      console.log('🔌 [WebSocket] Conditions not met, ensuring disconnected', { enabled, hasToken: !!token });
      // Ensure we're disconnected when not enabled
      if (ws.current) {
        disconnect();
      }
    }

    return () => {
      disconnect();
    };
  }, [enabled, token]); // Removed connect/disconnect from dependencies to prevent loops

  // Comprehensive cleanup on unmount
  useEffect(() => {
    return () => {
      logDebug('Component unmounting - performing cleanup');

      // Clear all timers and intervals
      clearTimeouts();

      // Force disconnect to ensure complete cleanup
      if (ws.current) {
        try {
          // Remove all event listeners
          ws.current.onopen = null;
          ws.current.onmessage = null;
          ws.current.onclose = null;
          ws.current.onerror = null;

          // Close connection
          ws.current.close(1000, 'Component unmount');
          ws.current = null;
        } catch (error) {
          console.warn('Error during unmount cleanup:', error);
          ws.current = null;
        }
      }

      // Reset reconnection attempts
      reconnectAttemptsRef.current = maxReconnectAttempts;

      logDebug('Unmount cleanup completed');
    };
  }, [logDebug, clearTimeouts]); // Include dependencies for proper cleanup

  return {
    ...connectionState,
    connect,
    disconnect,
    sendMessage,
  };
};

export default useOrderWebSocket;
