import { useQuery } from "@tanstack/react-query";
import { couponService } from "@/services/api";
import { ListCouponsSuccessResponse } from "@/types/api";

export const useCoupons = (page: number = 1, limit: number = 10) => {
  return useQuery<ListCouponsSuccessResponse>({
    queryKey: ["coupons", page, limit],
    queryFn: async () => {
      const response = await couponService.getCoupons(page, limit);
      return response.data;
    },
  });
}; 