import { useCallback, useEffect, useRef, useState } from 'react';

// Keep the same OrderStatusNotification interface for compatibility
export interface OrderStatusNotification {
  order_id: string;
  invoice_id: number;
  new_status: string;
  old_status?: string;
  status_description: string;
  message: string;
  updated_by: string;
  updater_name?: string;
  user_id: number;
  company_id: number;
  company_name?: string;
  timestamp: string;
}

// Secure push interfaces - Web Push subscription data only
export interface SecurePushTokenRequest {
  device_id: string;
  platform: 'web' | 'android' | 'ios';
  app_version: string;
  subscription: {
    endpoint: string;
    keys: {
      p256dh: string;
      auth: string;
    };
  };
  device_model?: string;
  os_version?: string;
}

export interface SecurePushTokenResponse {
  data: {
    push_token: string;
    device_id: string;
    platform: string;
    expires_at: string;
    created_at: string;
  };
}

export interface SecurePushConnectionState {
  isRegistered: boolean;
  registrationAttempts: number;
  lastError?: string;
  pushToken?: string;
  deviceId?: string;
  lastRegistered?: Date;
}

export interface UseOrderPushNotificationsProps {
  token: string;
  apiUrl?: string;
  onStatusUpdate?: (notification: OrderStatusNotification) => void;
  onRegistrationChange?: (registered: boolean) => void;
  onError?: (error: string) => void;
  enabled?: boolean;
}

/**
 * Secure push notification hook using Web Push API
 * All FCM token generation and management happens on the backend
 */
const useOrderPushNotifications = ({
  token,
  apiUrl = import.meta.env.VITE_NODE_ENV === "development"
    ? "http://localhost:8080"
    : "https://api.izymercado.com.br",
  onStatusUpdate,
  onRegistrationChange,
  onError,
  enabled = true,
}: UseOrderPushNotificationsProps) => {
  const [connectionState, setConnectionState] = useState<SecurePushConnectionState>({
    isRegistered: false,
    registrationAttempts: 0,
  });

  const registrationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const registrationAttemptsRef = useRef(0);
  const maxRegistrationAttempts = 3;
  const isRegisteringRef = useRef(false); // Prevent multiple simultaneous registrations
  const initializationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const logDebug = useCallback((message: string, data?: any) => {
    console.log(`🔔 [FCM-Secure] ${message}`, data || '');
  }, []);

  // Handle push messages (foreground)
  const handlePushMessage = useCallback((payload: any) => {
    try {
      const data = payload.data || payload;

      if (data && data.type === 'order_status_update') {
        const notification: OrderStatusNotification = {
          order_id: data.order_id,
          invoice_id: parseInt(data.invoice_id),
          new_status: data.new_status,
          old_status: data.old_status || undefined,
          status_description: data.status_description,
          message: data.message,
          updated_by: data.updated_by,
          updater_name: data.updater_name || undefined,
          user_id: parseInt(data.user_id),
          company_id: parseInt(data.company_id),
          company_name: data.company_name || undefined,
          timestamp: data.timestamp,
        };

        logDebug('Order status update received', {
          orderId: notification.order_id,
          status: notification.new_status,
        });

        onStatusUpdate?.(notification);
      }
    } catch (error) {
      logDebug('Failed to parse push message', error);
      onError?.('Failed to parse notification message');
    }
  }, [onStatusUpdate, onError, logDebug]);

  // Get VAPID key from backend and create Web Push subscription
  const createWebPushSubscription = useCallback(async () => {
    if (!enabled || !token) {
      logDebug('Push notifications disabled or no token provided');
      return null;
    }

    try {
      // Check if service worker and push messaging are supported
      if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
        throw new Error('Push messaging is not supported in this browser');
      }

      // Request notification permission
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        throw new Error('Notification permission denied');
      }

      // Register service worker
      const swPath = import.meta.env.VITE_PUSH_SW_PATH || '/push-sw.js';
      logDebug('Registering service worker', { path: swPath });

      let registration: ServiceWorkerRegistration;
      try {
        registration = await navigator.serviceWorker.register(swPath, {
          scope: '/',
        });
        logDebug('Service worker registered successfully', {
          scope: registration.scope,
          state: registration.installing?.state || registration.waiting?.state || registration.active?.state
        });

        await navigator.serviceWorker.ready;
        logDebug('Service worker ready');
      } catch (swError) {
        logDebug('Service worker registration failed', swError);
        throw new Error(`Service worker registration failed: ${swError.message}`);
      }

      // Get VAPID key from backend (secure approach)
      let vapid_key: string;
      try {
        const vapidResponse = await fetch(`${apiUrl}/v1/secure-push/vapid-key`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!vapidResponse.ok) {
          if (vapidResponse.status === 404) {
            throw new Error('VAPID key endpoint not implemented on backend. Please implement GET /v1/secure-push/vapid-key');
          }
          throw new Error(`Failed to get VAPID key: ${vapidResponse.status} ${vapidResponse.statusText}`);
        }

        const vapidData = await vapidResponse.json();
        vapid_key = vapidData.vapid_key || vapidData.key || vapidData.data?.vapid_key;

        if (!vapid_key) {
          throw new Error('VAPID key not found in server response');
        }

        logDebug('VAPID key received from server');
      } catch (vapidError) {
        logDebug('VAPID key fetch failed', vapidError);

        // Development fallback - remove this in production!
        if (import.meta.env.VITE_NODE_ENV === 'development') {
          logDebug('Using development fallback VAPID key');
          const fallbackVapidKey = import.meta.env.VITE_FALLBACK_VAPID_KEY;

          if (fallbackVapidKey) {
            vapid_key = fallbackVapidKey;
            logDebug('Using fallback VAPID key from environment');
          } else {
            throw new Error('Backend VAPID endpoint not ready. Please implement GET /v1/secure-push/vapid-key or set VITE_FALLBACK_VAPID_KEY for development');
          }
        } else {
          throw vapidError;
        }
      }

      // Create push subscription
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: vapid_key,
      });

      logDebug('Web Push subscription created', {
        endpoint: subscription.endpoint.substring(0, 50) + '...',
      });

      // Set up message listener for foreground notifications
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'PUSH_NOTIFICATION') {
          handlePushMessage(event.data.payload);
        }
      });

      return subscription;
    } catch (error) {
      logDebug('Failed to create Web Push subscription', error);
      throw error;
    }
  }, [enabled, token, apiUrl, logDebug]);

  // Generate device ID
  const getDeviceId = useCallback(() => {
    let deviceId = localStorage.getItem('device_id');
    if (!deviceId) {
      deviceId = 'web_' + Math.random().toString(36).substring(2, 11) + '_' + Date.now();
      localStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  }, []);

  // Register Web Push subscription with your backend
  const registerPushToken = useCallback(async (subscription: PushSubscription) => {
    if (!token) {
      throw new Error('No authentication token available');
    }

    const requestData: SecurePushTokenRequest = {
      device_id: getDeviceId(),
      platform: 'web',
      app_version: '1.0.0',
      subscription: {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('p256dh')!))),
          auth: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('auth')!))),
        },
      },
      device_model: navigator.userAgent,
      os_version: navigator.userAgent.includes('Windows') ? 'Windows' :
                  navigator.userAgent.includes('Mac') ? 'macOS' :
                  navigator.userAgent.includes('Linux') ? 'Linux' : 'Unknown',
    };

    const response = await fetch(`${apiUrl}/v1/secure-push/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const responseData: SecurePushTokenResponse = await response.json();
    logDebug('Push token registered successfully', responseData);

    // Store push token locally
    localStorage.setItem('secure_push_token', responseData.data.push_token);
    localStorage.setItem('push_token_expires_at', responseData.data.expires_at);

    return responseData;
  }, [token, apiUrl, getDeviceId, logDebug]);

  // Validate push token with your backend
  const validatePushToken = useCallback(async (pushToken: string) => {
    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await fetch(`${apiUrl}/v1/secure-push/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        push_token: pushToken,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const responseData = await response.json();
    logDebug('Push token validation result', responseData);

    return responseData;
  }, [token, apiUrl, logDebug]);

  // Revoke push token from your backend
  const revokePushToken = useCallback(async (pushToken: string) => {
    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await fetch(`${apiUrl}/v1/secure-push/revoke`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        push_token: pushToken,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    // Remove from local storage
    localStorage.removeItem('secure_push_token');
    localStorage.removeItem('push_token_expires_at');

    const responseData = await response.json();
    logDebug('Push token revoked successfully', responseData);

    return responseData;
  }, [token, apiUrl, logDebug]);

  // Main registration function
  const register = useCallback(async (forceRetry = false) => {
    if (!enabled || !token) {
      logDebug('Registration disabled or no token provided');
      return;
    }

    // Prevent multiple simultaneous registrations
    if (isRegisteringRef.current) {
      logDebug('Registration already in progress, skipping duplicate call');
      return;
    }

    // Allow manual retry even if max attempts reached
    if (!forceRetry && registrationAttemptsRef.current >= maxRegistrationAttempts) {
      logDebug('Max registration attempts reached');
      return;
    }

    // Clear any existing retry timeout
    if (registrationTimeoutRef.current) {
      clearTimeout(registrationTimeoutRef.current);
      registrationTimeoutRef.current = null;
    }

    // Set registration in progress flag
    isRegisteringRef.current = true;

    try {
      logDebug('Starting secure push notification registration', {
        attempt: registrationAttemptsRef.current + 1,
        forceRetry
      });

      // Create Web Push subscription
      const subscription = await createWebPushSubscription();
      if (!subscription) {
        throw new Error('Failed to create Web Push subscription');
      }

      // Register with your backend
      const response = await registerPushToken(subscription);

      // Update state
      setConnectionState({
        isRegistered: true,
        registrationAttempts: registrationAttemptsRef.current,
        lastError: undefined,
        pushToken: response.data.push_token,
        deviceId: response.data.device_id,
        lastRegistered: new Date(),
      });

      registrationAttemptsRef.current = 0;
      onRegistrationChange?.(true);

      logDebug('Secure push registration completed successfully');

      // Set up message listener for foreground notifications
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'PUSH_NOTIFICATION') {
          handlePushMessage(event.data.payload);
        }
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logDebug('Secure push registration failed', errorMessage);

      registrationAttemptsRef.current++;
      setConnectionState(prev => ({
        ...prev,
        isRegistered: false,
        registrationAttempts: registrationAttemptsRef.current,
        lastError: errorMessage,
      }));

      onRegistrationChange?.(false);
      onError?.(errorMessage);

      // Schedule retry if not at max attempts and not a manual retry
      if (!forceRetry && registrationAttemptsRef.current < maxRegistrationAttempts) {
        const delay = 5000 * registrationAttemptsRef.current; // 5s, 10s, 15s
        logDebug(`Scheduling registration retry in ${delay}ms`);

        registrationTimeoutRef.current = setTimeout(() => {
          register();
        }, delay);
      }
    } finally {
      // Always clear the registration flag
      isRegisteringRef.current = false;
    }
  }, [enabled, token, createWebPushSubscription, registerPushToken, onRegistrationChange, onError, logDebug]);

  // Manual retry function that resets attempts counter
  const retryRegistration = useCallback(async () => {
    logDebug('Manual retry registration requested', {
      currentAttempts: registrationAttemptsRef.current,
      isRegistered: connectionState.isRegistered
    });

    // Reset attempts counter for manual retry
    registrationAttemptsRef.current = 0;

    // Clear any error state
    setConnectionState(prev => ({
      ...prev,
      lastError: undefined,
      registrationAttempts: 0,
    }));

    // Force retry registration
    await register(true);
  }, [register, logDebug, connectionState.isRegistered]);

  // Unregister function
  const unregister = useCallback(async () => {
    logDebug('Unregistering push notifications');

    try {
      // Clear timeout
      if (registrationTimeoutRef.current) {
        clearTimeout(registrationTimeoutRef.current);
        registrationTimeoutRef.current = null;
      }

      // Get stored push token
      const storedPushToken = localStorage.getItem('secure_push_token');

      // Revoke from server
      if (token && storedPushToken) {
        await revokePushToken(storedPushToken);
      }

      // Reset state but don't prevent re-registration
      registrationAttemptsRef.current = 0;
      setConnectionState({
        isRegistered: false,
        registrationAttempts: 0,
        lastError: undefined,
      });

      onRegistrationChange?.(false);
      logDebug('Push notification unregistration completed');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logDebug('Push notification unregistration failed', errorMessage);
      onError?.(errorMessage);
    }
  }, [token, revokePushToken, onRegistrationChange, onError, logDebug]);

  // Utility functions
  const isTokenExpired = useCallback(() => {
    const expiresAt = localStorage.getItem('push_token_expires_at');
    if (!expiresAt) return true;
    return new Date() >= new Date(expiresAt);
  }, []);

  const getStoredPushToken = useCallback(() => {
    if (isTokenExpired()) {
      localStorage.removeItem('secure_push_token');
      localStorage.removeItem('push_token_expires_at');
      return null;
    }
    return localStorage.getItem('secure_push_token');
  }, [isTokenExpired]);

  const validateStoredToken = useCallback(async () => {
    const storedToken = getStoredPushToken();
    if (!storedToken) return false;

    try {
      const result = await validatePushToken(storedToken);
      return result.data?.valid || false;
    } catch (error) {
      logDebug('Token validation failed', error);
      return false;
    }
  }, [getStoredPushToken, validatePushToken, logDebug]);

  // Auto-register when enabled and token is available with debounce
  useEffect(() => {
    if (enabled && token && !connectionState.isRegistered) {
      logDebug('Auto-registration triggered', { enabled, hasToken: !!token, isRegistered: connectionState.isRegistered });

      // Clear any existing timeout
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
      }

      // Debounce auto-registration to prevent multiple rapid calls
      initializationTimeoutRef.current = setTimeout(() => {
        // Check if we have a valid stored token first
        const storedToken = localStorage.getItem('secure_push_token');
        const expiresAt = localStorage.getItem('push_token_expires_at');

        if (storedToken && expiresAt && !isTokenExpired()) {
          logDebug('Found valid stored push token, validating with server');

          validateStoredToken().then((isValid) => {
            if (isValid) {
              logDebug('Stored token is valid, using existing registration');
              setConnectionState(prev => ({
                ...prev,
                isRegistered: true,
                pushToken: storedToken,
                lastError: undefined,
                registrationAttempts: 0,
              }));
              onRegistrationChange?.(true);
            } else {
              logDebug('Stored token is invalid, starting fresh registration');
              register();
            }
          }).catch((error) => {
            logDebug('Token validation failed, starting fresh registration', error);
            register();
          });
        } else {
          logDebug('No valid stored token found, starting fresh registration');
          register();
        }
      }, 1000); // 1 second debounce for auto-registration
    }

    return () => {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
        initializationTimeoutRef.current = null;
      }
    };
  }, [enabled, token, connectionState.isRegistered, register, onRegistrationChange, logDebug, isTokenExpired, validateStoredToken]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (registrationTimeoutRef.current) {
        clearTimeout(registrationTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    isRegistered: connectionState.isRegistered,
    registrationAttempts: connectionState.registrationAttempts,
    lastError: connectionState.lastError,
    pushToken: connectionState.pushToken,
    deviceId: connectionState.deviceId,
    lastRegistered: connectionState.lastRegistered,

    // Actions
    register,
    unregister,
    retryRegistration,
    validateStoredToken,
    getStoredPushToken,
    isTokenExpired,

    // For compatibility with WebSocket interface
    connect: register,
    disconnect: unregister,
    reconnect: retryRegistration,
    isConnected: connectionState.isRegistered,
    reconnectAttempts: connectionState.registrationAttempts,
    connectionId: connectionState.pushToken,
  };
};

export default useOrderPushNotifications;
