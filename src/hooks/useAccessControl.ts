import { useAuth } from "@/contexts/AuthContext";

/**
 * Hook for managing access control based on user role and company associations
 */
export const useAccessControl = () => {
  const { userRole, userCompanies } = useAuth();

  /**
   * Check if user has access to a specific company
   */
  const hasCompanyAccess = (companyExternalId: string): boolean => {
    // Admin has access to all companies
    if (userRole === 'admin') {
      return true;
    }
    
    // Partner only has access to their assigned companies
    if (userRole === 'partner') {
      return userCompanies.includes(companyExternalId);
    }
    
    return false;
  };

  /**
   * Check if user is admin
   */
  const isAdmin = (): boolean => {
    return userRole === 'admin';
  };

  /**
   * Check if user is partner
   */
  const isPartner = (): boolean => {
    return userRole === 'partner';
  };

  /**
   * Check if user can edit company profile data
   * Only admins can edit company profiles
   */
  const canEditCompanyProfile = (companyExternalId?: string): boolean => {
    return userRole === 'admin';
  };

  /**
   * Check if user can manage company products
   * Both admins and partners (with access) can manage products
   */
  const canManageProducts = (companyExternalId?: string): boolean => {
    if (userRole === 'admin') {
      return true;
    }
    
    if (userRole === 'partner' && companyExternalId) {
      return hasCompanyAccess(companyExternalId);
    }
    
    return false;
  };

  /**
   * Check if user can manage orders
   * Both admins and partners (with access) can manage orders
   */
  const canManageOrders = (companyExternalId?: string): boolean => {
    if (userRole === 'admin') {
      return true;
    }
    
    if (userRole === 'partner' && companyExternalId) {
      return hasCompanyAccess(companyExternalId);
    }
    
    return false;
  };

  /**
   * Check if user can view billing/revenue data
   * Both admins and partners (with access) can view billing
   */
  const canViewBilling = (companyExternalId?: string): boolean => {
    if (userRole === 'admin') {
      return true;
    }
    
    if (userRole === 'partner' && companyExternalId) {
      return hasCompanyAccess(companyExternalId);
    }
    
    return false;
  };

  /**
   * Check if user can create new companies
   * Only admins can create companies
   */
  const canCreateCompanies = (): boolean => {
    return userRole === 'admin';
  };

  /**
   * Check if user can manage global products/categories
   * Only admins can manage global products and categories
   */
  const canManageGlobalProducts = (): boolean => {
    return userRole === 'admin';
  };

  /**
   * Check if user can manage coupons
   * Only admins can manage coupons
   */
  const canManageCoupons = (): boolean => {
    return userRole === 'admin';
  };

  /**
   * Get filtered companies based on user access
   * Returns all companies for admin, only assigned companies for partner
   */
  const getAccessibleCompanies = (allCompanies: any[]): any[] => {
    if (userRole === 'admin') {
      return allCompanies;
    }
    
    if (userRole === 'partner') {
      return allCompanies.filter(company => 
        userCompanies.includes(company.external_id)
      );
    }
    
    return [];
  };

  return {
    userRole,
    userCompanies,
    hasCompanyAccess,
    isAdmin,
    isPartner,
    canEditCompanyProfile,
    canManageProducts,
    canManageOrders,
    canViewBilling,
    canCreateCompanies,
    canManageGlobalProducts,
    canManageCoupons,
    getAccessibleCompanies,
  };
};
