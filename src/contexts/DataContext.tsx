import React, { createContext, useContext, useState, useEffect } from 'react';
import { productService, categoryService, companyService } from '@/services/api';
import { GetActiveProductsResponse } from '@/types/api';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface Category {
  external_id: string;
  name: string;
  image: string;
}

interface Company {
  external_id: string;
  name: string;
  // Add other relevant fields as needed
}

interface DataContextType {
  products: GetActiveProductsResponse[];
  categories: Category[];
  companies: Company[];
  isLoading: boolean;
  refreshProducts: () => Promise<void>;
  refreshCategories: () => Promise<void>;
  refreshCompanies: () => Promise<void>;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [products, setProducts] = useState<GetActiveProductsResponse[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { userRole, isAuthenticated, loading: authLoading } = useAuth();

  // Load data when admin user is authenticated - restore original behavior
  useEffect(() => {
    const loadAdminData = async () => {
      if (!authLoading && isAuthenticated && userRole === 'admin') {
        console.log('🔧 Admin user authenticated, loading all data...');
        setIsLoading(true);
        try {
          await Promise.all([
            fetchAllProducts(),
            fetchAllCategories(),
            fetchAllCompanies()
          ]);
          console.log('✅ Admin data loaded successfully');
        } catch (error) {
          console.error('Error loading admin data:', error);
        } finally {
          setIsLoading(false);
        }
      } else if (!authLoading && (!isAuthenticated || userRole !== 'admin')) {
        setIsLoading(false);
      }
    };

    loadAdminData();
  }, [userRole, isAuthenticated, authLoading]);

  // Load from localStorage on mount - restore original behavior
  useEffect(() => {
    const storedProducts = localStorage.getItem('izy_products');
    const storedCategories = localStorage.getItem('izy_categories');
    const storedCompanies = localStorage.getItem('izy_companies');

    if (storedProducts) {
      try {
        const parsedProducts = JSON.parse(storedProducts);
        setProducts(parsedProducts);
        console.log('📦 Loaded products from localStorage:', parsedProducts.length);
      } catch (error) {
        console.error('Error parsing stored products:', error);
        localStorage.removeItem('izy_products');
      }
    }
    if (storedCategories) {
      try {
        const parsedCategories = JSON.parse(storedCategories);
        setCategories(parsedCategories);
        console.log('📂 Loaded categories from localStorage:', parsedCategories.length);
      } catch (error) {
        console.error('Error parsing stored categories:', error);
        localStorage.removeItem('izy_categories');
      }
    }
    if (storedCompanies) {
      try {
        const parsedCompanies = JSON.parse(storedCompanies);
        setCompanies(parsedCompanies);
        console.log('🏢 Loaded companies from localStorage:', parsedCompanies.length);
      } catch (error) {
        console.error('Error parsing stored companies:', error);
        localStorage.removeItem('izy_companies');
      }
    }
  }, []);

  const fetchAllProducts = async () => {
    try {
      const response = await productService.getProducts(1, 10000);
      setProducts(response.data.data);
      localStorage.setItem('izy_products', JSON.stringify(response.data.data));
      console.log('📦 Products loaded:', response.data.data.length);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch products',
        variant: 'destructive',
      });
    }
  };

  const fetchAllCategories = async () => {
    try {
      const response = await categoryService.getCategories(1, 10000);
      setCategories(response.data.data);
      localStorage.setItem('izy_categories', JSON.stringify(response.data.data));
      console.log('📂 Categories loaded:', response.data.data.length);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch categories',
        variant: 'destructive',
      });
    }
  };

  const fetchAllCompanies = async () => {
    try {
      const response = await companyService.getCompanies();
      setCompanies(response.data.data);
      localStorage.setItem('izy_companies', JSON.stringify(response.data.data));
      console.log('🏢 Companies loaded:', response.data.data.length);
    } catch (error) {
      console.error('Error fetching companies:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch companies',
        variant: 'destructive',
      });
    }
  };

  const refreshProducts = async () => {
    if (userRole !== 'admin') {
      console.warn('Products refresh is admin-only. User does not have admin access.');
      return;
    }
    setIsLoading(true);
    await fetchAllProducts();
    setIsLoading(false);
  };

  const refreshCategories = async () => {
    if (userRole !== 'admin') {
      console.warn('Categories refresh is admin-only. User does not have admin access.');
      return;
    }
    setIsLoading(true);
    await fetchAllCategories();
    setIsLoading(false);
  };

  const refreshCompanies = async () => {
    if (userRole !== 'admin') {
      console.warn('Companies refresh is admin-only. User does not have admin access.');
      return;
    }
    setIsLoading(true);
    await fetchAllCompanies();
    setIsLoading(false);
  };

  return (
    <DataContext.Provider
      value={{
        products,
        categories,
        companies,
        isLoading,
        refreshProducts,
        refreshCategories,
        refreshCompanies,
      }}
    >
      {children}
    </DataContext.Provider>
  );
};

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}; 