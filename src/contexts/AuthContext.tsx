import React, { createContext, useContext, useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { authService, api, companyService, orderService, resetLogoutState } from "@/services/api";
import { LoginSuccessResponse, GetMyCompaniesSuccessResponse } from "@/types/api";
// Removed: useOrderPushNotifications - consolidated to FCM only
import useBrowserNotifications from "@/hooks/useBrowserNotifications";
import useAudioNotifications from "@/hooks/useAudioNotifications";
import useFCMNotifications, { FCMNotification } from "@/hooks/useFCMNotifications";
import useBackgroundNotifications from "@/hooks/useBackgroundNotifications";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  saveTokenWithTimestamp,
  hasValidTokens,
  clearAllTokens,
  getTokenTimeRemaining,
  formatTimeRemaining
} from "@/utils/tokenManager";

interface NotificationItem {
  id: string;
  type: string;
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  data?: any;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: any;
  userRole: 'admin' | 'partner' | null;
  userCompanies: string[];
  dashboardUrl: string;
  login: (data: LoginSuccessResponse) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
  forceLogout: () => void;

  // Notification system (FCM-based for partner users)
  notifications: {
    notifications: NotificationItem[];
    unreadCount: number;
    lastNotificationTime?: Date;
    markAsRead: (notificationId: string) => void;
    markAllAsRead: () => void;
    clearAllNotifications: () => void;
    browserNotifications: ReturnType<typeof useBrowserNotifications>;
    audioNotifications: ReturnType<typeof useAudioNotifications>;
    fcmNotifications: {
      isInitialized: boolean;
      isRegistered: boolean;
      hasPermission: boolean;
      error: string | null;
      deviceId: string | null;
      initialize: () => Promise<void>;
      refreshToken: () => Promise<void>;
      cleanup: () => Promise<void>;
    };
  };
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const NOTIFICATIONS_STORAGE_KEY = 'orderNotifications';
const MAX_STORED_NOTIFICATIONS = 50;

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [userRole, setUserRole] = useState<'admin' | 'partner' | null>(null);
  const [userCompanies, setUserCompanies] = useState<string[]>([]);
  const [dashboardUrl, setDashboardUrl] = useState('/admin/dashboard');
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Notification state
  const [notificationState, setNotificationState] = useState<{
    items: NotificationItem[];
    unreadCount: number;
    lastNotificationTime?: Date;
  }>({
    items: [],
    unreadCount: 0,
  });

  // Load notifications from localStorage
  const loadStoredNotifications = () => {
    try {
      const stored = localStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        const items = parsed.items.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp),
          // Ensure backward compatibility - convert 'read' to 'isRead' if needed
          isRead: item.isRead !== undefined ? item.isRead : item.read || false,
        }));

        // Recalculate unreadCount to ensure accuracy
        const unreadCount = items.filter((item: NotificationItem) => !item.isRead).length;

        return {
          items,
          unreadCount,
          lastNotificationTime: parsed.lastNotificationTime
            ? new Date(parsed.lastNotificationTime)
            : undefined,
        };
      }
    } catch (error) {
      console.error('Failed to load stored notifications', error);
    }
    return { items: [], unreadCount: 0 };
  };

  // Save notifications to localStorage
  const storeNotifications = (notifications: NotificationItem[]) => {
    try {
      const itemsToSave = notifications.slice(0, MAX_STORED_NOTIFICATIONS);
      localStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify({ items: itemsToSave }));
    } catch (error) {
      console.error('Failed to save notifications', error);
    }
  };

  // Removed: addNotification - now handled by FCM notification handler

  // Mark notification as read
  const markAsRead = (notificationId: string) => {
    setNotificationState(prev => {
      const itemIndex = prev.items.findIndex(item => item.id === notificationId);
      if (itemIndex === -1 || prev.items[itemIndex].isRead) return prev;

      const newItems = [...prev.items];
      newItems[itemIndex] = { ...newItems[itemIndex], isRead: true };

      const newState = {
        ...prev,
        items: newItems,
        unreadCount: Math.max(0, prev.unreadCount - 1),
      };

      storeNotifications(newState.items);
      return newState;
    });
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotificationState(prev => {
      if (prev.unreadCount === 0) return prev;

      const newItems = prev.items.map(item => ({ ...item, isRead: true }));
      const newState = { ...prev, items: newItems, unreadCount: 0 };

      storeNotifications(newState.items);
      return newState;
    });
  };

  // Clear all notifications
  const clearAllNotifications = () => {
    const newState = { items: [], unreadCount: 0 };
    setNotificationState(newState);
    storeNotifications([]);
  };

  // Get current token for push notifications (must be declared before hooks that use it)
  const token = localStorage.getItem('token') || '';

  // Initialize notification system hooks (always available)
  const browserNotifications = useBrowserNotifications();
  const audioNotifications = useAudioNotifications();

  // Initialize background notifications handler for partner users
  useBackgroundNotifications({
    enabled: isAuthenticated && !!token && !loading && userRole === 'partner',
    forceRefresh: () => {
      console.log("🔄 Force refresh callback triggered from background notifications");
      if (queryClient) {
        // Nuclear option: clear all order-related queries and force refetch
        queryClient.removeQueries({
          predicate: (query) => {
            const key = query.queryKey[0];
            return key === 'orders' || key === 'partner-orders' || key === 'admin-orders';
          }
        });

        // Force a complete reload of partner orders
        queryClient.prefetchQuery({
          queryKey: ['partner-orders', 1],
          queryFn: () => orderService.getOrders(1, 10)
        });
      }
    },
    onNotificationProcessed: (notification) => {
      console.log("🔄 Background notification processed:", notification);

      // Force immediate query invalidation and refetch
      if (queryClient) {
        console.log("🔄 Force invalidating all order queries from background notification");

        // Invalidate all order-related queries immediately
        queryClient.invalidateQueries({
          predicate: (query) => {
            const key = query.queryKey[0];
            return key === 'orders' || key === 'partner-orders' || key === 'admin-orders';
          },
          refetchType: 'all'
        });

        // Also force refetch partner-orders specifically
        queryClient.refetchQueries({
          predicate: (query) => {
            const key = query.queryKey[0];
            return key === 'partner-orders';
          }
        });
      }

      // Convert background notification to FCM notification format
      const fcmNotification: FCMNotification = {
        id: notification.id,
        title: notification.title,
        body: notification.body,
        type: notification.type,
        data: notification.data,
        timestamp: new Date(notification.timestamp),
        read: false
      };

      // Add to notification state (similar to foreground notifications)
      setNotificationState(prev => {
        const newNotificationItem = {
          id: notification.id,
          type: notification.type as any,
          title: notification.title,
          message: notification.body,
          timestamp: new Date(notification.timestamp),
          isRead: false,
          data: notification.data
        };

        const newState = {
          items: [newNotificationItem, ...prev.items],
          unreadCount: prev.unreadCount + 1,
          lastNotificationTime: new Date(notification.timestamp)
        };

        // Store in localStorage
        storeNotifications(newState.items);
        return newState;
      });

      // Show browser notification if available
      if (browserNotifications.canShowNotifications) {
        browserNotifications.showNotification(notification.title, {
          body: notification.body,
          icon: '/favicon.ico',
          tag: notification.type,
          data: notification.data
        });
      }

      // Play audio notification
      if (audioNotifications.isSupported && audioNotifications.settings.enabled) {
        const soundType = notification.type === 'new-order' ? 'new-order' :
                         notification.type === 'new_order_received' ? 'new-order' :
                         notification.type === 'payment_confirmed' ? 'payment' : 'status-update';
        console.log("🔊 Playing audio for background notification:", soundType);
        audioNotifications.playNotificationSound(soundType);
      }

      // Show toast notification with navigation (same as foreground)
      const isOrderRelated = notification.type === 'new-order' ||
                             notification.type === 'new_order_received' ||
                             notification.type === 'order_status_update' ||
                             notification.type === 'payment_confirmed';

      console.log("🔄 Showing toast for background notification:", {
        notificationType: notification.type,
        isOrderRelated,
        userRole
      });

      toast.success(notification.title, {
        description: notification.body,
        duration: 6000,
        dismissible: true,
        action: isOrderRelated ? {
          label: 'Ver Pedidos',
          onClick: () => {
            navigate('/partner/orders');
          },
        } : undefined,
      });
    }
  });

  // Track if permission denial toast has been shown this session
  const [permissionDenialToastShown, setPermissionDenialToastShown] = useState(() => {
    // Check if this is a new session by comparing session storage
    const sessionId = sessionStorage.getItem('fcm_session_id');
    const currentSessionId = Date.now().toString();

    if (!sessionId) {
      // New session - reset the flag and set session ID
      sessionStorage.setItem('fcm_session_id', currentSessionId);
      localStorage.removeItem('fcm_permission_denial_toast_shown');
      return false;
    }

    // Existing session - check if toast was already shown
    return localStorage.getItem('fcm_permission_denial_toast_shown') === 'true';
  });

  // Initialize FCM notifications for partner users only (will handle PIX payments and order updates)
  const fcmNotifications = useFCMNotifications({
    enabled: isAuthenticated && !!token && !loading && userRole === 'partner',
    onNotificationReceived: (notification: FCMNotification) => {
      console.log("🔔 FCM notification received:", notification);

      // Add to notification state
      setNotificationState(prev => {
        const newNotificationItem = {
          id: notification.id,
          type: notification.type as any,
          title: notification.title,
          message: notification.body,
          timestamp: notification.timestamp,
          isRead: notification.read,
          data: notification.data
        };

        const newState = {
          items: [newNotificationItem, ...prev.items],
          unreadCount: prev.unreadCount + (notification.read ? 0 : 1),
          lastNotificationTime: notification.timestamp
        };

        console.log("🔔 Notification state updated:", {
          previousCount: prev.items.length,
          newCount: newState.items.length,
          unreadCount: newState.unreadCount,
          notificationId: notification.id,
          notificationType: notification.type
        });

        // Store in localStorage with the new state
        storeNotifications(newState.items);

        return newState;
      });

      // Show browser notification if available
      if (browserNotifications.canShowNotifications) {
        browserNotifications.showNotification(notification.title, {
          body: notification.body,
          icon: '/favicon.ico',
          tag: notification.type,
          data: notification.data
        });
      }

      // Play audio notification
      if (audioNotifications.isSupported && audioNotifications.settings.enabled) {
        const soundType = notification.type === 'new-order' ? 'new-order' :
                         notification.type === 'payment_confirmed' ? 'payment' : 'status-update';
        audioNotifications.playNotificationSound(soundType);
      }

      // Show toast notification with navigation
      const isOrderRelated = notification.type === 'new-order' ||
                             notification.type === 'new_order_received' ||
                             notification.type === 'order_status_update' ||
                             notification.type === 'payment_confirmed';

      console.log("🔄 Checking if notification is order-related:", {
        notificationType: notification.type,
        isOrderRelated,
        userRole,
        availableTypes: ['new-order', 'new_order_received', 'order_status_update', 'payment_confirmed'],
        exactMatch: {
          'new-order': notification.type === 'new-order',
          'new_order_received': notification.type === 'new_order_received',
          'order_status_update': notification.type === 'order_status_update',
          'payment_confirmed': notification.type === 'payment_confirmed'
        }
      });

      toast.success(notification.title, {
        description: notification.body,
        duration: 6000,
        dismissible: true,
        action: isOrderRelated ? {
          label: 'Ver Pedidos',
          onClick: () => {
            navigate('/partner/orders');
          },
        } : undefined,
      });

      // Automatically refresh orders list for order-related notifications
      if (isOrderRelated && userRole === 'partner') {
        console.log("🔄 Refreshing orders due to FCM notification", {
          notificationType: notification.type,
          notificationId: notification.id,
          userRole,
          queryClientAvailable: !!queryClient,
          notificationData: notification.data
        });

        if (!queryClient) {
          console.error("🔄 QueryClient not available for invalidation");
          return;
        }

        // Invalidate all order-related queries to ensure complete refresh

        // Get all queries before invalidation for debugging
        const allQueries = queryClient.getQueryCache().getAll();
        const orderQueries = allQueries.filter(query =>
          query.queryKey[0] === 'orders' ||
          query.queryKey[0] === 'partner-orders' ||
          query.queryKey[0] === 'partner-orders-metrics' ||
          query.queryKey[0] === 'admin-orders'
        );

        console.log("🔄 Found order queries to invalidate:", orderQueries.map(q => ({
          key: q.queryKey,
          state: q.state.status,
          dataUpdatedAt: q.state.dataUpdatedAt
        })));

        // Invalidate all order-related queries using predicate for comprehensive coverage
        const invalidationResult = queryClient.invalidateQueries({
          predicate: (query) => {
            const key = query.queryKey[0];
            const shouldInvalidate = key === 'orders' ||
                   key === 'partner-orders' ||
                   key === 'partner-orders-metrics' ||
                   key === 'admin-orders';

            if (shouldInvalidate) {
              console.log("🔄 Invalidating query:", query.queryKey);
            }

            return shouldInvalidate;
          }
        });

        console.log("🔄 Order queries invalidated successfully, result:", invalidationResult);
      } else {
        // Temporary: Force refresh for any notification to test if query invalidation works
        console.log("🔄 [DEBUG] Notification not recognized as order-related, but forcing refresh for testing", {
          notificationType: notification.type,
          userRole,
          isOrderRelated
        });

        if (userRole === 'partner' && queryClient) {
          console.log("🔄 [DEBUG] Force invalidating partner-orders queries");
          queryClient.invalidateQueries({
            predicate: (query) => {
              const key = query.queryKey[0];
              return key === 'partner-orders';
            }
          });
        }
      }
    },
    onError: (error: string) => {
      console.error("🔔 FCM error:", error);

      // Handle permission denial specifically to prevent infinite toast loops
      if (error === 'Notification permission not granted') {
        if (!permissionDenialToastShown) {
          toast.error("Notificações Bloqueadas", {
            description: "Você bloqueou as notificações e não receberá alertas quando novos pedidos chegarem. Para ativar, clique no ícone de cadeado na barra de endereços do navegador.",
            duration: 8000
          });

          // Mark as shown for this session
          setPermissionDenialToastShown(true);
          localStorage.setItem('fcm_permission_denial_toast_shown', 'true');
        }
        return;
      }

      // Show other FCM errors normally
      toast.error("Erro nas notificações", {
        description: error
      });
    },
    onPermissionGranted: () => {
      // Reset permission denial toast flag when permission is granted
      console.log("🔔 FCM permission granted - resetting denial toast flag");
      setPermissionDenialToastShown(false);
      localStorage.removeItem('fcm_permission_denial_toast_shown');
    }
  });

  // Consolidated order status update handler (now handled by FCM notifications)
  // This function is no longer needed as FCM handles all notifications

  // Removed: Push notification handlers - consolidated to FCM only

  // Removed: Push notifications - consolidated to FCM only

  // Função para buscar dados do usuário autenticado e suas empresas
  const fetchUser = async () => {
    try {
      const { data } = await api.get("/v1/user/me");
      setUser(data.data);
      setIsAuthenticated(true);

      // Buscar empresas do usuário para determinar role e acesso
      await fetchUserCompanies();

      return true;
    } catch (error: any) {
      // Se for erro de acesso não autorizado, propagar o erro
      if (error.message === "ACESSO NÃO AUTORIZADO!") {
        throw error;
      }

      // Só desconecta se for erro AUTH_REQUIRED (refresh token expirado)
      if (error.message === "AUTH_REQUIRED") {
        forceLogout();
      }
      return false;
    }
  };

  // Função para buscar empresas do usuário e determinar role
  const fetchUserCompanies = async () => {
    try {
      const response = await companyService.getMyCompanies();
      const myCompaniesData = response.data as GetMyCompaniesSuccessResponse;

      setUserCompanies(myCompaniesData.data.company_external_ids);
      setDashboardUrl(myCompaniesData.data.dashboard_url);

      // Determinar role baseado na URL do dashboard
      const role = myCompaniesData.data.dashboard_url.includes('/admin/') ? 'admin' : 'partner';
      setUserRole(role);

    } catch (error: any) {
      console.error("Erro ao buscar empresas do usuário:", error);

      // Se retornar 403, significa acesso não autorizado - falhar o login
      if (error.response?.status === 403) {
        throw new Error("ACESSO NÃO AUTORIZADO!");
      }

      // Para outros erros, assumir role admin como fallback
      setUserRole('admin');
      setDashboardUrl('/admin/dashboard');
      setUserCompanies([]);
    }
  };

  // Função para forçar logout (apenas quando refresh token expira)
  const forceLogout = () => {
    console.log("🚪 Forçando logout - refresh token expirado");

    // Comprehensive notification cleanup
    try {
      // Clean up FCM notifications
      fcmNotifications.cleanup();

      // Clean up notification systems
      if (browserNotifications.cleanup) {
        browserNotifications.cleanup();
      }
      if (audioNotifications.cleanup) {
        audioNotifications.cleanup();
      }

      console.log("🧹 Performing comprehensive cleanup on force logout");

    } catch (error) {
      console.warn("⚠️ Error during notification cleanup:", error);
    }

    setIsAuthenticated(false);
    setUser(null);
    setUserRole(null);
    setUserCompanies([]);
    setDashboardUrl('/admin/dashboard');

    // Clear notification state completely
    setNotificationState({ items: [], unreadCount: 0 });

    clearAllTokens();
    delete api.defaults.headers.common["Authorization"];
    navigate('/login');
  };

  // Handle AUTH_REQUIRED errors from API interceptor
  useEffect(() => {
    const responseInterceptor = api.interceptors.response.use(
      (response) => response,
      (error) => {
        // Se o erro for AUTH_REQUIRED, força logout
        if (error.message === "AUTH_REQUIRED") {
          console.log("🔒 Erro AUTH_REQUIRED detectado - forçando logout");
          forceLogout();
        }
        return Promise.reject(error);
      }
    );

    // Cleanup do interceptor quando o componente for desmontado
    return () => {
      api.interceptors.response.eject(responseInterceptor);
    };
  }, []);

  useEffect(() => {
    // Load stored notifications on mount
    const storedState = loadStoredNotifications();
    setNotificationState(storedState);

    if (hasValidTokens()) {
      setLoading(true);
      const token = localStorage.getItem('token');
      // Configura o token no header antes de tentar buscar o usuário
      api.defaults.headers.common["Authorization"] = `Bearer ${token}`;

      // Log do tempo restante dos tokens
      const accessTimeRemaining = getTokenTimeRemaining('access');
      const refreshTimeRemaining = getTokenTimeRemaining('refresh');
      console.log(`🔐 Tokens válidos - Access: ${formatTimeRemaining(accessTimeRemaining)}, Refresh: ${formatTimeRemaining(refreshTimeRemaining)}`);

      fetchUser().finally(() => setLoading(false));
    } else {
      console.log("❌ Tokens inválidos ou expirados");
      setLoading(false);
    }
  }, []);

  const login = async (data: LoginSuccessResponse) => {
    const { access_token, refresh_token } = data.data;
    console.log("🔐 Login successful");

    // Reset logout state to allow API calls again
    resetLogoutState();

    // Salva tokens com timestamp
    saveTokenWithTimestamp(access_token, 'access');
    saveTokenWithTimestamp(refresh_token, 'refresh');

    api.defaults.headers.common["Authorization"] = `Bearer ${access_token}`;

    try {
      await fetchUser();

      // FCM notifications will auto-initialize for partner users
      if (dashboardUrl === '/partner/dashboard') {
        console.log("🔔 FCM notifications will auto-initialize for partner user");
      }

      // Redirecionar para o dashboard apropriado baseado no role
      navigate(dashboardUrl || '/admin/dashboard');
    } catch (error: any) {
      // Se houver erro de acesso não autorizado, limpar tokens e propagar erro
      if (error.message === "ACESSO NÃO AUTORIZADO!") {
        setIsAuthenticated(false);
        setUser(null);
        setUserRole(null);
        setUserCompanies([]);
        setDashboardUrl('/admin/dashboard');
        clearAllTokens();
        delete api.defaults.headers.common["Authorization"];
        throw error;
      }
      throw error;
    }
  };

  const logout = async () => {
    console.log("🚪 Fazendo logout manual");

    // Reset logout state to allow future logins
    resetLogoutState();

    // Comprehensive cleanup before logout
    console.log("🧹 Starting logout cleanup process");

    // Clean up FCM notifications
    fcmNotifications.cleanup();

    // Clean up notification systems
    if (browserNotifications.cleanup) {
      browserNotifications.cleanup();
    }
    if (audioNotifications.cleanup) {
      audioNotifications.cleanup();
    }

    // Wait a brief moment to ensure all cleanup completes
    await new Promise(resolve => setTimeout(resolve, 150));

    try {
      await authService.logout();
    } catch (error) {
      // Ignora erros no logout - pode ser que o token já esteja inválido
      console.log("Erro no logout (ignorado):", error);
    } finally {
      setIsAuthenticated(false);
      setUser(null);
      setUserRole(null);
      setUserCompanies([]);
      setDashboardUrl('/admin/dashboard');

      // Clear notification state completely
      setNotificationState({ items: [], unreadCount: 0 });

      // Reset permission denial toast flag for next session
      setPermissionDenialToastShown(false);
      localStorage.removeItem('fcm_permission_denial_toast_shown');

      clearAllTokens();
      delete api.defaults.headers.common["Authorization"];
      navigate('/login');

      console.log("✅ Logout cleanup completed");
    }
  };

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      user,
      userRole,
      userCompanies,
      dashboardUrl,
      login,
      logout,
      loading,
      forceLogout,
      notifications: {
        notifications: notificationState.items,
        unreadCount: notificationState.unreadCount,
        lastNotificationTime: notificationState.lastNotificationTime,
        markAsRead,
        markAllAsRead,
        clearAllNotifications,
        browserNotifications,
        audioNotifications,
        fcmNotifications: {
          // FCM is only available for partner users
          isInitialized: userRole === 'partner' ? fcmNotifications.isInitialized : false,
          isRegistered: userRole === 'partner' ? fcmNotifications.isRegistered : false,
          hasPermission: userRole === 'partner' ? fcmNotifications.hasPermission : false,
          error: userRole === 'partner' ? fcmNotifications.error : null,
          deviceId: userRole === 'partner' ? fcmNotifications.deviceId : null,
          initialize: userRole === 'partner' ? fcmNotifications.initialize : () => Promise.resolve(),
          refreshToken: userRole === 'partner' ? fcmNotifications.refreshToken : () => Promise.resolve(),
          cleanup: userRole === 'partner' ? fcmNotifications.cleanup : () => Promise.resolve()
        },
      }
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
