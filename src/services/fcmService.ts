import { getToken, onMessage, deleteToken } from 'firebase/messaging';
import { messaging, VAPID_KEY, isFirebaseConfigured } from '@/config/firebase';
import { api } from './api';

// Types for FCM integration (Partner users only)
export interface FCMTokenRequest {
  fcm_token: string;
  device_id: string;
  platform: 'web' | 'android' | 'ios';
  app_version?: string;
  device_model?: string;
  os_version?: string;
}

export interface FCMNotificationPayload {
  notification?: {
    title?: string;
    body?: string;
    icon?: string;
  };
  data?: {
    type?: string;
    order_id?: string;
    payment_id?: string;
    amount?: string;
    customer_name?: string;
    [key: string]: any;
  };
}

/**
 * FCM Service for Partner Users Only
 * Handles PIX payment notifications and order updates for partner dashboard
 */
class FCMService {
  private deviceId: string | null = null;
  private currentToken: string | null = null;
  private isRegistered: boolean = false;
  private isRegistering: boolean = false; // Prevent multiple simultaneous registrations
  private isInitializing: boolean = false; // Prevent multiple simultaneous initializations
  private isCleaningUp: boolean = false; // Prevent multiple simultaneous cleanups

  constructor() {
    this.deviceId = this.getOrCreateDeviceId();
    this.initializeServiceWorker();
  }

  /**
   * Generate or retrieve unique device ID
   */
  private getOrCreateDeviceId(): string {
    let deviceId = localStorage.getItem('fcm_device_id');
    if (!deviceId) {
      deviceId = `web_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      localStorage.setItem('fcm_device_id', deviceId);
    }
    return deviceId;
  }

  /**
   * Initialize service worker for background notifications
   */
  private async initializeServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        // First, check if the service worker file is accessible
        const swResponse = await fetch('/firebase-messaging-sw.js');
        if (!swResponse.ok) {
          throw new Error(`Service worker file not accessible: ${swResponse.status} ${swResponse.statusText}`);
        }

        const contentType = swResponse.headers.get('content-type');
        if (!contentType || !contentType.includes('javascript')) {
          throw new Error(`Service worker has incorrect MIME type: ${contentType}`);
        }

        // If file is accessible and has correct MIME type, register it
        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
          scope: '/'
        });
        console.log('FCM Service Worker registered successfully:', registration);

        // Wait for the service worker to be ready
        await navigator.serviceWorker.ready;
        console.log('FCM Service Worker is ready');

      } catch (error) {
        console.error('FCM Service Worker registration failed:', error);

        // Provide helpful debugging information
        if (error instanceof Error) {
          if (error.message.includes('MIME type')) {
            console.error('🔧 Service worker MIME type issue detected.');
            console.error('🔧 This usually means the server is returning HTML instead of JavaScript.');
            console.error('🔧 Check that firebase-messaging-sw.js is properly deployed and accessible.');
          } else if (error.message.includes('not accessible')) {
            console.error('🔧 Service worker file not found or not accessible.');
            console.error('🔧 Ensure firebase-messaging-sw.js is in the root of your deployment.');
          }
        }

        throw error;
      }
    } else {
      throw new Error('Service workers not supported in this browser');
    }
  }

  /**
   * Request notification permission from user
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return 'denied';
    }

    const permission = await Notification.requestPermission();
    console.log('Notification permission:', permission);
    return permission;
  }

  /**
   * Generate FCM token
   */
  async generateToken(): Promise<string | null> {
    // Check if Firebase is properly configured first
    if (!isFirebaseConfigured()) {
      console.warn('Firebase not configured. Push notifications will be disabled.');
      console.warn('To enable push notifications, set these environment variables in Cloudflare Pages:');
      console.warn('- VITE_FIREBASE_API_KEY');
      console.warn('- VITE_FIREBASE_AUTH_DOMAIN');
      console.warn('- VITE_FIREBASE_PROJECT_ID');
      console.warn('- VITE_FIREBASE_STORAGE_BUCKET');
      console.warn('- VITE_FIREBASE_MESSAGING_SENDER_ID');
      console.warn('- VITE_FIREBASE_APP_ID');
      console.warn('- VITE_FIREBASE_VAPID_KEY');
      return null;
    }

    if (!messaging) {
      console.warn('Firebase messaging not available - this may be due to browser compatibility or service worker issues');
      return null;
    }

    try {
      const permission = await this.requestPermission();
      if (permission !== 'granted') {
        console.warn('Notification permission not granted');
        return null;
      }

      const token = await getToken(messaging, { vapidKey: VAPID_KEY });
      console.log('FCM token generated:', token);
      
      this.currentToken = token;
      localStorage.setItem('fcm_token', token);
      localStorage.setItem('fcm_token_timestamp', Date.now().toString());
      
      return token;
    } catch (error) {
      console.error('Error generating FCM token:', error);
      return null;
    }
  }

  /**
   * Register FCM token with backend
   */
  async registerToken(): Promise<boolean> {
    // Prevent multiple simultaneous registrations
    if (this.isRegistering) {
      console.log('FCM registration already in progress, skipping duplicate call');
      return false;
    }

    this.isRegistering = true;

    try {
      const token = this.currentToken || await this.generateToken();
      if (!token || !this.deviceId) {
        console.error('No FCM token or device ID available');
        return false;
      }

      const payload: FCMTokenRequest = {
        fcm_token: token,
        device_id: this.deviceId,
        platform: 'web',
        app_version: '1.0.0',
        device_model: navigator.userAgent,
        os_version: (navigator as any).userAgentData?.platform || navigator.platform || 'unknown'
      };

      const response = await api.post('/v1/fcm/register-token', payload);
      
      if (response.status === 200 || response.status === 201) {
        console.log('FCM token registered successfully');
        this.isRegistered = true;
        localStorage.setItem('fcm_registered', 'true');
        localStorage.setItem('fcm_registration_timestamp', Date.now().toString());
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error registering FCM token:', error);
      return false;
    } finally {
      this.isRegistering = false;
    }
  }

  /**
   * Refresh FCM token
   */
  async refreshToken(): Promise<boolean> {
    if (!this.deviceId) {
      console.error('No device ID available for token refresh');
      return false;
    }

    try {
      const response = await api.post(`/v1/fcm/refresh-token/${this.deviceId}`);
      
      if (response.status === 200) {
        console.log('FCM token refreshed successfully');
        // Generate new token and register it
        await this.generateToken();
        return await this.registerToken();
      }
      
      return false;
    } catch (error) {
      console.error('Error refreshing FCM token:', error);
      return false;
    }
  }

  /**
   * Delete FCM token from backend
   */
  async deleteToken(): Promise<boolean> {
    // Prevent multiple simultaneous cleanup attempts
    if (this.isCleaningUp) {
      console.log('FCM token cleanup already in progress, skipping');
      return true;
    }

    if (!this.deviceId) {
      console.error('No device ID available for token deletion');
      return false;
    }

    this.isCleaningUp = true;

    try {
      // Delete from backend (but don't fail if it errors - user might be logged out)
      try {
        await api.delete(`/v1/fcm/delete-token/${this.deviceId}`);
      } catch (backendError) {
        console.warn('Failed to delete FCM token from backend (user might be logged out):', backendError);
      }

      // Delete local token regardless of backend response
      if (messaging && this.currentToken) {
        try {
          await deleteToken(messaging);
        } catch (firebaseError) {
          console.warn('Failed to delete FCM token from Firebase:', firebaseError);
        }
      }

      // Clear local storage
      localStorage.removeItem('fcm_token');
      localStorage.removeItem('fcm_device_id');
      localStorage.removeItem('fcm_registered');
      localStorage.removeItem('fcm_token_timestamp');
      localStorage.removeItem('fcm_registration_timestamp');

      this.currentToken = null;
      this.isRegistered = false;

      console.log('FCM token deleted successfully');
      return true;
    } catch (error) {
      console.error('Error deleting FCM token:', error);
      return false;
    } finally {
      this.isCleaningUp = false;
    }
  }

  /**
   * Setup foreground message listener
   */
  setupForegroundListener(callback: (payload: FCMNotificationPayload) => void): void {
    if (!messaging) {
      console.warn('Firebase messaging not available for foreground listener');
      return;
    }

    onMessage(messaging, (payload) => {
      console.log('Foreground message received:', payload);
      callback(payload);
    });
  }

  /**
   * Check if token needs refresh (90 days)
   */
  needsTokenRefresh(): boolean {
    const timestamp = localStorage.getItem('fcm_registration_timestamp');
    if (!timestamp) return true;
    
    const registrationTime = parseInt(timestamp);
    const now = Date.now();
    const ninetyDays = 90 * 24 * 60 * 60 * 1000; // 90 days in milliseconds
    
    return (now - registrationTime) > ninetyDays;
  }

  /**
   * Get current registration status
   */
  getRegistrationStatus(): {
    isRegistered: boolean;
    hasToken: boolean;
    deviceId: string | null;
    needsRefresh: boolean;
  } {
    return {
      isRegistered: this.isRegistered || localStorage.getItem('fcm_registered') === 'true',
      hasToken: !!this.currentToken || !!localStorage.getItem('fcm_token'),
      deviceId: this.deviceId,
      needsRefresh: this.needsTokenRefresh()
    };
  }

  /**
   * Initialize FCM service (call this after user login)
   */
  async initialize(): Promise<boolean> {
    // Prevent multiple simultaneous initializations
    if (this.isInitializing) {
      console.log('FCM initialization already in progress, skipping duplicate call');
      return false;
    }

    this.isInitializing = true;

    try {
      console.log('Initializing FCM service...');
      
      // Check if already registered and token is fresh
      const status = this.getRegistrationStatus();
      if (status.isRegistered && status.hasToken && !status.needsRefresh) {
        console.log('FCM already registered and token is fresh');
        this.currentToken = localStorage.getItem('fcm_token');
        this.isRegistered = true;
        return true;
      }

      // Generate new token and register
      const token = await this.generateToken();
      if (!token) {
        console.error('Failed to generate FCM token');
        return false;
      }

      const registered = await this.registerToken();
      if (!registered) {
        console.error('Failed to register FCM token');
        return false;
      }

      console.log('🔔 FCM service ready');
      return true;
    } catch (error) {
      console.error('Error initializing FCM service:', error);
      return false;
    } finally {
      this.isInitializing = false;
    }
  }
}

// Export singleton instance
export const fcmService = new FCMService();
export default fcmService;
