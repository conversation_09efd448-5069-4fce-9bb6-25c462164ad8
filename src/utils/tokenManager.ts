/**
 * Utilitários para gerenciamento de tokens
 */

// Constantes de tempo
const ACCESS_TOKEN_LIFETIME = 15 * 60 * 1000; // 15 minutos em ms
const REFRESH_TOKEN_LIFETIME = 30 * 24 * 60 * 60 * 1000; // 30 dias em ms
const REFRESH_THRESHOLD = 5 * 60 * 1000; // Renovar quando restam 5 minutos

interface TokenInfo {
  token: string;
  timestamp: number;
}

/**
 * Salva o token com timestamp
 */
export const saveTokenWithTimestamp = (token: string, type: 'access' | 'refresh') => {
  const tokenInfo: TokenInfo = {
    token,
    timestamp: Date.now()
  };
  
  if (type === 'access') {
    localStorage.setItem('token', token);
    localStorage.setItem('tokenTimestamp', tokenInfo.timestamp.toString());
  } else {
    localStorage.setItem('refreshToken', token);
    localStorage.setItem('refreshTokenTimestamp', tokenInfo.timestamp.toString());
  }
};

/**
 * Verifica se o token está próximo do vencimento
 */
export const isTokenNearExpiry = (type: 'access' | 'refresh'): boolean => {
  const timestampKey = type === 'access' ? 'tokenTimestamp' : 'refreshTokenTimestamp';
  const lifetime = type === 'access' ? ACCESS_TOKEN_LIFETIME : REFRESH_TOKEN_LIFETIME;
  
  const timestampStr = localStorage.getItem(timestampKey);
  if (!timestampStr) return true;
  
  const timestamp = parseInt(timestampStr);
  const now = Date.now();
  const elapsed = now - timestamp;
  
  return elapsed >= (lifetime - REFRESH_THRESHOLD);
};

/**
 * Verifica se o token expirou
 */
export const isTokenExpired = (type: 'access' | 'refresh'): boolean => {
  const timestampKey = type === 'access' ? 'tokenTimestamp' : 'refreshTokenTimestamp';
  const lifetime = type === 'access' ? ACCESS_TOKEN_LIFETIME : REFRESH_TOKEN_LIFETIME;
  
  const timestampStr = localStorage.getItem(timestampKey);
  if (!timestampStr) return true;
  
  const timestamp = parseInt(timestampStr);
  const now = Date.now();
  const elapsed = now - timestamp;
  
  return elapsed >= lifetime;
};

/**
 * Obtém informações sobre o tempo restante do token
 */
export const getTokenTimeRemaining = (type: 'access' | 'refresh'): number => {
  const timestampKey = type === 'access' ? 'tokenTimestamp' : 'refreshTokenTimestamp';
  const lifetime = type === 'access' ? ACCESS_TOKEN_LIFETIME : REFRESH_TOKEN_LIFETIME;
  
  const timestampStr = localStorage.getItem(timestampKey);
  if (!timestampStr) return 0;
  
  const timestamp = parseInt(timestampStr);
  const now = Date.now();
  const elapsed = now - timestamp;
  const remaining = lifetime - elapsed;
  
  return Math.max(0, remaining);
};

/**
 * Limpa todos os tokens e timestamps
 */
export const clearAllTokens = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('tokenTimestamp');
  localStorage.removeItem('refreshTokenTimestamp');
};

/**
 * Verifica se temos tokens válidos
 */
export const hasValidTokens = (): boolean => {
  const token = localStorage.getItem('token');
  const refreshToken = localStorage.getItem('refreshToken');
  
  if (!token || !refreshToken) return false;
  
  // Se o refresh token expirou, não temos tokens válidos
  if (isTokenExpired('refresh')) return false;
  
  return true;
};

/**
 * Formata tempo em formato legível
 */
export const formatTimeRemaining = (ms: number): string => {
  const minutes = Math.floor(ms / (60 * 1000));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) return `${days}d ${hours % 24}h`;
  if (hours > 0) return `${hours}h ${minutes % 60}m`;
  return `${minutes}m`;
};
