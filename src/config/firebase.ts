import { initializeApp } from 'firebase/app';
import { getMessaging } from 'firebase/messaging';

// Firebase configuration - safe to expose publicly
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID
};

// VAPID key for web push notifications - safe to expose publicly
const VAPID_KEY = import.meta.env.VITE_FIREBASE_VAPID_KEY;

// Validate Firebase configuration
const isFirebaseConfigured = () => {
  const requiredFields = [
    firebaseConfig.apiKey,
    firebaseConfig.authDomain,
    firebaseConfig.projectId,
    firebaseConfig.messagingSenderId,
    firebaseConfig.appId,
    VAPID_KEY
  ];

  const isValid = requiredFields.every(field => field && field.trim() !== '');

  if (!isValid) {
    console.warn('Firebase configuration incomplete. Missing environment variables:', {
      hasApiKey: !!firebaseConfig.apiKey,
      hasAuthDomain: !!firebaseConfig.authDomain,
      hasProjectId: !!firebaseConfig.projectId,
      hasMessagingSenderId: !!firebaseConfig.messagingSenderId,
      hasAppId: !!firebaseConfig.appId,
      hasVapidKey: !!VAPID_KEY
    });
  }

  return isValid;
};

// Initialize Firebase
let app: any = null;
let messaging: any = null;

try {
  if (isFirebaseConfigured()) {
    app = initializeApp(firebaseConfig);

    // Initialize Firebase Cloud Messaging and get a reference to the service
    // Check if we're in a browser environment and messaging is supported
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      try {
        messaging = getMessaging(app);
        console.log('Firebase messaging initialized successfully');
      } catch (error) {
        console.warn('Firebase messaging not supported in this environment:', error);
      }
    }
  } else {
    console.warn('Firebase not configured. Push notifications will be disabled.');
  }
} catch (error) {
  console.error('Failed to initialize Firebase:', error);
}

export { messaging, VAPID_KEY, isFirebaseConfigured };
export default app;
