import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/contexts/AuthContext";
import { DataProvider } from "@/contexts/DataContext";


// Componentes de rota
import { PrivateRoute } from "@/components/PrivateRoute";
import { AdminPrivateRoute } from "@/components/AdminPrivateRoute";
import { PartnerPrivateRoute } from "@/components/PartnerPrivateRoute";
import { DashboardRedirect } from "@/components/DashboardRedirect";
import Login from "@/pages/Login";
import Dashboard from "@/pages/Dashboard";
import Companies from "@/pages/Companies";
import NewCompany from "@/pages/NewCompany";
import CompanyDetails from "@/pages/CompanyDetails";
import Products from "@/pages/Products";
import Categories from "@/pages/Categories";
import NotFound from "@/pages/NotFound";
import Coupons from "@/pages/Coupons";
import NewCoupon from "@/pages/NewCoupon";
import CouponDetails from "@/pages/CouponDetails";
import PartnerDashboard from "@/pages/PartnerDashboard";
import PartnerCompanies from "@/pages/PartnerCompanies";
import PartnerCompanyDetails from "@/pages/PartnerCompanyDetails";
import PartnerOrders from "@/pages/PartnerOrders";

import Users from "@/pages/Users";

// Configuração do cliente de consulta
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5, // 5 minutos
    },
  },
});

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        <AuthProvider>
          <DataProvider>
              <TooltipProvider>
                <Toaster />
                <Sonner position="top-right" />

                <Routes>
                {/* Rota pública: Login */}
                <Route path="/login" element={<Login />} />

                {/* Rota raiz: Redireciona para dashboard apropriado baseado no role */}
                <Route path="/" element={<DashboardRedirect />} />
                <Route path="/dashboard" element={<DashboardRedirect />} />

                {/* Rotas de Admin: Protegidas por autenticação e role */}
                <Route element={<AdminPrivateRoute />}>
                  <Route path="/admin/dashboard" element={<Dashboard />} />
                  <Route path="/admin/users" element={<Users />} />
                  <Route path="/admin/companies" element={<Companies />} />
                  <Route path="/admin/companies/new" element={<NewCompany />} />
                  <Route path="/admin/companies/:id" element={<CompanyDetails />} />
                  <Route path="/admin/products" element={<Products />} />
                  <Route path="/admin/categories" element={<Categories />} />
                  <Route path="/admin/coupons" element={<Coupons />} />
                  <Route path="/admin/coupons/new" element={<NewCoupon />} />
                  <Route path="/admin/coupons/:id" element={<CouponDetails />} />
                </Route>

                {/* Rotas de Parceiro: Protegidas por autenticação e role */}
                <Route element={<PartnerPrivateRoute />}>
                  <Route path="/partner/dashboard" element={<PartnerDashboard />} />
                  <Route path="/partner/companies" element={<PartnerCompanies />} />
                  <Route path="/partner/companies/:id" element={<PartnerCompanyDetails />} />
                  <Route path="/partner/orders" element={<PartnerOrders />} />
                </Route>

                {/* Rota 404: Para caminhos que não existem */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </TooltipProvider>
        </DataProvider>
      </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

export default App;
