import React from "react";
import { Card } from "@/components/ui/card";
import { usePartnerData } from "@/hooks/usePartnerData";
import { useAuth } from "@/contexts/AuthContext";
import CompanyOrdersTab from "@/components/CompanyOrdersTab";
import NotificationSettings from "@/components/NotificationSettings";
import NotificationPanel from "@/components/NotificationPanel";
import FCMStatusIndicator from "@/components/FCMStatusIndicator";
import BackgroundNotificationIndicator from "@/components/BackgroundNotificationIndicator";

const PartnerOrders = () => {
  const { companyIds, companies } = usePartnerData();
  const { notifications } = useAuth();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Pedidos</h2>
          <p className="text-muted-foreground">
            <PERSON><PERSON><PERSON><PERSON> os pedidos das suas empresas
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* Background Notification Indicator */}
          <BackgroundNotificationIndicator />

          {/* FCM Status - Partner Users Only */}
          <FCMStatusIndicator
            status={notifications.fcmNotifications}
            onInitialize={notifications.fcmNotifications.initialize}
            onRefresh={notifications.fcmNotifications.refreshToken}
            showDetails
            variant="button"
          />

          {/* Notification Panel */}
          <NotificationPanel
            notifications={notifications.notifications}
            unreadCount={notifications.unreadCount}
            onMarkAsRead={notifications.markAsRead}
            onMarkAllAsRead={notifications.markAllAsRead}
            onClearAll={notifications.clearAllNotifications}
          />

          {/* Notification Settings */}
          <NotificationSettings />
        </div>
      </div>

      {/* Orders Management */}
      <Card>        
          <CompanyOrdersTab
            companyData={{
              partnerCompanyIds: companyIds,
              partnerCompanies: companies
            }}
          />

      </Card>
    </div>
  );
};

export default PartnerOrders;
