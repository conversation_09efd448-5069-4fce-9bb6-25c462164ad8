import React, { useState, useMemo } from "react";
import { use<PERSON>ara<PERSON>, Navigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { companyService, productService } from "@/services/api";
import { useAuth } from "@/contexts/AuthContext";
import { GetActiveCompanySuccessResponse, GetActiveProductsResponse } from "@/types/api";
import { ArrowLeft, Building, Package, ShoppingBag, Plus, Search, Trash2, Check, DollarSign } from "lucide-react";
import { Link } from "react-router-dom";
import CompanyOrdersTab from "@/components/CompanyOrdersTab";
import CompanyBillingTab from "@/components/CompanyBillingTab";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { CurrencyInput } from "@/components/ui/currency-input";

interface ProductFormValues {
  price: number; // Value in centavos
  discount: number;
  stock: number;
}

const PartnerCompanyDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { userCompanies } = useAuth();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("products");
  const [productDialogOpen, setProductDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<GetActiveProductsResponse | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Product filter states
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showAddedProducts, setShowAddedProducts] = useState(false);
  const [showAvailableProducts, setShowAvailableProducts] = useState(true);

  // Form for product pricing
  const { register, handleSubmit, reset, setValue, formState: { errors } } = useForm<ProductFormValues>();
  const [priceValue, setPriceValue] = useState<number>(0);

  // Verificar se o usuário tem acesso a esta empresa
  const hasAccess = userCompanies.includes(id || "");

  // Se não tem acesso, redirecionar
  if (!hasAccess) {
    return <Navigate to="/partner/dashboard" replace />;
  }

  // Buscar dados da empresa
  const { data: companyResponse, isLoading, error } = useQuery({
    queryKey: ["company", id],
    queryFn: async () => {
      if (!id) throw new Error("ID da empresa não fornecido");
      const response = await companyService.getCompany(id);
      return response.data as GetActiveCompanySuccessResponse;
    },
    enabled: !!id,
  });

  // Buscar todos os produtos disponíveis para adicionar
  const { data: productsResponse, isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products-for-partner"],
    queryFn: async () => {
      const response = await productService.getProducts(1, 10000);
      return response.data;
    },
    enabled: productDialogOpen, // Only fetch when dialog is open
  });

  const allProducts = productsResponse?.data || [];

  const companyData = companyResponse?.data;

  // Extract unique categories from products
  const availableCategories = useMemo(() => {
    const categoryMap = new Map();
    allProducts.forEach(product => {
      product.categories?.forEach(category => {
        if (!categoryMap.has(category.external_id)) {
          categoryMap.set(category.external_id, category);
        }
      });
    });
    return Array.from(categoryMap.values());
  }, [allProducts]);

  // Verifica se a empresa já tem o produto (moved before filteredProducts)
  const isProductAlreadyAdded = (productId: string) => {
    return companyData?.products?.some((product) => product.external_id === productId) || false;
  };

  // Enhanced product filtering with category and status filters
  const filteredProducts = useMemo(() => {
    return allProducts.filter((product) => {
      // Search term filter
      const matchesSearch = !searchTerm || (
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.ean.includes(searchTerm)
      );

      // Category filter
      const matchesCategory = selectedCategories.length === 0 ||
        product.categories?.some(category =>
          selectedCategories.includes(category.external_id)
        );

      // Product status filter (added vs available)
      const isAdded = isProductAlreadyAdded(product.external_id);
      const matchesStatus = (showAddedProducts && isAdded) ||
                           (showAvailableProducts && !isAdded);

      // Base filters (reviewed and active)
      const isValidProduct = product.is_reviewed && product.is_active;

      return matchesSearch && matchesCategory && matchesStatus && isValidProduct;
    });
  }, [allProducts, searchTerm, selectedCategories, showAddedProducts, showAvailableProducts, companyData?.products]);

  // Mutation para adicionar produtos à empresa
  const addProductsMutation = useMutation({
    mutationFn: ({ productId, price, discount, stock }: {
      productId: string;
      price: number; // Already in centavos from CurrencyInput
      discount: number;
      stock: number
    }) => {
      if (!id) throw new Error("ID da empresa não fornecido");
      return companyService.addProductToCompany(id, {
        product_external_id: productId,
        price: price,
        discount,
        stock
      });
    },
    onSuccess: () => {
      toast("Produto adicionado com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["company", id] });
      setSelectedProduct(null);
      setProductDialogOpen(false);
      reset();
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao adicionar produto";
      toast(errorMsg);
    },
  });

  // Mutation para remover produtos da empresa
  const removeProductMutation = useMutation({
    mutationFn: (productId: string) => {
      if (!id) throw new Error("ID da empresa não fornecido");
      return companyService.removeProductsFromCompany(id, {
        product_external_id: productId,
        price: 0,
        discount: 0,
        stock: 0
      });
    },
    onSuccess: () => {
      toast("Produto removido com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["company", id] });
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao remover produto";
      toast(errorMsg);
    },
  });

  // Handler para selecionar produto
  const handleProductSelection = (product: GetActiveProductsResponse) => {
    if (isProductAlreadyAdded(product.external_id)) {
      toast("Este produto já foi adicionado");
      return;
    }
    setSelectedProduct(product);
  };

  // Handler para adicionar produto com preço, desconto e estoque
  const handleAddProduct = (values: ProductFormValues) => {
    if (!selectedProduct) return;

    addProductsMutation.mutate({
      productId: selectedProduct.external_id,
      price: values.price || 0,
      discount: values.discount || 0,
      stock: values.stock || 0
    });
  };

  // Handler para remover produto
  const handleRemoveProduct = (productId: string) => {
    if (confirm("Tem certeza que deseja remover este produto?")) {
      removeProductMutation.mutate(productId);
    }
  };

  // Função para fechar o modal e limpar seleção
  const handleCloseModal = () => {
    setProductDialogOpen(false);
    setSelectedProduct(null);
    setSearchTerm("");
    setSelectedCategories([]);
    setShowAddedProducts(false);
    setShowAvailableProducts(true);
    setPriceValue(0);
    reset();
  };

  // Função para formatar preço
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(price / 100);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Carregando dados da empresa...</p>
        </div>
      </div>
    );
  }

  if (error || !companyData) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500 mb-4">Erro ao carregar dados da empresa</p>
        <Link to="/partner/dashboard">
          <Button variant="outline">Voltar ao Dashboard</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/partner/dashboard">
            <Button variant="outline" size="icon">
              <ArrowLeft size={16} />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">{companyData.name}</h1>
            <p className="text-muted-foreground">Gerenciar empresa</p>
          </div>
        </div>
      </div>

      {/* Company Info Card - Read Only */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="mr-2" size={20} />
            Informações da Empresa
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div>
              <p className="text-sm font-medium text-gray-500">Nome</p>
              <p className="text-lg">{companyData.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">CNPJ</p>
              <p className="text-lg">{companyData.cnpj || companyData.document}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Email</p>
              <p className="text-lg">{companyData.owner?.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Telefone</p>
              <p className="text-lg">{companyData.phone_numbers?.[0] || "Não informado"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <p className="text-lg capitalize">{companyData.is_active ? "Ativo" : "Inativo"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Produtos</p>
              <p className="text-lg">{companyData.products?.length || 0}</p>
            </div>
          </div>
          {companyData.bio && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Descrição</p>
              <p className="text-base mt-1">{companyData.bio}</p>
            </div>
          )}
          {companyData.address && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Endereço</p>
              <p className="text-base mt-1">
                {companyData.address.street}, {companyData.address.number}
                {companyData.address.complement && `, ${companyData.address.complement}`}
                <br />
                {companyData.address.neighborhood}, {companyData.address.city} - {companyData.address.state}
                <br />
                CEP: {companyData.address.zip_code}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tabs for Management */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="products" className="flex items-center">
            <Package className="mr-2" size={16} />
            Produtos
          </TabsTrigger>
          <TabsTrigger value="orders" className="flex items-center">
            <ShoppingBag className="mr-2" size={16} />
            Pedidos
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex items-center">
            <DollarSign className="mr-2" size={16} />
            Faturamento
          </TabsTrigger>
        </TabsList>

        {/* Products Tab */}
        <TabsContent value="products" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center">
                <Package className="mr-2" size={20} />
                Produtos
              </CardTitle>
              <Button onClick={() => setProductDialogOpen(true)}>
                <Plus className="mr-2" size={18} />
                Adicionar Produto
              </Button>
            </CardHeader>
            <CardContent>
              {companyData.products && companyData.products.length > 0 ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Imagem</TableHead>
                        <TableHead>Nome</TableHead>
                        <TableHead>Marca</TableHead>
                        <TableHead>EAN</TableHead>
                        <TableHead>Preço</TableHead>
                        <TableHead>Desconto</TableHead>
                        <TableHead>Estoque</TableHead>
                        <TableHead>Categorias</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {companyData.products.map((product) => (
                        <TableRow key={product.external_id}>
                          <TableCell>
                            {product.image ? (
                              <img
                                src={product.image}
                                alt={product.name}
                                className="w-12 h-12 object-cover rounded-lg"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                <Package size={16} className="text-gray-500" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.brand}</TableCell>
                          <TableCell>{product.ean}</TableCell>
                          <TableCell>{formatPrice(product.price || 0)}</TableCell>
                          <TableCell>{product.discount || 0}%</TableCell>
                          <TableCell>{product.stock || 0}</TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {product.categories?.map((category) => (
                                <span
                                  key={category.external_id}
                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                                >
                                  {category.name}
                                </span>
                              )) || "Sem categoria"}
                            </div>
                          </TableCell>
                          <TableCell>{product.external_id}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveProduct(product.external_id)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 size={16} />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="mb-4">Este parceiro ainda não tem produtos associados.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders" className="mt-6">
          <CompanyOrdersTab companyData={companyData} />
        </TabsContent>

        {/* Billing Tab */}
        <TabsContent value="billing" className="mt-6">
          <CompanyBillingTab companyId={companyData?.external_id} />
        </TabsContent>

      </Tabs>

      {/* Modal para adicionar produtos */}
      <Dialog open={productDialogOpen} onOpenChange={handleCloseModal}>
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>Adicionar Produto</DialogTitle>
            <DialogDescription>
              {selectedProduct
                ? "Informe o preço, desconto e estoque para o produto selecionado"
                : "Clique em um produto para selecioná-lo"}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {!selectedProduct ? (
              <>
                <div className="flex items-center space-x-2 mb-4">
                  <Search size={20} className="text-gray-400" />
                  <Input
                    placeholder="Buscar por nome, marca ou código"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="flex-1"
                  />
                </div>

                {/* Filtering Controls */}
                <div className="space-y-4 mb-4">
                  {/* Category Filter */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Filtrar por Categoria</Label>
                    <Select
                      value={selectedCategories.length === 0 ? "all" : selectedCategories.join(",")}
                      onValueChange={(value) => {
                        if (value === "all") {
                          setSelectedCategories([]);
                        } else {
                          // For now, handle single selection. Can be enhanced for multi-select later
                          setSelectedCategories([value]);
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Todas as Categorias" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as Categorias</SelectItem>
                        {availableCategories.map((category) => (
                          <SelectItem key={category.external_id} value={category.external_id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Product Status Filters */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Exibir Produtos</Label>
                    <div className="flex items-center space-x-6">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="show-available"
                          checked={showAvailableProducts}
                          onCheckedChange={setShowAvailableProducts}
                        />
                        <Label htmlFor="show-available" className="text-sm">
                          Disponíveis
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="show-added"
                          checked={showAddedProducts}
                          onCheckedChange={setShowAddedProducts}
                        />
                        <Label htmlFor="show-added" className="text-sm">
                          Já Adicionados
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-4" />

                <ScrollArea className="h-[400px] rounded-md">
                  {isLoadingProducts ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                        <p>Carregando produtos...</p>
                      </div>
                    </div>
                  ) : filteredProducts.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                      {filteredProducts.map((product) => {
                        const isAdded = isProductAlreadyAdded(product.external_id);
                        const isSelected = selectedProduct?.external_id === product.external_id;

                        return (
                          <div
                            key={product.external_id}
                            className={`relative rounded-lg border p-4 transition-all ${
                              isAdded
                                ? "bg-green-50 border-green-200"
                                : isSelected
                                  ? "bg-blue-50 border-blue-200 ring-2 ring-blue-400"
                                  : "hover:bg-gray-50 hover:border-gray-300 cursor-pointer"
                            }`}
                            onClick={() => {
                              if (!isAdded) {
                                handleProductSelection(product);
                              }
                            }}
                          >
                            {isAdded && (
                              <div className="absolute top-2 right-2 text-sm font-medium text-green-600 flex items-center bg-green-100 px-2 py-1 rounded-full">
                                <Check size={14} className="mr-1" />
                                Adicionado
                              </div>
                            )}

                            <div className="flex items-start space-x-3">
                              {product.image ? (
                                <img
                                  src={product.image}
                                  alt={product.name}
                                  className="w-16 h-16 object-cover rounded-lg"
                                />
                              ) : (
                                <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                  <Package size={20} className="text-gray-500" />
                                </div>
                              )}

                              <div className="flex-1 min-w-0">
                                <h3 className="font-medium text-sm truncate">{product.name}</h3>
                                <p className="text-xs text-gray-500 mt-1">Marca: {product.brand}</p>
                                <p className="text-xs text-gray-500">EAN: {product.ean}</p>

                                {product.categories && product.categories.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mt-2">
                                    {product.categories.slice(0, 2).map((category) => (
                                      <Badge
                                        key={category.external_id}
                                        variant="secondary"
                                        className="text-xs"
                                      >
                                        {category.name}
                                      </Badge>
                                    ))}
                                    {product.categories.length > 2 && (
                                      <span className="text-xs text-gray-500">
                                        +{product.categories.length - 2} mais
                                      </span>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>


                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Package size={48} className="mx-auto text-gray-400 mb-4" />
                      <p className="text-gray-500 mb-2">
                        {searchTerm
                          ? "Nenhum produto encontrado para esta busca"
                          : !showAvailableProducts && !showAddedProducts
                            ? "Selecione pelo menos um filtro de status"
                            : !showAvailableProducts
                              ? "Nenhum produto já adicionado encontrado"
                              : !showAddedProducts
                                ? "Nenhum produto disponível encontrado"
                                : "Nenhum produto encontrado"}
                      </p>
                      <p className="text-sm text-gray-400">
                        Tente ajustar os filtros ou termo de busca
                      </p>
                    </div>
                  )}
                </ScrollArea>
              </>
            ) : (
              <div className="space-y-6">
                {/* Produto selecionado */}
                <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  {selectedProduct.image ? (
                    <img
                      src={selectedProduct.image}
                      alt={selectedProduct.name}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                      <Package size={20} className="text-gray-500" />
                    </div>
                  )}

                  <div className="flex-1">
                    <h3 className="font-medium">{selectedProduct.name}</h3>
                    <p className="text-sm text-gray-600">Marca: {selectedProduct.brand}</p>
                    <p className="text-sm text-gray-600">EAN: {selectedProduct.ean}</p>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedProduct(null)}
                  >
                    Trocar produto
                  </Button>
                </div>

                {/* Formulário de preço, desconto e estoque */}
                <form onSubmit={handleSubmit(handleAddProduct)} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="price">Preço (R$)</Label>
                      <CurrencyInput
                        id="price"
                        placeholder="R$ 0,00"
                        value={priceValue}
                        onValueChange={(value) => {
                          setPriceValue(value);
                          setValue("price", value, { shouldValidate: true });
                        }}
                        className={errors.price ? "border-red-500" : ""}
                      />
                      {errors.price && (
                        <p className="text-sm text-red-500 mt-1">{errors.price.message}</p>
                      )}
                      {/* Hidden input for form validation */}
                      <input
                        type="hidden"
                        {...register("price", {
                          required: "Preço é obrigatório",
                          min: { value: 1, message: "Preço deve ser maior que zero" }
                        })}
                      />
                    </div>

                    <div>
                      <Label htmlFor="discount">Desconto (%)</Label>
                      <Input
                        id="discount"
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        placeholder="0"
                        {...register("discount", {
                          valueAsNumber: true,
                          min: { value: 0, message: "Desconto não pode ser negativo" },
                          max: { value: 100, message: "Desconto não pode ser maior que 100%" }
                        })}
                      />
                      {errors.discount && (
                        <p className="text-sm text-red-500 mt-1">{errors.discount.message}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="stock">Estoque</Label>
                      <Input
                        id="stock"
                        type="number"
                        min="0"
                        placeholder="0"
                        {...register("stock", {
                          valueAsNumber: true,
                          required: "Estoque é obrigatório",
                          min: { value: 0, message: "Estoque não pode ser negativo" }
                        })}
                      />
                      {errors.stock && (
                        <p className="text-sm text-red-500 mt-1">{errors.stock.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCloseModal}
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      disabled={addProductsMutation.isPending}
                    >
                      {addProductsMutation.isPending ? "Adicionando..." : "Adicionar Produto"}
                    </Button>
                  </div>
                </form>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PartnerCompanyDetails;
