import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useAuth } from "@/contexts/AuthContext";
import { authService } from "@/services/api";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";

const formSchema = z.object({
  email: z.string().email("Email inválido"),
  login_code: z.string().min(1, "Código é obrigatório"),
});

type FormValues = z.infer<typeof formSchema>;

const Login = () => {
  const { login } = useAuth();
  const { toast } = useToast();
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      login_code: "",
    },
  });

  const onSubmit = async (values: FormValues) => {
    try {
      setIsLoading(true);
      const response = await authService.login({ email: values.email, login_code: values.login_code });
      await login(response.data);
    } catch (error: any) {
      let errorMessage = "Tente novamente mais tarde";
      let errorTitle = "Erro ao fazer login";

      // Verificar se é erro de acesso não autorizado
      if (error.message === "ACESSO NÃO AUTORIZADO!") {
        errorTitle = "Acesso Negado";
        errorMessage = "ACESSO NÃO AUTORIZADO! Você não tem permissão para acessar este sistema.";
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      toast({
        variant: "destructive",
        title: errorTitle,
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendCode = async () => {
    const email = form.getValues("email");
    if (!email) {
      form.setError("email", { message: "Email é obrigatório" });
      return;
    }

    try {
      setIsSendingCode(true);
      await authService.sendLoginCode(email);
      toast({
        title: "Código enviado",
        description: "Verifique seu email para o código de acesso",
      });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao enviar código",
        description: error.response?.data?.message || "Tente novamente mais tarde",
      });
    } finally {
      setIsSendingCode(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Login</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="login_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Código de Acesso</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Digite o código recebido"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex flex-col space-y-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSendCode}
                  disabled={isSendingCode}
                >
                  {isSendingCode ? "Enviando..." : "Enviar Código"}
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Entrando..." : "Entrar"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default Login;
