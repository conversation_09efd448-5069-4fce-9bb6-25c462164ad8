
import React from "react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { companyService } from "@/services/api";
import { GetActiveCompaniesSuccessResponse } from "@/types/api";
import { Badge } from "@/components/ui/badge";
import { Building, Eye, Plus } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { format, parse, parseISO } from "date-fns";

const Companies = () => {
  // Estado para paginação (implementação básica)
  const [currentPage, setCurrentPage] = React.useState(1);
  const itemsPerPage = 10;

  // Busca a lista de empresas
  const { data, isLoading, isError } = useQuery({
    queryKey: ["companies"],
    queryFn: async () => {
      try {
        const response = await companyService.getCompanies();
        return response.data as GetActiveCompaniesSuccessResponse;
      } catch (error) {
        console.error("Erro ao buscar empresas:", error);
        throw error;
      }
    },
  });

  // Filtra as empresas pela página atual
  const paginatedCompanies = data?.data
    ? data.data.slice(
      (currentPage - 1) * itemsPerPage,
      currentPage * itemsPerPage
    )
    : [];

  // Calcula o total de páginas
  const totalPages = data?.data
    ? Math.ceil(data.data.length / itemsPerPage)
    : 0;

  // Função para formatar a data corrigida
  const formatDate = (dateString: string) => {
    try {
      // Usar parseISO para garantir um parsing correto de string ISO
      const cleaned = dateString.replace(/ UTC$/, '').replace(/\.(\d{3})\d+/, '.$1');
      const parsed = parse(cleaned, 'yyyy-MM-dd HH:mm:ss.SSS X', new Date());
      return format(parsed, 'dd/MM/yyyy HH:mm:ss');
    } catch (error) {
      console.error("Erro ao formatar data:", error);
      return "Data inválida";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Empresas Parceiras</h2>
        <Link to="/admin/companies/new">
          <Button>
            <Plus className="mr-2" size={18} />
            Nova Empresa
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center">
              <Building className="mr-2" size={20} />
              Lista de Parceiros
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Carregando empresas...</div>
          ) : isError ? (
            <div className="text-center py-8 text-red-500">
              Erro ao carregar empresas. Tente novamente.
            </div>
          ) : data?.data && data.data.length > 0 ? (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>CNPJ</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Telefone</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Data de Criação</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedCompanies.map((company) => (
                      <TableRow key={company.external_id}>
                        <TableCell className="font-medium">
                          {company.name}
                        </TableCell>
                        <TableCell>{company.cnpj || company.document}</TableCell>
                        <TableCell>{company.email}</TableCell>
                        <TableCell>{company.phone_numbers?.[0]}</TableCell>
                        <TableCell>
                          <Badge variant={company.is_active ? "default" : "secondary"}>
                            {company.is_active ? "ATIVO" : "INATIVO"}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(company.created_at)}</TableCell>
                        <TableCell className="text-right">
                          <Link to={`/admin/companies/${company.external_id}`}>
                            <Button size="sm" variant="ghost">
                              <Eye size={16} className="mr-1" />
                              Ver
                            </Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Paginação simples */}
              {totalPages >= 1 && (
                <div className="flex justify-end space-x-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Anterior
                  </Button>
                  <span className="flex items-center px-2">
                    {currentPage} de {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setCurrentPage(Math.min(totalPages, currentPage + 1))
                    }
                    disabled={currentPage === totalPages}
                  >
                    Próxima
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8">
              <p className="mb-4">Nenhuma empresa cadastrada ainda.</p>
              <Link to="/admin/companies/new">
                <Button>Cadastrar Empresa</Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Companies;
