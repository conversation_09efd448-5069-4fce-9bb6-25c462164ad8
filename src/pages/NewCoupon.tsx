import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { couponService, companyService } from "@/services/api";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tag, Search, Building2, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { SearchCompanyResponse } from "@/types/api";

const formSchema = z.object({
  code: z.string()
    .min(1, "Código é obrigatório")
    .max(20, "Código deve ter no máximo 20 caracteres")
    .regex(/^[A-Z0-9]+$/, "Código deve conter apenas letras maiúsculas e números"),
  type: z.enum(["percentage", "fixed"], {
    required_error: "Tipo é obrigatório",
  }),
  value: z.string()
    .min(1, "Valor é obrigatório")
    .refine((val): val is string => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Valor deve ser maior que zero"),
  min_order_value: z.string()
    .min(1, "Valor mínimo é obrigatório")
    .refine((val): val is string => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 50;
    }, "Valor mínimo deve ser pelo menos R$ 50,00"),
  quantity: z.string()
    .min(1, "Quantidade é obrigatória")
    .refine((val): val is string => {
      const num = parseInt(val);
      return !isNaN(num) && num > 0 && num <= 1000;
    }, "Quantidade deve estar entre 1 e 1000"),
  expires_at: z.string()
    .min(1, "Data de expiração é obrigatória")
    .refine((val): val is string => {
      const date = new Date(val);
      return date > new Date();
    }, "Data de expiração deve ser futura"),
  owner_type: z.enum(["company", "admin"], {
    required_error: "Tipo de proprietário é obrigatório",
  }),
  company_external_id: z.string().optional(),
}).refine((data) => {
  // If owner_type is "company", company_external_id is required
  if (data.owner_type === "company") {
    return data.company_external_id && data.company_external_id.length > 0;
  }
  return true;
}, {
  message: "Empresa é obrigatória quando o tipo de proprietário é 'company'",
  path: ["company_external_id"],
});

type FormValues = z.infer<typeof formSchema>;

export function NewCoupon() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Company search state
  const [companySearchQuery, setCompanySearchQuery] = useState("");
  const [companySearchResults, setCompanySearchResults] = useState<SearchCompanyResponse[]>([]);
  const [isSearchingCompanies, setIsSearchingCompanies] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<SearchCompanyResponse | null>(null);
  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);
  const [searchDebounceTimer, setSearchDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: "",
      type: "percentage",
      value: "",
      min_order_value: "",
      quantity: "",
      expires_at: "",
      owner_type: "admin",
    },
  });

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer);
      }
    };
  }, [searchDebounceTimer]);

  // Clear company selection when owner_type changes to non-company
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "owner_type" && value.owner_type !== "company") {
        clearCompanySelection();
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.company-search-container')) {
        setShowCompanyDropdown(false);
      }
    };

    if (showCompanyDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showCompanyDropdown]);

  // Company search functions
  const searchCompanies = async (query: string) => {
    if (!query.trim()) {
      setCompanySearchResults([]);
      setIsSearchingCompanies(false);
      return;
    }

    setIsSearchingCompanies(true);
    try {
      const response = await companyService.searchCompanies(query);
      setCompanySearchResults(response.data.data || []);
    } catch (error) {
      console.error("Error searching companies:", error);
      setCompanySearchResults([]);
      toast.error("Erro ao buscar empresas. Tente novamente.");
    } finally {
      setIsSearchingCompanies(false);
    }
  };

  // Handle company search input changes with debounce
  const handleCompanySearchChange = (value: string) => {
    setCompanySearchQuery(value);
    setShowCompanyDropdown(true);

    // Clear previous timer
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
    }

    // Clear results if search is empty
    if (!value.trim()) {
      setCompanySearchResults([]);
      setIsSearchingCompanies(false);
      setShowCompanyDropdown(false);
      return;
    }

    // Set new timer for debounced search
    const timeoutId = setTimeout(() => {
      searchCompanies(value);
    }, 500); // 500ms debounce

    setSearchDebounceTimer(timeoutId);
  };

  // Handle company selection
  const handleCompanySelect = (company: SearchCompanyResponse) => {
    setSelectedCompany(company);
    setCompanySearchQuery(`${company.name} - ${company.cnpj}`);
    setShowCompanyDropdown(false);
    form.setValue("company_external_id", company.external_id);
  };

  // Clear company selection
  const clearCompanySelection = () => {
    setSelectedCompany(null);
    setCompanySearchQuery("");
    setCompanySearchResults([]);
    setShowCompanyDropdown(false);
    form.setValue("company_external_id", "");
  };

  const createCouponMutation = useMutation({
    mutationFn: async (values: FormValues) => {
      const formattedDate = new Date(values.expires_at).toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(',', '');

      // Convert values based on type - no multiplication needed
      const value = parseFloat(values.value);

      const couponData: any = {
        code: values.code,
        type: values.type,
        value: value,
        min_order_value: parseFloat(values.min_order_value),
        quantity: parseInt(values.quantity),
        expires_at: formattedDate,
        owner_type: values.owner_type
      };

      // Add company_external_id if owner_type is "company"
      if (values.owner_type === "company" && values.company_external_id) {
        couponData.company_external_id = values.company_external_id;
      }

      return couponService.createCoupon(couponData);
    },
    onSuccess: async () => {
      try {
        // First invalidate the queries
        await queryClient.invalidateQueries({ queryKey: ["coupons"] });
        // Then show success message
        toast.success("Cupom criado com sucesso!", {
          dismissible: true,
        });
        // Finally navigate
        navigate("/admin/coupons", { replace: true });
      } catch (error) {
        console.error("Error during success handling:", error);
        toast.error("Erro ao finalizar criação do cupom", {
          dismissible: true,
        });
      }
    },
    onError: () => {
      toast.error("Erro ao criar cupom. Por favor, tente novamente.", {
        dismissible: true,
      });
    },
    onSettled: () => {
      setIsSubmitting(false);
    }
  });

  const onSubmit = (values: FormValues) => {
    setIsSubmitting(true);
    createCouponMutation.mutate(values);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Novo Cupom</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center">
              <Tag className="mr-2" size={20} />
              Criar Cupom de Desconto
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Código</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: SUMMER2024" {...field} />
                    </FormControl>
                    <FormDescription>
                      Use apenas letras maiúsculas e números. Ex: SUMMER2024, BLACKFRIDAY
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Desconto</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="percentage">Porcentagem</SelectItem>
                        <SelectItem value="fixed">Valor Fixo</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Porcentagem: desconto baseado em % do valor total
                      <br />
                      Valor Fixo: desconto de valor específico
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {form.watch("type") === "percentage"
                        ? "Porcentagem de Desconto"
                        : "Valor do Desconto"}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder={
                          form.watch("type") === "percentage"
                            ? "Ex: 15"
                            : "Ex: 50.00"
                        }
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {form.watch("type") === "percentage"
                        ? "Valor entre 1% e 100%"
                        : "Valor em reais (R$)"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="min_order_value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Valor Mínimo do Pedido</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Ex: 100.00"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Valor mínimo em reais (R$) para aplicar o cupom. Mínimo: R$ 50,00
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantidade Disponível</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Ex: 100"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Número de vezes que o cupom pode ser utilizado. Máximo: 1000
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expires_at"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Expiração</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormDescription>
                      Data e hora em que o cupom expirará. Deve ser uma data futura.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="owner_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Proprietário</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="admin">Administrador</SelectItem>
                        <SelectItem value="company">Empresa</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Admin: cupom disponível para todas as empresas
                      <br />
                      Empresa: cupom disponível apenas para a empresa específica
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Company Search Field - Only show when owner_type is "company" */}
              {form.watch("owner_type") === "company" && (
                <FormField
                  control={form.control}
                  name="company_external_id"
                  render={() => (
                    <FormItem>
                      <FormLabel>Empresa *</FormLabel>
                      <FormControl>
                        <div className="relative company-search-container">
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <Input
                              placeholder="Buscar empresa por nome ou CNPJ..."
                              value={companySearchQuery}
                              onChange={(e) => handleCompanySearchChange(e.target.value)}
                              onFocus={() => setShowCompanyDropdown(true)}
                              className="pl-10"
                            />
                            {isSearchingCompanies && (
                              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 animate-spin" />
                            )}
                          </div>

                          {/* Search Results Dropdown */}
                          {showCompanyDropdown && (companySearchResults.length > 0 || isSearchingCompanies) && (
                            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
                              {isSearchingCompanies && (
                                <div className="p-3 text-center text-gray-500">
                                  <Loader2 className="h-4 w-4 animate-spin mx-auto mb-2" />
                                  Buscando empresas...
                                </div>
                              )}

                              {!isSearchingCompanies && companySearchResults.length === 0 && companySearchQuery.trim() && (
                                <div className="p-3 text-center text-gray-500">
                                  Nenhuma empresa encontrada
                                </div>
                              )}

                              {companySearchResults.map((company) => (
                                <div
                                  key={company.external_id}
                                  className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                                  onClick={() => handleCompanySelect(company)}
                                >
                                  <div className="flex items-center space-x-3">
                                    <Building2 className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                    <div className="flex-1 min-w-0">
                                      <p className="text-sm font-medium text-gray-900 truncate">
                                        {company.name}
                                      </p>
                                      <p className="text-sm text-gray-500">
                                        CNPJ: {company.cnpj}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}

                          {/* Selected Company Display */}
                          {selectedCompany && (
                            <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <Building2 className="h-4 w-4 text-green-600" />
                                  <div>
                                    <p className="text-sm font-medium text-green-900">
                                      {selectedCompany.name}
                                    </p>
                                    <p className="text-xs text-green-700">
                                      CNPJ: {selectedCompany.cnpj}
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={clearCompanySelection}
                                  className="text-green-600 hover:text-green-800"
                                >
                                  ✕
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormDescription>
                        Busque e selecione a empresa para a qual este cupom será disponibilizado
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate("/admin/coupons")}
                >
                  Cancelar
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Criando..." : "Criar Cupom"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}

export default NewCoupon; 