import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { orderService, companyService } from "@/services/api";
import { useAuth } from "@/contexts/AuthContext";
import { ListOrdersSuccessResponse, GetActiveCompaniesSuccessResponse } from "@/types/api";
import { ShoppingBag } from "lucide-react";
import CompanyOrdersTab from "@/components/CompanyOrdersTab";
import NotificationSettings from "@/components/NotificationSettings";
import ConnectionStatusIndicator from "@/components/ConnectionStatusIndicator";
import NotificationPanel from "@/components/NotificationPanel";

const Orders = () => {
  const { notifications, userRole } = useAuth();

  // Fetch all companies for admin users to ensure we have access to all company data
  const { data: companiesData } = useQuery({
    queryKey: ["admin-companies"],
    queryFn: async () => {
      if (userRole === 'admin') {
        const response = await companyService.getCompanies();
        return response.data as GetActiveCompaniesSuccessResponse;
      }
      return null;
    },
    enabled: userRole === 'admin',
  });

  // Busca todos os pedidos para admin
  const { data: ordersData, isLoading, error } = useQuery({
    queryKey: ["admin-orders"],
    queryFn: async () => {
      const response = await orderService.getAdminOrders(1, 100);
      return response.data as ListOrdersSuccessResponse;
    },
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Carregando pedidos...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500 mb-4">Erro ao carregar pedidos</p>
        <button 
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
        >
          Tentar novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Pedidos</h2>
          <p className="text-muted-foreground">
            Gerencie todos os pedidos do sistema
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* Connection Status */}
          <ConnectionStatusIndicator
            status={notifications.connectionState}
            onReconnect={notifications.reconnect}
            showDetails
            variant="button"
          />

          {/* Notification Panel */}
          <NotificationPanel
            notifications={notifications.notifications}
            unreadCount={notifications.unreadCount}
            onMarkAsRead={notifications.markAsRead}
            onMarkAllAsRead={notifications.markAllAsRead}
            onClearAll={notifications.clearAllNotifications}
          />

          {/* Notification Settings */}
          <NotificationSettings />
        </div>
      </div>

      {/* Orders Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <ShoppingBag className="mr-2" size={20} />
              Todos os Pedidos (Admin)
              {companiesData?.data && (
                <span className="ml-2 text-sm text-muted-foreground">
                  ({companiesData.data.length} empresas)
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              {notifications.connectionState.isConnected && (
                <span className="flex items-center text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                  Notificações ativas
                </span>
              )}
              {notifications.lastNotificationTime && (
                <span>
                  Última atualização: {notifications.lastNotificationTime.toLocaleTimeString()}
                </span>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <CompanyOrdersTab
            companyData={{
              // Admin view - show all orders from all companies
              isAdminView: true,
              allCompanies: companiesData?.data || [],
            }}
            notificationState={{
              unreadCount: notifications.unreadCount,
              lastNotificationTime: notifications.lastNotificationTime,
              connectionState: notifications.connectionState,
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default Orders;
