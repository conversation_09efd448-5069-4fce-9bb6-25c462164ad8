
import React from "react";
import { Link } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, Package, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { companyService, productService } from "@/services/api";
import { GetActiveCompaniesSuccessResponse, GetActiveProductsSuccessPaginatedResponse } from "@/types/api";
import { useData } from "@/contexts/DataContext";
import { useScreenSize } from "@/hooks/use-mobile";

const Dashboard = () => {
  const { isAdmin } = useData();
  const screenSize = useScreenSize();

  // Busca as empresas do servidor
  const { data: companiesData, isLoading: isLoadingCompanies } = useQuery({
    queryKey: ["companies"],
    queryFn: async () => {
      const response = await companyService.getCompanies();
      return response.data as GetActiveCompaniesSuccessResponse;
    },
    enabled: isAdmin, // Only fetch if user is admin
  });

  // Busca os produtos do servidor para mostrar a contagem
  const { data: productsData, isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products", 1, 10],
    queryFn: async () => {
      const response = await productService.getProducts(1, 10);
      return response.data as GetActiveProductsSuccessPaginatedResponse;
    },
    enabled: isAdmin, // Only fetch if user is admin
  });

  // Resumo para o dashboard
  const summaryCards = [
    {
      title: "Total de Parceiros",
      value: isLoadingCompanies ? "..." : companiesData?.data.length || 0,
      icon: Building,
      description: "Empresas parceiras ativas",
      color: "bg-blue-500",
    },
    {
      title: "Produtos",
      value: isLoadingProducts ? "..." : productsData?.totalItems || 0,
      icon: Package,
      description: "Produtos disponíveis",
      color: "bg-green-500",
    },
  ];

  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight">Dashboard</h2>
      </div>

      {/* Cards de resumo */}
      <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-2">
        {summaryCards.map((card, index) => (
          <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow">
            <CardHeader className={`${card.color} text-white p-4 sm:p-6`}>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg sm:text-xl">{card.title}</CardTitle>
                <card.icon size={screenSize === 'mobile' ? 20 : 24} />
              </div>
            </CardHeader>
            <CardContent className="p-4 sm:p-6">
              <div className="text-2xl sm:text-3xl font-bold mb-2">{card.value}</div>
              <CardDescription className="text-sm sm:text-base">{card.description}</CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Últimos parceiros adicionados */}
      <Card>
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="text-lg sm:text-xl">Parceiros Recentes</CardTitle>
          <CardDescription className="text-sm sm:text-base">
            Últimos parceiros cadastrados no sistema
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          {isLoadingCompanies ? (
            <div className="text-center py-8">
              <div className="flex flex-col items-center gap-2">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="text-sm text-muted-foreground">Carregando parceiros...</span>
              </div>
            </div>
          ) : companiesData?.data && companiesData.data.length > 0 ? (
            <div className="space-y-4">
              {companiesData.data.slice(0, 5).map((company) => (
                <div
                  key={company.external_id}
                  className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 border-b pb-4 last:border-b-0"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                      <h3 className="font-medium truncate">{company.name}</h3>
                      <Badge
                        variant={company.is_active ? "default" : "secondary"}
                        className="w-fit"
                      >
                        {company.is_active ? "Ativo" : "Inativo"}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground truncate mt-1">
                      {company.email}
                    </p>
                  </div>
                  <Link to={`/admin/companies/${company.external_id}`} className="w-full sm:w-auto">
                    <Button variant="ghost" size="sm" className="w-full sm:w-auto">
                      <span className="hidden sm:inline">Visualizar</span>
                      <span className="sm:hidden">Ver Detalhes</span>
                    </Button>
                  </Link>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="flex flex-col items-center gap-4">
                <Building className="h-12 w-12 text-gray-400" />
                <div>
                  <p className="text-gray-500 mb-4">Nenhum parceiro cadastrado ainda.</p>
                  <Link to="/admin/companies/new">
                    <Button className="w-full sm:w-auto">
                      <Plus className="h-4 w-4 mr-2" />
                      Cadastrar Parceiro
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>


    </div>
  );
};

export default Dashboard;
