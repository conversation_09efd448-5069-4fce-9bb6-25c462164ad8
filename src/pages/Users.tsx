import React, { useState, useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { userService } from "@/services/api";
import {
  CreateUserRequest,
  CheckEmailExistsResponse,
  User
} from "@/types/api";
import { toast } from "sonner";
import {
  Users,
  UserPlus,
  Loader2
} from "lucide-react";

const UsersPage = () => {
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [formData, setFormData] = useState<CreateUserRequest>({
    name: "",
    email: "",
    cpf: "",
    phone_numbers: [""]
  });
  const [emailError, setEmailError] = useState("");
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
  const [emailDebounceTimer, setEmailDebounceTimer] = useState<NodeJS.Timeout | null>(null);
  const [searchDebounceTimer, setSearchDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Search for user status management
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (emailDebounceTimer) {
        clearTimeout(emailDebounceTimer);
      }
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer);
      }
    };
  }, [emailDebounceTimer, searchDebounceTimer]);

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: (userData: CreateUserRequest) => userService.createUser(userData),
    onSuccess: () => {
      toast("Usuário criado com sucesso!", {
        dismissible: true,
      });
      setCreateModalOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast(error.response?.data?.message || "Erro ao criar usuário", {
        dismissible: true,
      });
    },
  });

  // Update user status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ userExternalId, isActive }: { userExternalId: string; isActive: boolean }) =>
      userService.updateUserStatus(userExternalId, isActive),
    onSuccess: (_, { isActive }) => {
      toast(`Usuário ${isActive ? 'ativado' : 'desativado'} com sucesso!`);
      // Refresh search results to show updated status
      if (searchQuery) {
        handleUserSearch(searchQuery);
      }
    },
    onError: (error: any) => {
      toast(error.response?.data?.message || "Erro ao atualizar status do usuário");
    },
  });

  // Search for users (for status management) - automatic with debounce
  const handleUserSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      const response = await userService.searchUsers(query);
      setSearchResults(response.data.data || []);
    } catch (error) {
      console.error("Erro ao buscar usuários:", error);
      toast("Erro ao buscar usuários");
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Check email exists
  const checkEmailExists = async (email: string) => {
    // Don't check if email is empty or invalid
    if (!email || !email.includes('@') || email.length < 5) {
      setEmailError("");
      setIsCheckingEmail(false);
      return;
    }

    setIsCheckingEmail(true);
    setEmailError(""); // Clear previous errors

    try {
      const response = await userService.checkEmailExists(email);
      const data = response.data as CheckEmailExistsResponse;

      if (data.data === true) {
        setEmailError("Este email já está em uso");
      } else {
        setEmailError("");
      }
    } catch (error: any) {
      console.error("Erro ao verificar email:", error);
      // Don't show error to user for API failures during email check
      // Just log it and allow them to proceed
      setEmailError("");
    } finally {
      setIsCheckingEmail(false);
    }
  };

  // Handle search input changes with debounce
  const handleSearchInputChange = (value: string) => {
    setSearchQuery(value);

    // Clear previous timer
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
    }

    // Clear results if search is empty
    if (!value.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    // Set new timer for debounced search
    const timeoutId = setTimeout(() => {
      handleUserSearch(value);
    }, 500); // 500ms debounce

    setSearchDebounceTimer(timeoutId);
  };

  // Handle form input changes
  const handleInputChange = (field: keyof CreateUserRequest, value: string) => {
    if (field === 'phone_numbers') {
      setFormData(prev => ({ ...prev, phone_numbers: [value] }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    if (field === 'email') {
      // Clear previous timer
      if (emailDebounceTimer) {
        clearTimeout(emailDebounceTimer);
      }

      // Clear previous error when user starts typing
      setEmailError("");
      setIsCheckingEmail(false);

      // Set new timer for debounced email check
      const timeoutId = setTimeout(() => {
        checkEmailExists(value);
      }, 800); // 800ms debounce

      setEmailDebounceTimer(timeoutId);
    }
  };

  // Reset form
  const resetForm = () => {
    // Clear email debounce timer
    if (emailDebounceTimer) {
      clearTimeout(emailDebounceTimer);
      setEmailDebounceTimer(null);
    }

    // Clear search debounce timer
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
      setSearchDebounceTimer(null);
    }

    setFormData({
      name: "",
      email: "",
      cpf: "",
      phone_numbers: [""]
    });
    setEmailError("");
    setIsCheckingEmail(false);
  };

  // Handle user status toggle
  const handleStatusToggle = (user: User) => {
    const newStatus = !user.is_active;
    const action = newStatus ? 'ativar' : 'desativar';

    if (window.confirm(`Tem certeza que deseja ${action} o usuário ${user.name}?`)) {
      updateStatusMutation.mutate({
        userExternalId: user.external_id,
        isActive: newStatus
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (emailError) {
      toast("Corrija os erros no formulário antes de continuar", {
        dismissible: true,
      });
      return;
    }

    if (!formData.name || !formData.email || !formData.cpf || !formData.phone_numbers[0]) {
      toast("Preencha todos os campos obrigatórios", {
        dismissible: true,
      });
      return;
    }

    createUserMutation.mutate(formData);
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Gerenciamento de Usuários</h2>
          <p className="text-muted-foreground">
            Crie novos usuários e gerencie status de usuários existentes
          </p>
        </div>
        <Button onClick={() => setCreateModalOpen(true)}>
          <UserPlus className="mr-2" size={16} />
          Criar Usuário
        </Button>
      </div>

      {/* Create User Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserPlus className="mr-2" size={20} />
            Criar Novo Usuário
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            Clique no botão "Criar Usuário" acima para adicionar um novo usuário ao sistema.
          </p>
        </CardContent>
      </Card>

      {/* User Status Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2" size={20} />
            Gerenciar Status de Usuários
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-6">
            <div className="relative">
              <Input
                placeholder="Digite para buscar usuário por nome, email ou CPF..."
                value={searchQuery}
                onChange={(e) => handleSearchInputChange(e.target.value)}
                className="pr-10"
              />
              {isSearching && (
                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
              )}
            </div>
            {searchQuery && (
              <p className="text-sm text-gray-500 mt-2">
                {isSearching ? "Buscando..." : `Resultados para "${searchQuery}"`}
              </p>
            )}
          </div>

          {/* Search Results */}
          {searchResults.length > 0 ? (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Resultados da Busca</h3>
              {searchResults.map((user) => (
                <div
                  key={user.external_id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{user.name}</h4>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        user.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {user.is_active ? "Ativo" : "Inativo"}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                    {user.cpf && (
                      <p className="text-sm text-muted-foreground">
                        CPF: {user.cpf}
                      </p>
                    )}
                  </div>
                  <Button
                    variant={user.is_active ? "destructive" : "default"}
                    size="sm"
                    onClick={() => handleStatusToggle(user)}
                    disabled={updateStatusMutation.isPending}
                  >
                    {updateStatusMutation.isPending ? (
                      <Loader2 size={14} className="animate-spin" />
                    ) : user.is_active ? (
                      "Desativar"
                    ) : (
                      "Ativar"
                    )}
                  </Button>
                </div>
              ))}
            </div>
          ) : searchQuery && !isSearching ? (
            <div className="text-center py-8">
              <Users size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 mb-2">Nenhum usuário encontrado</p>
              <p className="text-sm text-gray-400">
                Tente ajustar os termos de busca
              </p>
            </div>
          ) : !searchQuery ? (
            <div className="text-center py-8">
              <Users size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 mb-2">Digite no campo acima para buscar usuários</p>
              <p className="text-sm text-gray-400">
                A busca é automática - digite nome, email ou CPF para encontrar usuários
              </p>
            </div>
          ) : null}
        </CardContent>
      </Card>

      {/* Create User Modal */}
      <Dialog open={createModalOpen} onOpenChange={setCreateModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Criar Novo Usuário</DialogTitle>
            <DialogDescription>
              Preencha os dados para criar um novo usuário no sistema.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name">Nome *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Nome completo"
                required
              />
            </div>

            <div>
              <Label htmlFor="email">Email *</Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  className={emailError ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""}
                  required
                />
                {isCheckingEmail && (
                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
                )}
              </div>
              {emailError && (
                <p className="text-sm text-red-500 mt-1 flex items-center">
                  <span className="mr-1">⚠️</span>
                  {emailError}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="cpf">CPF *</Label>
              <Input
                id="cpf"
                value={formData.cpf}
                onChange={(e) => handleInputChange('cpf', e.target.value)}
                placeholder="000.000.000-00"
                required
              />
            </div>

            <div>
              <Label htmlFor="phone">Telefone *</Label>
              <Input
                id="phone"
                value={formData.phone_numbers[0] || ""}
                onChange={(e) => handleInputChange('phone_numbers', e.target.value)}
                placeholder="+5511999999999"
                required
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setCreateModalOpen(false);
                  resetForm();
                }}
                disabled={createUserMutation.isPending}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={createUserMutation.isPending || !!emailError || isCheckingEmail}
                className={emailError ? "opacity-50 cursor-not-allowed" : ""}
              >
                {createUserMutation.isPending ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Criando...
                  </>
                ) : isCheckingEmail ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Verificando email...
                  </>
                ) : (
                  <>
                    <UserPlus size={16} className="mr-2" />
                    Criar Usuário
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UsersPage;
