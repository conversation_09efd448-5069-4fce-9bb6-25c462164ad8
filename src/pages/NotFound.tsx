
import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Home } from "lucide-react";

const NotFound = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="text-center space-y-6">
        <h1 className="text-6xl font-bold text-primary">404</h1>
        <h2 className="text-2xl font-semibold text-gray-800">
          Página não encontrada
        </h2>
        <p className="text-gray-600 max-w-md">
          A página que você está procurando não existe ou foi movida para outro lugar.
        </p>
        <Link to="/dashboard">
          <Button>
            <Home className="mr-2" size={18} />
            Voltar para o Dashboard
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default NotFound;
