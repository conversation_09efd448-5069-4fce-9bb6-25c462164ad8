
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83% 53.3%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 221.2 83% 53.3%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 221.2 83% 53.3%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }

  /* Para corrigir overflow em dispositivos móveis e desktop */
  html, body {
    @apply overflow-x-hidden;
    width: 100%;
    max-width: 100vw;
  }

  /* Fix for root container */
  #root {
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
  }

  /* Prevent any element from causing horizontal scroll */
  * {
    max-width: 100%;
  }

  /* Fix for specific problematic elements */
  .container, .max-w-4xl, .max-w-6xl, .max-w-7xl {
    max-width: calc(100vw - 2rem) !important;
  }

  /* Estilos personalizados para o backoffice */
  .backoffice-card {
    @apply bg-white rounded-lg shadow-md p-4 sm:p-6 transition-all duration-200;
  }

  .backoffice-card:hover {
    @apply shadow-lg;
  }

  .backoffice-input {
    @apply rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent w-full;
  }
}

/* Enhanced responsive classes */
@layer components {
  /* Grid layouts */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  .grid-responsive-2 {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6;
  }

  .grid-responsive-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
  }

  /* Form containers */
  .form-container {
    @apply w-full max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl mx-auto px-4 sm:px-6;
  }

  .form-container-small {
    @apply w-full max-w-sm sm:max-w-md mx-auto px-4 sm:px-6;
  }

  /* Card layouts */
  .card-responsive {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .card-responsive-small {
    @apply p-3 sm:p-4 md:p-6;
  }

  /* Typography */
  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .text-responsive-small {
    @apply text-xs sm:text-sm md:text-base;
  }

  .heading-responsive {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
  }

  .heading-responsive-small {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  /* Button sizes */
  .btn-responsive {
    @apply px-3 py-2 sm:px-4 sm:py-2 md:px-6 md:py-3 text-sm sm:text-base;
  }

  /* Touch targets */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }

  /* Table responsive */
  .table-responsive {
    @apply overflow-x-auto -mx-4 sm:mx-0;
  }

  .table-responsive table {
    @apply min-w-full;
  }

  /* Spacing */
  .space-responsive {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }

  .gap-responsive {
    @apply gap-3 sm:gap-4 lg:gap-6;
  }

  /* Flex layouts */
  .flex-responsive {
    @apply flex flex-col sm:flex-row gap-4 sm:gap-6;
  }

  .flex-responsive-center {
    @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
  }
}

/* Animações e transições */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}
