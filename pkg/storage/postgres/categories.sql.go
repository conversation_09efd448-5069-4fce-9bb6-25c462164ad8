// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: categories.sql

package postgres

import (
	"context"
)

const checkCategoryExists = `-- name: CheckCategoryExists :one
SELECT COUNT(*) > 0 FROM categories WHERE external_id = $1
`

func (q *Queries) CheckCategoryExists(ctx context.Context, externalID string) (bool, error) {
	row := q.db.QueryRow(ctx, checkCategoryExists, externalID)
	var column_1 bool
	err := row.Scan(&column_1)
	return column_1, err
}

const createCategory = `-- name: CreateCategory :one
INSERT INTO categories (name, image, external_id)
  VALUES ($1, $2, $3) RETURNING external_id
`

type CreateCategoryParams struct {
	Name       string
	Image      string
	ExternalID string
}

func (q *Queries) CreateCategory(ctx context.Context, arg CreateCategoryParams) (string, error) {
	row := q.db.QueryRow(ctx, createCategory, arg.Name, arg.Image, arg.ExternalID)
	var external_id string
	err := row.Scan(&external_id)
	return external_id, err
}

const disableCategory = `-- name: DisableCategory :exec
UPDATE categories SET is_active = false WHERE external_id = $1
`

func (q *Queries) DisableCategory(ctx context.Context, externalID string) error {
	_, err := q.db.Exec(ctx, disableCategory, externalID)
	return err
}

const getCategories = `-- name: GetCategories :many
SELECT name, image, external_id, COUNT(*) OVER() AS total_count FROM categories WHERE is_active = true ORDER BY name ASC LIMIT $1 OFFSET $2
`

type GetCategoriesParams struct {
	Limit  int32
	Offset int32
}

type GetCategoriesRow struct {
	Name       string
	Image      string
	ExternalID string
	TotalCount int64
}

func (q *Queries) GetCategories(ctx context.Context, arg GetCategoriesParams) ([]GetCategoriesRow, error) {
	rows, err := q.db.Query(ctx, getCategories, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetCategoriesRow
	for rows.Next() {
		var i GetCategoriesRow
		if err := rows.Scan(
			&i.Name,
			&i.Image,
			&i.ExternalID,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateCategory = `-- name: UpdateCategory :exec
UPDATE categories SET name = $1 WHERE external_id = $2
`

type UpdateCategoryParams struct {
	Name       string
	ExternalID string
}

func (q *Queries) UpdateCategory(ctx context.Context, arg UpdateCategoryParams) error {
	_, err := q.db.Exec(ctx, updateCategory, arg.Name, arg.ExternalID)
	return err
}

const updateCategoryImage = `-- name: UpdateCategoryImage :exec
UPDATE categories SET image = $1 WHERE external_id = $2
`

type UpdateCategoryImageParams struct {
	Image      string
	ExternalID string
}

func (q *Queries) UpdateCategoryImage(ctx context.Context, arg UpdateCategoryImageParams) error {
	_, err := q.db.Exec(ctx, updateCategoryImage, arg.Image, arg.ExternalID)
	return err
}
