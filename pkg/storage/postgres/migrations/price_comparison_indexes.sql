-- Essential indexes for price comparison query performance
-- These indexes should be created with CONCURRENTLY in production to avoid blocking

-- Index for company_products filtering by company and stock
CREATE INDEX IF NOT EXISTS idx_company_products_company_stock 
ON company_products (company_id, stock) WHERE stock > 0;

-- Index for products filtering by active and reviewed status
CREATE INDEX IF NOT EXISTS idx_products_active_reviewed 
ON products (is_active, is_reviewed) WHERE is_active = true AND is_reviewed = true;

-- GIN index for full-text search on product names
CREATE INDEX IF NOT EXISTS idx_products_name_gin 
ON products USING gin(to_tsvector('simple', name));

-- Index for products_categories joins
CREATE INDEX IF NOT EXISTS idx_products_categories_category_product 
ON products_categories (category_id, product_id);

-- Index for company_addresses default lookup
CREATE INDEX IF NOT EXISTS idx_company_addresses_default 
ON company_addresses (company_id, is_default) WHERE is_default = true;

-- Index for companies active status
CREATE INDEX IF NOT EXISTS idx_companies_active 
ON companies (is_active) WHERE is_active = true;

-- Index for user_products_lists external_id lookup
CREATE INDEX IF NOT EXISTS idx_user_products_lists_external_id 
ON user_products_lists (external_id);

-- Index for user_products_lists_items list_id lookup
CREATE INDEX IF NOT EXISTS idx_user_products_lists_items_list_id 
ON user_products_lists_items (list_id);

-- Composite index for company_products price ordering
CREATE INDEX IF NOT EXISTS idx_company_products_company_price 
ON company_products (company_id, price) WHERE stock > 0;

-- Note: Removed duplicate index idx_products_categories_product_category
-- The existing idx_products_categories_category_product (category_id, product_id)
-- is sufficient for most query patterns and JOIN operations.
-- If specific queries require (product_id, category_id) ordering,
-- consider adding it back with performance testing.
