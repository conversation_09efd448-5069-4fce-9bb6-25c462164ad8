-- Add application_type column to push_tokens table for application-specific FCM token management
ALTER TABLE push_tokens 
ADD COLUMN IF NOT EXISTS application_type TEXT NOT NULL DEFAULT 'mobile' 
CHECK (application_type IN ('mobile', 'partner-web'));

-- Drop the old unique constraint and create a new one that includes application_type
DROP INDEX IF EXISTS idx_push_tokens_user_device;

-- Create new unique constraint on user_id + device_id + application_type
-- This allows the same device to have different FCM tokens for different applications
CREATE UNIQUE INDEX IF NOT EXISTS idx_push_tokens_user_device_app 
ON push_tokens(user_id, device_id, application_type);

-- Create index for efficient lookups by application type
CREATE INDEX IF NOT EXISTS idx_push_tokens_app_type 
ON push_tokens(application_type);

-- Add comment for documentation
COMMENT ON COLUMN push_tokens.application_type IS 'Application type: mobile or partner-web. Allows same device to have different FCM tokens for different applications';
