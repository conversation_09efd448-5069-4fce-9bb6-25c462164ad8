-- Create roles table
CREATE TABLE IF NOT EXISTS "roles" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL UNIQUE,
  "description" TEXT NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMP NOT NULL DEFAULT (now())
);

-- Create user_roles junction table
CREATE TABLE IF NOT EXISTS "user_roles" (
  "user_id" INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  "role_id" INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  "created_at" TIMESTAMP NOT NULL DEFAULT (now()),
  PRIMARY KEY (user_id, role_id)
);

-- Insert the three core roles
INSERT INTO roles (name, description) VALUES 
  ('admin', 'Administrator with full system access'),
  ('partner', 'Company owner with partner-level access'),
  ('user', 'Standard user with basic access');

-- Assign user role to all users (including admins, as they should have hierarchical access)
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id
FROM users u
CROSS JOIN roles r
WHERE r.name = 'user'
ON CONFLICT (user_id, role_id) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);
