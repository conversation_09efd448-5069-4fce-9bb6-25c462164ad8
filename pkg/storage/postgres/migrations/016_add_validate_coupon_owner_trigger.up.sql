CREATE OR REPLACE FUNCTION validate_owner()
<PERSON><PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  IF NEW.owner_type = 'admin' THEN
    IF NOT EXISTS (SELECT 1 FROM users WHERE id = NEW.owner_id) THEN
      RAISE EXCEPTION 'owner_id % not found in users', NEW.owner_id;
    END IF;
  ELSIF NEW.owner_type = 'company' THEN
    IF NOT EXISTS (SELECT 1 FROM companies WHERE id = NEW.owner_id) THEN
      RAISE EXCEPTION 'owner_id % not found in companies', NEW.owner_id;
    END IF;
  ELSE
    RAISE EXCEPTION 'invalid owner_type %', NEW.owner_type;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER coupons_validate_owner
BEFORE INSERT OR UPDATE ON coupons
FOR EACH ROW
EXECUTE FUNCTION validate_owner();