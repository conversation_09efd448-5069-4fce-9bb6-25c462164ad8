-- Add 'cancelled' status to the invoices status check constraint
-- This migration updates the status constraint to include the new 'cancelled' status
-- which provides proper separation between payment/system failures ('failed') and intentional cancellations ('cancelled')

-- Drop the existing constraint
ALTER TABLE invoices DROP CONSTRAINT IF EXISTS invoices_status_check;

-- Add the new constraint with 'cancelled' status included
ALTER TABLE invoices ADD CONSTRAINT invoices_status_check 
CHECK (status IN ('pending', 'failed', 'expired', 'processing', 'preparing', 'ready', 'delivering', 'completed', 'cancelled'));

-- Add comment explaining the status meanings
COMMENT ON COLUMN invoices.status IS 'Invoice status: pending (awaiting payment), failed (payment/system failure), expired (payment timeout), processing (payment approved), preparing (order being prepared), ready (ready for pickup/delivery), delivering (out for delivery), completed (finished), cancelled (intentionally cancelled by user or company)';
