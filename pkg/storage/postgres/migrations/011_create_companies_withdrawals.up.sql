-- Create company_withdrawals table for tracking partner withdrawal requests
CREATE TABLE IF NOT EXISTS company_withdrawals (
    id BIGSERIAL PRIMARY KEY,
    company_id BIGINT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL CHECK (amount > 0), -- Amount in cents
    correlation_id VARCHAR(255), -- Woovi correlation ID for tracking
    destination_pix_key VARCHAR(255), -- Destination account info from Woovi
    comment TEXT, -- Additional notes or comments
    end_to_end_id VARCHAR(255), -- PIX end-to-end ID when available
    woovi_response JSONB, -- Store full Woovi API response for audit
    status VARCHAR(20) NOT NULL DEFAULT 'CREATED' CHECK (status IN ('CREATED', 'CONFIRMED', 'FAILED')), -- Withdrawal status
    finished_at TIMESTAMP, -- When the withdrawal was completed or failed
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Add indexes for performance optimization
-- Index for looking up withdrawals by company (most common query)
CREATE INDEX idx_company_withdrawals_company_id ON company_withdrawals(company_id);

-- Index for date-based filtering (used in period queries)
CREATE INDEX idx_company_withdrawals_created_at ON company_withdrawals(created_at DESC);

-- Composite index for company + date filtering (optimizes the main query pattern)
CREATE INDEX idx_company_withdrawals_company_created ON company_withdrawals(company_id, created_at DESC);

-- Index for correlation ID lookups (for tracking and debugging)
CREATE INDEX idx_company_withdrawals_correlation_id ON company_withdrawals(correlation_id) WHERE correlation_id IS NOT NULL;

-- Index for status-based filtering (for webhook updates and status queries)
CREATE INDEX idx_company_withdrawals_status ON company_withdrawals(status);

-- Index for finished_at timestamp (for completed withdrawal queries)
CREATE INDEX idx_company_withdrawals_finished_at ON company_withdrawals(finished_at DESC) WHERE finished_at IS NOT NULL;

-- Add comments for documentation
COMMENT ON TABLE company_withdrawals IS 'Tracks all withdrawal requests made by partner companies through Woovi sub-accounts';
COMMENT ON COLUMN company_withdrawals.amount IS 'Withdrawal amount in cents (Brazilian centavos)';
COMMENT ON COLUMN company_withdrawals.correlation_id IS 'Woovi API correlation ID for tracking the withdrawal request';
COMMENT ON COLUMN company_withdrawals.destination_pix_key IS 'PIX key of the destination account';
COMMENT ON COLUMN company_withdrawals.woovi_response IS 'Complete Woovi API response stored as JSONB for audit purposes';
COMMENT ON COLUMN company_withdrawals.status IS 'Current status of the withdrawal: CREATED (initial), CONFIRMED (confirmed by webhook), FAILED (error occurred)';
COMMENT ON COLUMN company_withdrawals.finished_at IS 'Timestamp when the withdrawal was completed or failed, set by webhook callback';
