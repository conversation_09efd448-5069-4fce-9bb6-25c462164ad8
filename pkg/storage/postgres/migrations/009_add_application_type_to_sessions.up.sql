-- Add application_type column to sessions table for application-layer authentication
ALTER TABLE sessions 
ADD COLUMN IF NOT EXISTS application_type TEXT NOT NULL DEFAULT 'mobile' 
CHECK (application_type IN ('mobile', 'partner-web'));

-- Create index for efficient session lookups by user and application type
CREATE INDEX IF NOT EXISTS idx_sessions_user_app_type_active 
ON sessions(user_id, application_type, is_active) 
WHERE is_active = true;

-- Add comment for documentation
COMMENT ON COLUMN sessions.application_type IS 'Application type: mobile or partner-web. Each user can have only one active session per application type';
