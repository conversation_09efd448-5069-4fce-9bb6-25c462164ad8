-- Remove 'cancelled' status from the invoices status check constraint
-- This migration reverts the addition of the 'cancelled' status

-- Drop the constraint with 'cancelled' status
ALTER TABLE invoices DROP CONSTRAINT IF EXISTS invoices_status_check;

-- Restore the original constraint without 'cancelled' status
ALTER TABLE invoices ADD CONSTRAINT invoices_status_check 
CHECK (status IN ('pending', 'failed', 'expired', 'processing', 'preparing', 'ready', 'delivering', 'completed'));

-- Remove the comment
COMMENT ON COLUMN invoices.status IS NULL;
