-- Create push_tokens table for secure push token management
CREATE TABLE IF NOT EXISTS push_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    push_token VARCHAR(255) NOT NULL UNIQUE,
    device_id VARCHAR(255) NOT NULL,
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('android', 'ios', 'web')),
    fcm_token TEXT NOT NULL,
    app_version VARCHAR(50) NOT NULL,
    device_model TEXT,
    os_version VARCHAR(50),
    expires_at TIMESTAMP NOT NULL,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_push_tokens_device_id ON push_tokens(device_id);
CREATE INDEX IF NOT EXISTS idx_push_tokens_platform ON push_tokens(platform);
CREATE INDEX IF NOT EXISTS idx_push_tokens_expires_at ON push_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_push_tokens_created_at ON push_tokens(created_at);

-- Create unique constraint on user_id + device_id to prevent duplicate tokens per device
CREATE UNIQUE INDEX IF NOT EXISTS idx_push_tokens_user_device ON push_tokens(user_id, device_id);

-- Add comments for documentation
COMMENT ON TABLE push_tokens IS 'Secure push tokens generated by backend for device authentication';
COMMENT ON COLUMN push_tokens.push_token IS 'Secure random token generated by backend (base64 encoded)';
COMMENT ON COLUMN push_tokens.device_id IS 'Unique device identifier provided by client';
COMMENT ON COLUMN push_tokens.platform IS 'Device platform: android, ios, or web';
COMMENT ON COLUMN push_tokens.fcm_token IS 'Firebase Cloud Messaging token for the device';
COMMENT ON COLUMN push_tokens.app_version IS 'Version of the mobile application';
COMMENT ON COLUMN push_tokens.device_model IS 'Device model information (optional)';
COMMENT ON COLUMN push_tokens.os_version IS 'Operating system version (optional)';
COMMENT ON COLUMN push_tokens.expires_at IS 'Token expiration timestamp (90 days from creation)';
COMMENT ON COLUMN push_tokens.last_used_at IS 'Last time this token was used for authentication';
