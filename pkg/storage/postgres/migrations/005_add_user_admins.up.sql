-- Create 3 default admin users and set roles there's no is_admin field 
INSERT INTO users (name, email, cpf, login_code, phone_numbers, external_id)
VALUES
  ('Vinicius', '<EMAIL>', '79220436191', '', '{"[\"11999999999\"]"}', '01JR0X8RNYFJECMV1NBNVK9921'),
  ('Guilherme', '<EMAIL>', '12345678901', '', '{"[\"11999999999\"]"}', '01JR0X8RNYFJECMV1NBNVK9922'),
  ('Lucas', '<EMAIL>', '12345678902', '', '{"[\"11999999999\"]"}', '01JR0X8RNYFJECMV1NBNVK9923')
ON CONFLICT DO NOTHING;

-- Assign user and admin role to the 3 default admin users
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id
FROM users u
CROSS JOIN roles r
WHERE u.email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
) AND r.name = 'admin'
ON CONFLICT (user_id, role_id) DO NOTHING;
