// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: templates_products_lists.sql

package postgres

import (
	"context"
	"time"

	"github.com/jackc/pgtype"
)

const getRandomTemplateList = `-- name: GetRandomTemplateList :one
SELECT
  tpl.id,
  tpl.name,
  tpl.is_active,
  tpl.external_id,
  tpl.created_at,
  tpl.updated_at,
  jsonb_agg(jsonb_build_object(
      'name', p.name,
      'ean', p.ean,
      'quantity', tpli.quantity,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'external_id', p.external_id
  )) AS products
FROM
  public.templates_products_lists tpl
LEFT JOIN public.templates_products_lists_items tpli ON tpl.id = tpli.template_id
LEFT JOIN public.products p ON tpli.product_id = p.id
GROUP BY tpl.id, tpl.name, tpl.is_active, tpl.external_id, tpl.created_at, tpl.updated_at
LIMIT 1
`

type GetRandomTemplateListRow struct {
	ID         int32
	Name       string
	IsActive   bool
	ExternalID string
	CreatedAt  time.Time
	UpdatedAt  time.Time
	Products   pgtype.JSONB
}

func (q *Queries) GetRandomTemplateList(ctx context.Context) (GetRandomTemplateListRow, error) {
	row := q.db.QueryRow(ctx, getRandomTemplateList)
	var i GetRandomTemplateListRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.IsActive,
		&i.ExternalID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Products,
	)
	return i, err
}
