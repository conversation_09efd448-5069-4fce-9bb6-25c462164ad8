// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: products.sql

package postgres

import (
	"context"
	"database/sql"

	"github.com/jackc/pgtype"
)

const addCategoryToProduct = `-- name: AddCategoryToProduct :exec
INSERT INTO products_categories (product_id, category_id)
SELECT
  (SELECT id FROM products p WHERE p.external_id = $1),
  (SELECT id FROM categories c WHERE c.external_id = $2)
ON CONFLICT DO NOTHING
`

type AddCategoryToProductParams struct {
	ProductExternalID  string
	CategoryExternalID string
}

// Adding a category to a product by external IDs
// This query inserts a new association between a product and a category
func (q *Queries) AddCategoryToProduct(ctx context.Context, arg AddCategoryToProductParams) error {
	_, err := q.db.Exec(ctx, addCategoryToProduct, arg.ProductExternalID, arg.CategoryExternalID)
	return err
}

const createProduct = `-- name: CreateProduct :exec
INSERT INTO products (name, ean, image, brand, is_reviewed, is_18_plus, external_id)
VALUES ($1, $2, $3, $4, $5, $6, $7)
`

type CreateProductParams struct {
	Name       string
	Ean        string
	Image      sql.NullString
	Brand      sql.NullString
	IsReviewed bool
	Is18Plus   bool
	ExternalID string
}

func (q *Queries) CreateProduct(ctx context.Context, arg CreateProductParams) error {
	_, err := q.db.Exec(ctx, createProduct,
		arg.Name,
		arg.Ean,
		arg.Image,
		arg.Brand,
		arg.IsReviewed,
		arg.Is18Plus,
		arg.ExternalID,
	)
	return err
}

const deleteAllCategoriesFromProduct = `-- name: DeleteAllCategoriesFromProduct :exec
DELETE FROM products_categories WHERE product_id = (SELECT id FROM products p WHERE p.external_id = $1)
`

func (q *Queries) DeleteAllCategoriesFromProduct(ctx context.Context, productExternalID string) error {
	_, err := q.db.Exec(ctx, deleteAllCategoriesFromProduct, productExternalID)
	return err
}

const getProductByEan = `-- name: GetProductByEan :one
SELECT
    p.ean,
    p.name,
    p.image,
    JSONB_AGG(
        JSONB_BUILD_OBJECT(
            'name', c.name,
            'image', c.image,
            'external_id', c.external_id
        ) ORDER BY c.name
    ) FILTER (WHERE c.id IS NOT NULL) AS categories,
    p.brand,
    p.external_id,
    p.is_18_plus,
    p.is_reviewed
FROM products p
LEFT JOIN products_categories pc ON p.id = pc.product_id
LEFT JOIN categories c ON pc.category_id = c.id
WHERE p.ean = $1
GROUP BY p.id
`

type GetProductByEanRow struct {
	Ean        string
	Name       string
	Image      sql.NullString
	Categories pgtype.JSONB
	Brand      sql.NullString
	ExternalID string
	Is18Plus   bool
	IsReviewed bool
}

// This query retrieves a product by its EAN (European Article Number)
// It returns the product's EAN, name, image, categories, brand, external_id, and is_reviewed status
func (q *Queries) GetProductByEan(ctx context.Context, ean string) (GetProductByEanRow, error) {
	row := q.db.QueryRow(ctx, getProductByEan, ean)
	var i GetProductByEanRow
	err := row.Scan(
		&i.Ean,
		&i.Name,
		&i.Image,
		&i.Categories,
		&i.Brand,
		&i.ExternalID,
		&i.Is18Plus,
		&i.IsReviewed,
	)
	return i, err
}

const getProductsByCategoryExternalId = `-- name: GetProductsByCategoryExternalId :many
WITH base_category AS (
    SELECT id AS category_id
    FROM categories
    WHERE categories.external_id = $1
    LIMIT 1
),
base_products AS (
    SELECT 
        p.id,
        p.external_id,
        p.image,
        p.ean,
        p.is_18_plus,
        p.name,
        p.brand,
        p.updated_at,
        c.image AS category_image,
        c.name AS category_name,
        COUNT(*) OVER() AS total_count
    FROM products p
    JOIN products_categories pc ON p.id = pc.product_id
    JOIN categories c ON pc.category_id = c.id
    JOIN base_category bc ON c.id = bc.category_id
    WHERE 
        p.is_active = true 
        AND p.is_reviewed = true
)
SELECT 
    bp.external_id,
    bp.name,
    bp.ean,
    bp.image,
    bp.is_18_plus,
    bp.brand,
    bp.category_image,
    bp.category_name,
    bp.total_count,
    COALESCE(jsonb_agg(jsonb_build_object(
        'external_id', sp.external_id,
        'name', sp.name,
        'brand', sp.brand
    ) ORDER BY sp.updated_at DESC) 
    FILTER (WHERE sp.id IS NOT NULL), '[]')::jsonb AS similar_products
FROM base_products bp
LEFT JOIN LATERAL (
    SELECT 
        p.id,
        p.external_id,
        p.name,
        p.brand,
        p.updated_at
    FROM products p
    JOIN products_categories pc ON p.id = pc.product_id
    WHERE 
        pc.category_id = (SELECT category_id FROM base_category)
        AND p.id != bp.id
        AND p.is_active = true 
        AND p.is_reviewed = true
        AND p.brand IS DISTINCT FROM bp.brand
        AND to_tsvector('simple', p.name) @@ plainto_tsquery('simple', bp.name)
    ORDER BY p.updated_at DESC
    LIMIT 5
) sp ON TRUE
GROUP BY 
    bp.id,
    bp.external_id,
    bp.name,
    bp.ean,
    bp.image,
    bp.is_18_plus,
    bp.brand,
    bp.category_image,
    bp.category_name,
    bp.total_count,
    bp.updated_at
ORDER BY bp.updated_at DESC
LIMIT $2 OFFSET $3
`

type GetProductsByCategoryExternalIdParams struct {
	ExternalID string
	Limit      int32
	Offset     int32
}

type GetProductsByCategoryExternalIdRow struct {
	ExternalID      string
	Name            string
	Ean             string
	Image           sql.NullString
	Is18Plus        bool
	Brand           sql.NullString
	CategoryImage   string
	CategoryName    string
	TotalCount      int64
	SimilarProducts pgtype.JSONB
}

// This query retrieves products by their category external ID
// It returns the product's details along with the category image and name
// It also counts the total number of products in that category
// The results are ordered by the product's updated_at timestamp in descending order
func (q *Queries) GetProductsByCategoryExternalId(ctx context.Context, arg GetProductsByCategoryExternalIdParams) ([]GetProductsByCategoryExternalIdRow, error) {
	rows, err := q.db.Query(ctx, getProductsByCategoryExternalId, arg.ExternalID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetProductsByCategoryExternalIdRow
	for rows.Next() {
		var i GetProductsByCategoryExternalIdRow
		if err := rows.Scan(
			&i.ExternalID,
			&i.Name,
			&i.Ean,
			&i.Image,
			&i.Is18Plus,
			&i.Brand,
			&i.CategoryImage,
			&i.CategoryName,
			&i.TotalCount,
			&i.SimilarProducts,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getProductsWithCount = `-- name: GetProductsWithCount :many
SELECT
    p.ean,
    p.name,
    p.image,
    JSONB_AGG(
        JSONB_BUILD_OBJECT(
            'name', c.name,
            'image', c.image,
            'external_id', c.external_id
        ) ORDER BY c.name
    ) FILTER (WHERE c.id IS NOT NULL) AS categories,
    p.brand,
    p.external_id,
    p.is_reviewed,
    p.is_18_plus,
    p.is_active,
    COUNT(*) OVER() AS total_count
FROM products p
LEFT JOIN products_categories pc ON p.id = pc.product_id
LEFT JOIN categories c ON pc.category_id = c.id
GROUP BY p.id
ORDER BY p.updated_at DESC
LIMIT $1 OFFSET $2
`

type GetProductsWithCountParams struct {
	Limit  int32
	Offset int32
}

type GetProductsWithCountRow struct {
	Ean        string
	Name       string
	Image      sql.NullString
	Categories pgtype.JSONB
	Brand      sql.NullString
	ExternalID string
	IsReviewed bool
	Is18Plus   bool
	IsActive   bool
	TotalCount int64
}

func (q *Queries) GetProductsWithCount(ctx context.Context, arg GetProductsWithCountParams) ([]GetProductsWithCountRow, error) {
	rows, err := q.db.Query(ctx, getProductsWithCount, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetProductsWithCountRow
	for rows.Next() {
		var i GetProductsWithCountRow
		if err := rows.Scan(
			&i.Ean,
			&i.Name,
			&i.Image,
			&i.Categories,
			&i.Brand,
			&i.ExternalID,
			&i.IsReviewed,
			&i.Is18Plus,
			&i.IsActive,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateProduct = `-- name: UpdateProduct :exec
UPDATE products
SET name = $1,
    ean = $2,
    brand = $3,
    is_reviewed = $4,
    is_18_plus = $5,
    is_active = $7,
    updated_at = now()
WHERE external_id = $6
`

type UpdateProductParams struct {
	Name       string
	Ean        string
	Brand      sql.NullString
	IsReviewed bool
	Is18Plus   bool
	ExternalID string
	IsActive   bool
}

// Updating a product's details
// This query updates the product's name, ean, brand, and is_reviewed status
// based on the provided external_id
// The updated_at timestamp is also set to the current time
func (q *Queries) UpdateProduct(ctx context.Context, arg UpdateProductParams) error {
	_, err := q.db.Exec(ctx, updateProduct,
		arg.Name,
		arg.Ean,
		arg.Brand,
		arg.IsReviewed,
		arg.Is18Plus,
		arg.ExternalID,
		arg.IsActive,
	)
	return err
}

const updateProductImage = `-- name: UpdateProductImage :exec
UPDATE products SET image = $1 WHERE external_id = $2
`

type UpdateProductImageParams struct {
	Image      sql.NullString
	ExternalID string
}

func (q *Queries) UpdateProductImage(ctx context.Context, arg UpdateProductImageParams) error {
	_, err := q.db.Exec(ctx, updateProductImage, arg.Image, arg.ExternalID)
	return err
}
