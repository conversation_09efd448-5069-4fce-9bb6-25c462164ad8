package custom_models

import (
	"encoding/json"
	"testing"
)

func TestAddressParams(t *testing.T) {
	jsonStr := `{
        "street": "Rua das Flores",
        "number": "123",
        "neighborhood": "Downtown",
        "zip_code": "12345678",
        "city": "São Paulo",
        "state": "SP",
        "latitude": -23.5505,
        "longitude": -46.6333
    }`

	var location Location
	if err := json.Unmarshal([]byte(jsonStr), &location); err != nil {
		t.Fatalf("Error unmarshalling JSON: %v", err)
	}

	tests := []struct {
		name string
		want float64
		got  float64
	}{
		{"Latitude", -23.5505, location.Latitude},
		{"Longitude", -46.6333, location.Longitude},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.got != tt.want {
				t.<PERSON>rf("got %v, want %v", tt.got, tt.want)
			}
		})
	}
}
