-- name: UpsertProductsList :one
WITH upsert_list AS (
  INSERT INTO user_products_lists (user_id, name, icon_url, is_public, external_id)
  VALUES ($1, $2, $3, $4, $5)
  ON CONFLICT (user_id, name) DO UPDATE
    SET updated_at = now()
  RETURNING id,external_id,
            (xmax = 0) AS is_insert  -- true se foi INSERT, false se foi UPDATE
)
SELECT id,external_id,
       CASE WHEN is_insert THEN 'create' ELSE 'update' END AS action
FROM upsert_list;


-- name: UpsertUserProductsListItem :exec
WITH product AS (
  SELECT id
  FROM products
  WHERE external_id = $1  -- external_id do produto
)
INSERT INTO user_products_lists_items (list_id, product_id, quantity)
SELECT $2, id, $3  -- list_id, product_id (obtido do JOIN), quantity
FROM product
ON CONFLICT (list_id, product_id) DO UPDATE
  SET quantity = EXCLUDED.quantity;

-- name: DeleteMissingUserProductsListItems :exec
WITH product_ids AS (
  SELECT id
  FROM products
  WHERE external_id = ANY(sqlc.arg(external_ids)::text[]) -- $external_ids será o nome do parâmetro
)
DELETE FROM user_products_lists_items
WHERE list_id = $1
  AND product_id NOT IN (SELECT id FROM product_ids);

-- name: DeleteUserProductsList :exec
DELETE FROM user_products_lists
WHERE external_id = $1
  AND user_id = $2;

-- name: GetUserProductsLists :many
SELECT
  COUNT(*) OVER() AS total_count,
  user_products_lists.id AS id,
  user_products_lists.name AS name,
  user_products_lists.icon_url AS icon_url,
  user_products_lists.external_id AS external_id,
  user_products_lists.is_public AS is_public,
  user_products_lists.created_at AS created_at,
  user_products_lists.updated_at AS updated_at,
  json_agg(
    jsonb_build_object(
      'quantity', user_products_lists_items.quantity,
      'name', Products.name,
      'ean', Products.ean,
      'description', Products.description,
      'image', Products.image,
      'brand', Products.brand,
      'external_id', Products.external_id,
      'categories', (
        SELECT json_agg(jsonb_build_object('external_id', c.external_id, 'name', c.name, 'image', c.image))
        FROM products_categories pc
        JOIN categories c ON c.id = pc.category_id
        WHERE pc.product_id = Products.id
      )
    )
  ) AS products
FROM
  user_products_lists
LEFT JOIN user_products_lists_items ON user_products_lists.id = user_products_lists_items.list_id
LEFT JOIN products AS Products ON user_products_lists_items.product_id = Products.id
WHERE
  (user_products_lists.user_id = $1)
  AND (Products.is_reviewed = TRUE)
  AND (Products.is_active = TRUE)
GROUP BY
  user_products_lists.id
ORDER BY
  user_products_lists.updated_at DESC
LIMIT $2 OFFSET $3;



-- name: GetUserProductsListByExternalId :one
SELECT
  user_products_lists.id AS id,
  user_products_lists.name AS name,
  user_products_lists.icon_url AS icon_url,
  user_products_lists.external_id AS external_id,
  user_products_lists.is_public AS is_public,
  user_products_lists.created_at AS created_at,
  user_products_lists.updated_at AS updated_at,
  json_agg(
    jsonb_build_object(
      'quantity', user_products_lists_items.quantity,
      'name', Products.name,
      'ean', Products.ean,
      'description', Products.description,
      'image', Products.image,
      'brand', Products.brand,
      'external_id', Products.external_id,
      'categories', (
        SELECT json_agg(jsonb_build_object('external_id', c.external_id, 'name', c.name, 'image', c.image))
        FROM products_categories pc
        JOIN categories c ON c.id = pc.category_id
        WHERE pc.product_id = Products.id
      )
    )
  ) AS products
FROM
  user_products_lists
LEFT JOIN user_products_lists_items ON user_products_lists.id = user_products_lists_items.list_id
LEFT JOIN products AS Products ON user_products_lists_items.product_id = Products.id
WHERE
  (user_products_lists.user_id = $1)
  AND (user_products_lists.external_id = $2)
  AND (Products.is_reviewed = TRUE)
  AND (Products.is_active = TRUE)
GROUP BY
  user_products_lists.id
ORDER BY
  user_products_lists.updated_at DESC
LIMIT 1;

-- name: CheckIfUserProductsListBelongsToUser :one
SELECT
  EXISTS (
    SELECT 1
    FROM user_products_lists
    WHERE user_id = $1
      AND external_id = $2
  ) AS exists;