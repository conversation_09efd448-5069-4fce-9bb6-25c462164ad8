-- name: GetCompanyAvailableBalance :one
SELECT 
    COALESCE(SUM(amount), 0) as available_balance,
    COUNT(*) as available_payout_count
FROM invoice_payouts 
WHERE company_id = $1 
  AND status = 'available' 
  AND available_after <= NOW();

-- name: GetCompanyAvailableBalanceFast :one
SELECT 
    COALESCE(available_balance, 0) as available_balance,
    COALESCE(available_payout_count, 0) as available_payout_count,
    latest_available_date
FROM company_available_balances 
WHERE company_id = $1;

-- name: GetEligiblePayoutsForWithdrawal :many
SELECT 
    id,
    invoice_id,
    company_id,
    amount,
    finalized_at,
    available_after,
    created_at
FROM invoice_payouts 
WHERE company_id = $1 
  AND status = 'available' 
  AND available_after <= NOW()
ORDER BY available_after ASC
FOR UPDATE SKIP LOCKED;

-- name: AllocatePayoutsForWithdrawal :exec
UPDATE invoice_payouts 
SET status = 'withdrawn', updated_at = NOW()
WHERE id = ANY($1::BIGINT[]) 
  AND status = 'available' 
  AND company_id = $2;

-- name: CreateWithdrawalWithPayouts :one
WITH new_withdrawal AS (
  INSERT INTO company_withdrawals (
    company_id,
    amount,
    correlation_id,
    destination_pix_key,
    comment,
    end_to_end_id,
    woovi_response
  ) VALUES (
    $1, $2, $3, $4, $5, $6, $7
  ) RETURNING id, created_at
),
payout_links AS (
  INSERT INTO withdrawal_payouts (withdrawal_id, payout_id)
  SELECT 
    (SELECT id FROM new_withdrawal),
    unnest($8::BIGINT[])
  RETURNING withdrawal_id, payout_id
)
SELECT 
  nw.id,
  nw.created_at,
  array_agg(pl.payout_id) as linked_payout_ids
FROM new_withdrawal nw
LEFT JOIN payout_links pl ON pl.withdrawal_id = nw.id
GROUP BY nw.id, nw.created_at;

-- name: GetWithdrawalWithPayouts :one
SELECT 
    cw.id,
    cw.company_id,
    cw.amount,
    cw.status,
    cw.correlation_id,
    cw.destination_pix_key,
    cw.comment,
    cw.end_to_end_id,
    cw.woovi_response,
    cw.finished_at,
    cw.created_at,
    c.external_id as company_external_id,
    c.name as company_name,
    json_agg(
        DISTINCT jsonb_build_object(
            'payout_id', ip.id,
            'invoice_id', ip.invoice_id,
            'amount', ip.amount,
            'finalized_at', ip.finalized_at,
            'available_after', ip.available_after,
            'order_id', i.order_id
        )
    ) FILTER (WHERE ip.id IS NOT NULL) as payouts
FROM company_withdrawals cw
JOIN companies c ON c.id = cw.company_id
LEFT JOIN withdrawal_payouts wp ON wp.withdrawal_id = cw.id
LEFT JOIN invoice_payouts ip ON ip.id = wp.payout_id
LEFT JOIN invoices i ON i.id = ip.invoice_id
WHERE cw.id = $1
GROUP BY cw.id, c.external_id, c.name;

-- name: GetCompanyPayoutHistory :many
WITH company_info AS (
  SELECT id, external_id, name
  FROM companies
  WHERE external_id = $1
),
period_filter AS (
  SELECT
    CASE
      WHEN $4::text = '1m' THEN NOW() - INTERVAL '1 month'
      WHEN $4::text = '3m' THEN NOW() - INTERVAL '3 months'
      WHEN $4::text = '6m' THEN NOW() - INTERVAL '6 months'
      WHEN $4::text = '1y' THEN NOW() - INTERVAL '1 year'
      ELSE NOW() - INTERVAL '30 days' -- Default to 30 days
    END AS start_date
),
filtered_payouts AS (
  SELECT
    ip.id,
    ip.invoice_id,
    ip.status,
    ip.amount,
    ip.finalized_at,
    ip.available_after,
    ip.created_at,
    i.order_id,
    ci.external_id as company_external_id,
    ci.name as company_name,
    CASE 
      WHEN wp.withdrawal_id IS NOT NULL THEN wp.withdrawal_id
      ELSE NULL
    END as withdrawal_id
  FROM invoice_payouts ip
  JOIN company_info ci ON ip.company_id = ci.id
  JOIN invoices i ON i.id = ip.invoice_id
  LEFT JOIN withdrawal_payouts wp ON wp.payout_id = ip.id
  CROSS JOIN period_filter pf
  WHERE ip.created_at >= pf.start_date
  ORDER BY ip.created_at DESC
),
total_info AS (
  SELECT
    COUNT(*) as total_count,
    COALESCE(SUM(amount), 0) as total_amount
  FROM filtered_payouts
)
SELECT
  fp.id,
  fp.invoice_id,
  fp.status,
  fp.amount,
  fp.finalized_at,
  fp.available_after,
  fp.created_at,
  fp.order_id,
  fp.company_external_id,
  fp.company_name,
  fp.withdrawal_id,
  ti.total_count,
  ti.total_amount
FROM filtered_payouts fp
CROSS JOIN total_info ti
LIMIT $2 OFFSET $3;

-- name: UpdatePayoutStatus :exec
UPDATE invoice_payouts 
SET status = $2, updated_at = NOW()
WHERE id = $1;

-- name: RefreshCompanyBalancesView :exec
REFRESH MATERIALIZED VIEW company_available_balances;

-- name: GetUserCompaniesBalances :many
SELECT
    c.id,
    c.external_id,
    c.name,
    c.pix_key,
    c.is_active,
    COALESCE(cab.available_balance, 0) as available_balance,
    COALESCE(cab.available_payout_count, 0) as available_payout_count,
    cab.latest_available_date,
    COUNT(*) OVER() AS total_count
FROM companies c
LEFT JOIN company_available_balances cab ON c.id = cab.company_id
WHERE c.owner_id = $1
ORDER BY c.name ASC
LIMIT $2 OFFSET $3;

-- name: GetPayoutsByInvoiceIDs :many
SELECT 
    id,
    invoice_id,
    company_id,
    status,
    amount,
    finalized_at,
    available_after,
    created_at,
    updated_at
FROM invoice_payouts 
WHERE invoice_id = ANY($1::BIGINT[])
ORDER BY created_at DESC;

-- name: BlockPayouts :exec
UPDATE invoice_payouts 
SET status = 'blocked', updated_at = NOW()
WHERE id = ANY($1::BIGINT[]) 
  AND status IN ('pending', 'available');

-- name: UnblockPayouts :exec
UPDATE invoice_payouts 
SET status = CASE 
    WHEN available_after <= NOW() THEN 'available'
    ELSE 'pending'
END,
updated_at = NOW()
WHERE id = ANY($1::BIGINT[]) 
  AND status = 'blocked';

-- name: GetCompanyPayoutSummary :one
SELECT 
    company_id,
    COUNT(*) as total_payouts,
    COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount,
    COALESCE(SUM(CASE WHEN status = 'available' THEN amount ELSE 0 END), 0) as available_amount,
    COALESCE(SUM(CASE WHEN status = 'withdrawn' THEN amount ELSE 0 END), 0) as withdrawn_amount,
    COALESCE(SUM(CASE WHEN status = 'blocked' THEN amount ELSE 0 END), 0) as blocked_amount,
    MIN(available_after) as earliest_available_date,
    MAX(available_after) as latest_available_date
FROM invoice_payouts 
WHERE company_id = $1
GROUP BY company_id;
