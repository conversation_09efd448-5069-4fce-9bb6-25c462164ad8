-- name: SearchGlobal :many
-- Global search - exactly like legacy but with pagination
WITH search_query AS (
  SELECT plainto_tsquery('portuguese', $1) AS query,
         '%' || $1 || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    ts_rank(
      setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
      setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B'),
      sq.query
    ) AS rank
  FROM products p, search_query sq
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND (
      (
        setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
        setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B')
      ) @@ sq.query
      OR p.name ILIKE sq.ilike_pattern
      OR p.brand ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank,
    COUNT(*) OVER() AS total_count
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT
  product_name,
  product_external_id,
  product_image,
  product_brand,
  is_18_plus,
  category_name,
  category_image,
  category_external_id,
  rank AS search_rank,
  total_count
FROM results_with_categories
ORDER BY rank DESC
LIMIT $2 OFFSET $3;

-- name: SearchByCategory :many
-- Category search - copy of working legacy query with category filter
WITH search_query AS (
  SELECT plainto_tsquery('portuguese', $1) AS query,
         '%' || $1 || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    ts_rank(
      setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
      setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B'),
      sq.query
    ) AS rank
  FROM products p
  CROSS JOIN search_query sq
  JOIN products_categories pc ON p.id = pc.product_id
  JOIN categories cat ON pc.category_id = cat.id
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND
    cat.external_id = $2 AND (
      (
        setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
        setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B')
      ) @@ sq.query
      OR p.name ILIKE sq.ilike_pattern
      OR p.brand ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank,
    COUNT(*) OVER() AS total_count
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT
  product_name,
  product_external_id,
  product_image,
  product_brand,
  is_18_plus,
  category_name,
  category_image,
  category_external_id,
  rank AS search_rank,
  total_count
FROM results_with_categories
ORDER BY rank DESC
LIMIT $3 OFFSET $4;

-- name: SearchByCompany :many
-- Company search - global search + company pricing/stock
WITH search_query AS (
  SELECT plainto_tsquery('portuguese', $1) AS query,
         '%' || $1 || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    cp.price,
    cp.discount,
    cp.stock,
    comp.name AS company_name,
    comp.external_id AS company_external_id,
    ts_rank(
      setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
      setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B'),
      sq.query
    ) AS rank
  FROM products p
  CROSS JOIN search_query sq
  JOIN company_products cp ON p.id = cp.product_id
  JOIN companies comp ON cp.company_id = comp.id
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND
    comp.external_id = $2 AND
    comp.is_active = true AND
    cp.stock > 0 AND (
      (
        setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
        setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B')
      ) @@ sq.query
      OR p.name ILIKE sq.ilike_pattern
      OR p.brand ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    pr.price,
    pr.discount,
    pr.stock,
    pr.company_name,
    pr.company_external_id,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank,
    COUNT(*) OVER() AS total_count
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT
  product_name,
  product_external_id,
  product_image,
  product_brand,
  is_18_plus,
  price,
  discount,
  stock,
  company_name,
  company_external_id,
  category_name,
  category_image,
  category_external_id,
  rank AS search_rank,
  total_count
FROM results_with_categories
ORDER BY rank DESC
LIMIT $3 OFFSET $4;

-- name: SearchByCompanyAndCategory :many
-- Company + Category search - company search + category filter
WITH search_query AS (
  SELECT plainto_tsquery('portuguese', $1) AS query,
         '%' || $1 || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    cp.price,
    cp.discount,
    cp.stock,
    comp.name AS company_name,
    comp.external_id AS company_external_id,
    ts_rank(
      setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
      setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B'),
      sq.query
    ) AS rank
  FROM products p
  CROSS JOIN search_query sq
  JOIN company_products cp ON p.id = cp.product_id
  JOIN companies comp ON cp.company_id = comp.id
  JOIN products_categories pc ON p.id = pc.product_id
  JOIN categories cat ON pc.category_id = cat.id
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND
    comp.external_id = $2 AND
    cat.external_id = $3 AND
    comp.is_active = true AND
    cp.stock > 0 AND (
      (
        setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
        setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B')
      ) @@ sq.query
      OR p.name ILIKE sq.ilike_pattern
      OR p.brand ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    pr.price,
    pr.discount,
    pr.stock,
    pr.company_name,
    pr.company_external_id,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank,
    COUNT(*) OVER() AS total_count
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT
  product_name,
  product_external_id,
  product_image,
  product_brand,
  is_18_plus,
  price,
  discount,
  stock,
  company_name,
  company_external_id,
  category_name,
  category_image,
  category_external_id,
  rank AS search_rank,
  total_count
FROM results_with_categories
ORDER BY rank DESC
LIMIT $4 OFFSET $5;

-- name: FullTextSearchProduct :many
-- Legacy endpoint for backward compatibility
WITH search_query AS (
  SELECT plainto_tsquery('portuguese', $1) AS query,
         '%' || $1 || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    ts_rank(
      setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
      setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B'),
      sq.query
    ) AS rank
  FROM products p, search_query sq
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND (
      (
        setweight(to_tsvector('portuguese', coalesce(p.name, '')), 'A') ||
        setweight(to_tsvector('portuguese', coalesce(p.brand, '')), 'B')
      ) @@ sq.query
      OR p.name ILIKE sq.ilike_pattern
      OR p.brand ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT json_build_object(
  'product_name', product_name,
  'product_image', product_image,
  'product_brand', product_brand,
  'product_external_id', product_external_id,
  'product_is_18_plus', is_18_plus,
  'category_name', category_name,
  'category_image', category_image,
  'category_external_id', category_external_id,
  'search_rank', rank
)
FROM results_with_categories
ORDER BY rank DESC
LIMIT 3;

