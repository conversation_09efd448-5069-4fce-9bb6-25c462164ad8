
-- name: DeleteUserDeviceToken :exec
DELETE FROM push_tokens 
WHERE user_id = $1 AND device_id = $2;

-- name: DeleteExpiredPushTokens :exec
DELETE FROM push_tokens
WHERE expires_at < $1;

-- name: GetActivePushTokensByUserID :many
SELECT
    id,
    user_id,
    push_token,
    device_id,
    platform,
    fcm_token,
    app_version,
    device_model,
    os_version,
    expires_at,
    last_used_at,
    created_at,
    updated_at
FROM push_tokens
WHERE user_id = $1 AND expires_at > NOW()
ORDER BY created_at DESC;

-- name: UpsertFCMToken :exec
INSERT INTO push_tokens (
    user_id,
    push_token,
    device_id,
    platform,
    fcm_token,
    app_version,
    application_type,
    device_model,
    os_version,
    expires_at,
    created_at,
    updated_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
) ON CONFLICT (user_id, device_id, application_type)
DO UPDATE SET
    fcm_token = EXCLUDED.fcm_token,
    platform = EXCLUDED.platform,
    app_version = EXCLUDED.app_version,
    device_model = EXCLUDED.device_model,
    os_version = EXCLUDED.os_version,
    expires_at = EXCLUDED.expires_at,
    updated_at = EXCLUDED.updated_at;

-- name: RefreshFCMTokenExpiration :exec
UPDATE push_tokens
SET expires_at = $1, updated_at = NOW()
WHERE user_id = $2 AND device_id = $3;

-- name: GetActiveFCMTokensByUserAndAppType :many
SELECT id, user_id, push_token, device_id, platform, fcm_token, app_version, application_type, device_model, os_version, expires_at, last_used_at, created_at, updated_at
FROM push_tokens
WHERE user_id = $1 AND application_type = $2 AND expires_at > NOW();

-- name: GetActiveFCMTokensByUser :many
SELECT id, user_id, push_token, device_id, platform, fcm_token, app_version, application_type, device_model, os_version, expires_at, last_used_at, created_at, updated_at
FROM push_tokens
WHERE user_id = $1 AND expires_at > NOW();
