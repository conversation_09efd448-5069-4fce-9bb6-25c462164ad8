-- name: CreateSession :exec
INSERT INTO sessions (user_id, access_token, refresh_token, device, ip, application_type)
  VALUES ($1, $2, $3, $4, $5, $6);

-- name: InvalidateSession :exec
UPDATE sessions SET is_active = false WHERE id = $1;

-- name: CheckIfExistsActiveSession :one
SELECT id, is_active FROM sessions WHERE user_id = $1 AND is_active = true;

-- name: GetActiveSessionWithAppType :one
SELECT id, is_active, application_type FROM sessions WHERE user_id = $1 AND is_active = true;

-- name: GetSessionByAccessToken :one
SELECT id, user_id, is_active, application_type FROM sessions WHERE access_token = $1 AND is_active = true;

-- name: CheckIfExistsActiveSessionByAppType :one
SELECT id, is_active, application_type FROM sessions
WHERE user_id = $1 AND application_type = $2 AND is_active = true;

-- name: InvalidateSessionsByUserAndAppType :exec
UPDATE sessions SET is_active = false
WHERE user_id = $1 AND application_type = $2 AND is_active = true;

-- name: GetActiveSessionsByUser :many
SELECT id, user_id, application_type, device, ip, created_at
FROM sessions
WHERE user_id = $1 AND is_active = true;