-- name: GetCompanyInfoWithProducts :one
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.pix_key,
  c.external_id,
  c.is_active,
  c.commission_rate,
  c.cashback_rate,
  c.shipping_fee,
  c.owner_id,
  COALESCE(
    jsonb_agg(
      jsonb_build_object(
        'id', p.id,
        'price', cp.price,
        'discount', cp.discount,
        'stock', cp.stock,
        'name', p.name,
        'ean', p.ean,
        'description', p.description,
        'image', p.image,
        'brand', p.brand,
        'external_id', p.external_id,
        'is_18_plus', p.is_18_plus,
        'categories', COALESCE(
          (SELECT jsonb_agg(DISTINCT jsonb_build_object(
            'name', cat.name,
            'image', cat.image,
            'external_id', cat.external_id
          ))
          FROM products_categories pc_cat
          JOIN categories cat ON cat.id = pc_cat.category_id
          WHERE pc_cat.product_id = p.id),
          '[]'::jsonb
        )
      )
    ) FILTER (WHERE p.id IS NOT NULL),
    '[]'::jsonb
  ) AS products
FROM
  public.companies c
LEFT JOIN public.company_products cp ON c.id = cp.company_id
LEFT JOIN public.products p ON cp.product_id = p.id
WHERE
  c.external_id = $1
  AND c.is_active = TRUE
  AND p.is_reviewed = TRUE
  AND p.is_active = TRUE
GROUP BY
  c.id;

-- name: UpdateCompanyProductsStock :exec
WITH invoice_data AS (
  SELECT
    i.company_id,
    ip.product_id,
    ip.quantity
  FROM invoices i
  JOIN invoice_products ip ON i.id = ip.invoice_id
  WHERE i.order_id = $1
)
UPDATE company_products cp
SET stock = stock - id.quantity
FROM invoice_data id
WHERE cp.company_id = id.company_id AND cp.product_id = id.product_id;

-- name: DeleteProductsWithStockZero :exec
DELETE FROM company_products
WHERE company_id = $1 AND stock = 0;