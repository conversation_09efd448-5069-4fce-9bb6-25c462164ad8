-- name: CreateCategory :one
INSERT INTO categories (name, image, external_id)
  VALUES ($1, $2, $3) RETURNING external_id;

-- name: GetCategories :many
SELECT name, image, external_id, COUNT(*) OVER() AS total_count FROM categories WHERE is_active = true ORDER BY name ASC LIMIT $1 OFFSET $2;

-- name: CheckCategoryExists :one
SELECT COUNT(*) > 0 FROM categories WHERE external_id = $1;

-- name: DisableCategory :exec
UPDATE categories SET is_active = false WHERE external_id = $1;

-- name: UpdateCategory :exec
UPDATE categories SET name = $1 WHERE external_id = $2;

-- name: UpdateCategoryImage :exec
UPDATE categories SET image = $1 WHERE external_id = $2;
