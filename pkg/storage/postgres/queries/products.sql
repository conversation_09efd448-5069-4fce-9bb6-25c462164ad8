-- name: CreateProduct :exec
INSERT INTO products (name, ean, image, brand, is_reviewed, is_18_plus, external_id)
VALUES ($1, $2, $3, $4, $5, $6, $7);

-- name: GetProductsWithCount :many
SELECT
    p.ean,
    p.name,
    p.image,
    JSONB_AGG(
        JSONB_BUILD_OBJECT(
            'name', c.name,
            'image', c.image,
            'external_id', c.external_id
        ) ORDER BY c.name
    ) FILTER (WHERE c.id IS NOT NULL) AS categories,
    p.brand,
    p.external_id,
    p.is_reviewed,
    p.is_18_plus,
    p.is_active,
    COUNT(*) OVER() AS total_count
FROM products p
LEFT JOIN products_categories pc ON p.id = pc.product_id
LEFT JOIN categories c ON pc.category_id = c.id
GROUP BY p.id
ORDER BY p.updated_at DESC
LIMIT $1 OFFSET $2;


-- name: UpdateProduct :exec
-- Updating a product's details
-- This query updates the product's name, ean, brand, and is_reviewed status
-- based on the provided external_id
-- The updated_at timestamp is also set to the current time
UPDATE products
SET name = $1,
    ean = $2,
    brand = $3,
    is_reviewed = $4,
    is_18_plus = $5,
    is_active = $7,
    updated_at = now()
WHERE external_id = $6;


-- name: DeleteAllCategoriesFromProduct :exec
DELETE FROM products_categories WHERE product_id = (SELECT id FROM products p WHERE p.external_id = sqlc.arg(product_external_id));

-- name: AddCategoryToProduct :exec
-- Adding a category to a product by external IDs
-- This query inserts a new association between a product and a category
INSERT INTO products_categories (product_id, category_id)
SELECT
  (SELECT id FROM products p WHERE p.external_id = sqlc.arg(product_external_id)),
  (SELECT id FROM categories c WHERE c.external_id = sqlc.arg(category_external_id))
ON CONFLICT DO NOTHING;

-- name: UpdateProductImage :exec
UPDATE products SET image = $1 WHERE external_id = $2;

-- name: GetProductByEan :one
-- This query retrieves a product by its EAN (European Article Number)
-- It returns the product's EAN, name, image, categories, brand, external_id, and is_reviewed status
SELECT
    p.ean,
    p.name,
    p.image,
    JSONB_AGG(
        JSONB_BUILD_OBJECT(
            'name', c.name,
            'image', c.image,
            'external_id', c.external_id
        ) ORDER BY c.name
    ) FILTER (WHERE c.id IS NOT NULL) AS categories,
    p.brand,
    p.external_id,
    p.is_18_plus,
    p.is_reviewed
FROM products p
LEFT JOIN products_categories pc ON p.id = pc.product_id
LEFT JOIN categories c ON pc.category_id = c.id
WHERE p.ean = $1
GROUP BY p.id;

-- name: GetProductsByCategoryExternalId :many
-- This query retrieves products by their category external ID
-- It returns the product's details along with the category image and name
-- It also counts the total number of products in that category
-- The results are ordered by the product's updated_at timestamp in descending order
WITH base_category AS (
    SELECT id AS category_id
    FROM categories
    WHERE categories.external_id = $1
    LIMIT 1
),
base_products AS (
    SELECT 
        p.id,
        p.external_id,
        p.image,
        p.ean,
        p.is_18_plus,
        p.name,
        p.brand,
        p.updated_at,
        c.image AS category_image,
        c.name AS category_name,
        COUNT(*) OVER() AS total_count
    FROM products p
    JOIN products_categories pc ON p.id = pc.product_id
    JOIN categories c ON pc.category_id = c.id
    JOIN base_category bc ON c.id = bc.category_id
    WHERE 
        p.is_active = true 
        AND p.is_reviewed = true
)
SELECT 
    bp.external_id,
    bp.name,
    bp.ean,
    bp.image,
    bp.is_18_plus,
    bp.brand,
    bp.category_image,
    bp.category_name,
    bp.total_count,
    COALESCE(jsonb_agg(jsonb_build_object(
        'external_id', sp.external_id,
        'name', sp.name,
        'brand', sp.brand
    ) ORDER BY sp.updated_at DESC) 
    FILTER (WHERE sp.id IS NOT NULL), '[]')::jsonb AS similar_products
FROM base_products bp
LEFT JOIN LATERAL (
    SELECT 
        p.id,
        p.external_id,
        p.name,
        p.brand,
        p.updated_at
    FROM products p
    JOIN products_categories pc ON p.id = pc.product_id
    WHERE 
        pc.category_id = (SELECT category_id FROM base_category)
        AND p.id != bp.id
        AND p.is_active = true 
        AND p.is_reviewed = true
        AND p.brand IS DISTINCT FROM bp.brand
        AND to_tsvector('simple', p.name) @@ plainto_tsquery('simple', bp.name)
    ORDER BY p.updated_at DESC
    LIMIT 5
) sp ON TRUE
GROUP BY 
    bp.id,
    bp.external_id,
    bp.name,
    bp.ean,
    bp.image,
    bp.is_18_plus,
    bp.brand,
    bp.category_image,
    bp.category_name,
    bp.total_count,
    bp.updated_at
ORDER BY bp.updated_at DESC
LIMIT $2 OFFSET $3;
