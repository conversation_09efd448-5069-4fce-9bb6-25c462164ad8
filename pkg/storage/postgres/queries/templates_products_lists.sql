-- name: GetRandomTemplateList :one
SELECT
  tpl.id,
  tpl.name,
  tpl.is_active,
  tpl.external_id,
  tpl.created_at,
  tpl.updated_at,
  jsonb_agg(jsonb_build_object(
      'name', p.name,
      'ean', p.ean,
      'quantity', tpli.quantity,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'external_id', p.external_id
  )) AS products
FROM
  public.templates_products_lists tpl
LEFT JOIN public.templates_products_lists_items tpli ON tpl.id = tpli.template_id
LEFT JOIN public.products p ON tpli.product_id = p.id
GROUP BY tpl.id, tpl.name, tpl.is_active, tpl.external_id, tpl.created_at, tpl.updated_at
LIMIT 1;
