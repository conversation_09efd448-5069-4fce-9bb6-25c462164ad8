-- name: GetCouponByCode :one
SELECT * FROM coupons WHERE code = $1;

-- name: UseCoupon :one
UPDATE coupons SET quantity = quantity - 1 WHERE code = $1 AND quantity > 0 RETURNING quantity;

-- name: RegisterCouponUsage :exec
INSERT INTO users_coupons (user_id, coupon_id) VALUES ($1, $2)
ON CONFLICT (user_id, coupon_id) DO NOTHING;

-- name: HasUserUsedCoupon :one
SELECT COUNT(1) FROM users_coupons WHERE user_id = $1 AND coupon_id = $2;

-- name: CreateCoupon :one
INSERT INTO coupons (
    code,
    type,
    value,
    quantity,
    is_active,
    expires_at,
    min_order_value,
    owner_type,
    owner_id,
    external_id
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
) RETURNING external_id;

-- name: ListCoupons :many
SELECT
    c.code,
    c.type,
    c.value,
    c.quantity,
    c.is_active,
    c.expires_at,
    c.min_order_value,
    c.owner_type,
    c.external_id,
    c.created_at,
    c.updated_at,
    COALESCE(u.external_id, co.external_id) AS owner_external_id,
    co.name AS owner_name,
    co.cnpj AS owner_cnpj,
    COUNT(*) OVER() AS total_count
FROM coupons c
LEFT JOIN users u 
    ON c.owner_type = 'admin' AND c.owner_id = u.id
LEFT JOIN companies co 
    ON c.owner_type = 'company' AND c.owner_id = co.id
ORDER BY c.created_at DESC
LIMIT $1 OFFSET $2;

-- name: GetCouponByExternalID :one
SELECT
    c.*,
    u.external_id as owner_external_id,
    co.name as owner_name,
    co.cnpj as owner_cnpj
FROM coupons c
JOIN users u ON c.owner_id = u.id
LEFT JOIN companies co ON c.owner_id = co.owner_id
WHERE c.external_id = $1;

-- name: UpdateCouponStatus :exec
UPDATE coupons 
SET is_active = $2, 
    updated_at = NOW() 
WHERE external_id = $1; 