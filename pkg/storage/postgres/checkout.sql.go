// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: checkout.sql

package postgres

import (
	"context"
	"database/sql"
)

const deleteProductsWithStockZero = `-- name: DeleteProductsWithStockZero :exec
DELETE FROM company_products
WHERE company_id = $1 AND stock = 0
`

func (q *Queries) DeleteProductsWithStockZero(ctx context.Context, companyID int32) error {
	_, err := q.db.Exec(ctx, deleteProductsWithStockZero, companyID)
	return err
}

const getCompanyInfoWithProducts = `-- name: GetCompanyInfoWithProducts :one
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.pix_key,
  c.external_id,
  c.is_active,
  c.commission_rate,
  c.cashback_rate,
  c.shipping_fee,
  c.owner_id,
  COALESCE(
    jsonb_agg(
      jsonb_build_object(
        'id', p.id,
        'price', cp.price,
        'discount', cp.discount,
        'stock', cp.stock,
        'name', p.name,
        'ean', p.ean,
        'description', p.description,
        'image', p.image,
        'brand', p.brand,
        'external_id', p.external_id,
        'is_18_plus', p.is_18_plus,
        'categories', COALESCE(
          (SELECT jsonb_agg(DISTINCT jsonb_build_object(
            'name', cat.name,
            'image', cat.image,
            'external_id', cat.external_id
          ))
          FROM products_categories pc_cat
          JOIN categories cat ON cat.id = pc_cat.category_id
          WHERE pc_cat.product_id = p.id),
          '[]'::jsonb
        )
      )
    ) FILTER (WHERE p.id IS NOT NULL),
    '[]'::jsonb
  ) AS products
FROM
  public.companies c
LEFT JOIN public.company_products cp ON c.id = cp.company_id
LEFT JOIN public.products p ON cp.product_id = p.id
WHERE
  c.external_id = $1
  AND c.is_active = TRUE
  AND p.is_reviewed = TRUE
  AND p.is_active = TRUE
GROUP BY
  c.id
`

type GetCompanyInfoWithProductsRow struct {
	ID             int32
	Name           string
	Cnpj           string
	Bio            string
	PixKey         string
	ExternalID     string
	IsActive       bool
	CommissionRate int32
	CashbackRate   int32
	ShippingFee    int32
	OwnerID        sql.NullInt32
	Products       interface{}
}

func (q *Queries) GetCompanyInfoWithProducts(ctx context.Context, externalID string) (GetCompanyInfoWithProductsRow, error) {
	row := q.db.QueryRow(ctx, getCompanyInfoWithProducts, externalID)
	var i GetCompanyInfoWithProductsRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Cnpj,
		&i.Bio,
		&i.PixKey,
		&i.ExternalID,
		&i.IsActive,
		&i.CommissionRate,
		&i.CashbackRate,
		&i.ShippingFee,
		&i.OwnerID,
		&i.Products,
	)
	return i, err
}

const updateCompanyProductsStock = `-- name: UpdateCompanyProductsStock :exec
WITH invoice_data AS (
  SELECT
    i.company_id,
    ip.product_id,
    ip.quantity
  FROM invoices i
  JOIN invoice_products ip ON i.id = ip.invoice_id
  WHERE i.order_id = $1
)
UPDATE company_products cp
SET stock = stock - id.quantity
FROM invoice_data id
WHERE cp.company_id = id.company_id AND cp.product_id = id.product_id
`

func (q *Queries) UpdateCompanyProductsStock(ctx context.Context, orderID string) error {
	_, err := q.db.Exec(ctx, updateCompanyProductsStock, orderID)
	return err
}
