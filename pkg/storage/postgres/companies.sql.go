// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: companies.sql

package postgres

import (
	"context"
	"database/sql"
	"time"

	"github.com/jackc/pgtype"
)

const activateCompany = `-- name: ActivateCompany :one
UPDATE companies
SET is_active = true, updated_at = now()
WHERE external_id = $1
RETURNING id, name, external_id, is_active, owner_id
`

type ActivateCompanyRow struct {
	ID         int32
	Name       string
	ExternalID string
	IsActive   bool
	OwnerID    sql.NullInt32
}

func (q *Queries) ActivateCompany(ctx context.Context, externalID string) (ActivateCompanyRow, error) {
	row := q.db.QueryRow(ctx, activateCompany, externalID)
	var i ActivateCompanyRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ExternalID,
		&i.IsActive,
		&i.OwnerID,
	)
	return i, err
}

const addProductsToCompany = `-- name: AddProductsToCompany :exec
WITH company AS (
  SELECT id AS company_id
  FROM companies
  WHERE companies.external_id = $4
),
product AS (
  SELECT id AS product_id
  FROM products
  WHERE products.external_id = $5
)
INSERT INTO company_products (
  company_id, product_id, price, discount, stock
)
SELECT
  c.company_id,
  p.product_id,
  $1::int,
  $2::int,
  $3::int
FROM company c, product p
ON CONFLICT (company_id, product_id) DO UPDATE
SET 
  price = EXCLUDED.price,
  discount = EXCLUDED.discount,
  stock = EXCLUDED.stock,
  created_at = now()
`

type AddProductsToCompanyParams struct {
	Price             int32
	Discount          int32
	Stock             int32
	CompanyExternalID string
	ProductExternalID string
}

func (q *Queries) AddProductsToCompany(ctx context.Context, arg AddProductsToCompanyParams) error {
	_, err := q.db.Exec(ctx, addProductsToCompany,
		arg.Price,
		arg.Discount,
		arg.Stock,
		arg.CompanyExternalID,
		arg.ProductExternalID,
	)
	return err
}

const countCompaniesByOwnerID = `-- name: CountCompaniesByOwnerID :one
SELECT COUNT(*) AS company_count
FROM companies
WHERE owner_id = $1
`

func (q *Queries) CountCompaniesByOwnerID(ctx context.Context, ownerID sql.NullInt32) (int64, error) {
	row := q.db.QueryRow(ctx, countCompaniesByOwnerID, ownerID)
	var company_count int64
	err := row.Scan(&company_count)
	return company_count, err
}

const createCompany = `-- name: CreateCompany :one
INSERT INTO companies (
  name, cnpj, bio, picture, phone_numbers, pix_key, delivery_modes, shipping_fee, commission_rate, cashback_rate, external_id, is_active, owner_id
) VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING id
`

type CreateCompanyParams struct {
	Name           string
	Cnpj           string
	Bio            string
	Picture        string
	PhoneNumbers   []string
	PixKey         string
	DeliveryModes  []string
	ShippingFee    int32
	CommissionRate int32
	CashbackRate   int32
	ExternalID     string
	IsActive       bool
	OwnerID        sql.NullInt32
}

func (q *Queries) CreateCompany(ctx context.Context, arg CreateCompanyParams) (int32, error) {
	row := q.db.QueryRow(ctx, createCompany,
		arg.Name,
		arg.Cnpj,
		arg.Bio,
		arg.Picture,
		arg.PhoneNumbers,
		arg.PixKey,
		arg.DeliveryModes,
		arg.ShippingFee,
		arg.CommissionRate,
		arg.CashbackRate,
		arg.ExternalID,
		arg.IsActive,
		arg.OwnerID,
	)
	var id int32
	err := row.Scan(&id)
	return id, err
}

const createCompanyAddress = `-- name: CreateCompanyAddress :exec
INSERT INTO company_addresses (
  company_id, name, street, number, complement, neighborhood, city, state, zip_code,
  latitude, longitude, location, is_default, external_id
) VALUES (
  $1, $2, $3, $4,
  $5, $6, $7, $8,
  $9, $10, $11,
  ST_SetSRID(ST_MakePoint($11, $10), 4326),
  $12, $13
)
`

type CreateCompanyAddressParams struct {
	CompanyID    int32
	Name         string
	Street       string
	Number       string
	Complement   sql.NullString
	Neighborhood string
	City         string
	State        string
	ZipCode      string
	Latitude     sql.NullFloat64
	Longitude    sql.NullFloat64
	IsDefault    bool
	ExternalID   string
}

func (q *Queries) CreateCompanyAddress(ctx context.Context, arg CreateCompanyAddressParams) error {
	_, err := q.db.Exec(ctx, createCompanyAddress,
		arg.CompanyID,
		arg.Name,
		arg.Street,
		arg.Number,
		arg.Complement,
		arg.Neighborhood,
		arg.City,
		arg.State,
		arg.ZipCode,
		arg.Latitude,
		arg.Longitude,
		arg.IsDefault,
		arg.ExternalID,
	)
	return err
}

const createWithdrawal = `-- name: CreateWithdrawal :one
INSERT INTO company_withdrawals (
  company_id,
  amount,
  correlation_id,
  destination_pix_key,
  comment,
  end_to_end_id,
  woovi_response
) VALUES (
  $1, $2, $3, $4, $5, $6, $7
) RETURNING id, created_at
`

type CreateWithdrawalParams struct {
	CompanyID         int64
	Amount            int32
	CorrelationID     sql.NullString
	DestinationPixKey sql.NullString
	Comment           sql.NullString
	EndToEndID        sql.NullString
	WooviResponse     pgtype.JSONB
}

type CreateWithdrawalRow struct {
	ID        int64
	CreatedAt time.Time
}

func (q *Queries) CreateWithdrawal(ctx context.Context, arg CreateWithdrawalParams) (CreateWithdrawalRow, error) {
	row := q.db.QueryRow(ctx, createWithdrawal,
		arg.CompanyID,
		arg.Amount,
		arg.CorrelationID,
		arg.DestinationPixKey,
		arg.Comment,
		arg.EndToEndID,
		arg.WooviResponse,
	)
	var i CreateWithdrawalRow
	err := row.Scan(&i.ID, &i.CreatedAt)
	return i, err
}

const getActiveCompanies = `-- name: GetActiveCompanies :many
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,  
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'name', p.name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
      'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      )
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products,
  COUNT(*) OVER() AS total_count
FROM companies c
LEFT JOIN company_addresses ca ON c.id = ca.company_id
INNER JOIN company_products cp ON c.id = cp.company_id
INNER JOIN products p ON cp.product_id = p.id
WHERE c.is_active = true
GROUP BY c.id
LIMIT $1 OFFSET $2
`

type GetActiveCompaniesParams struct {
	Limit  int32
	Offset int32
}

type GetActiveCompaniesRow struct {
	ID               int32
	Name             string
	Cnpj             string
	Bio              string
	Picture          string
	PhoneNumbers     []string
	PixKey           string
	SubscriptionID   sql.NullInt32
	Rating           float64
	ShippingFee      int32
	OwnerID          sql.NullInt32
	AffiliateBalance int32
	DeliveryModes    []string
	ExternalID       string
	IsActive         bool
	CreatedAt        time.Time
	UpdatedAt        time.Time
	Addresses        pgtype.JSON
	Products         pgtype.JSON
	TotalCount       int64
}

func (q *Queries) GetActiveCompanies(ctx context.Context, arg GetActiveCompaniesParams) ([]GetActiveCompaniesRow, error) {
	rows, err := q.db.Query(ctx, getActiveCompanies, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetActiveCompaniesRow
	for rows.Next() {
		var i GetActiveCompaniesRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Cnpj,
			&i.Bio,
			&i.Picture,
			&i.PhoneNumbers,
			&i.PixKey,
			&i.SubscriptionID,
			&i.Rating,
			&i.ShippingFee,
			&i.OwnerID,
			&i.AffiliateBalance,
			&i.DeliveryModes,
			&i.ExternalID,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Addresses,
			&i.Products,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getActiveCompaniesWithLocation = `-- name: GetActiveCompaniesWithLocation :many
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', p.external_id,
      'name', p.name,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'ean', p.ean,
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'is_18_plus', p.is_18_plus,
      'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      ),
      'is_active', p.is_active,
      'is_reviewed', p.is_reviewed,
      'created_at', p.created_at,
      'updated_at', p.updated_at
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products,
  COUNT(*) OVER() AS total_count,
  ROUND(
    (ST_Distance(ca.location::geography, ST_MakePoint($3::float8, $4::float8)::geography) / 1000.0)::numeric, 2
  ) AS distance_km
FROM companies c
LEFT JOIN company_addresses ca ON c.id = ca.company_id AND ca.is_default = true
INNER JOIN company_products cp ON c.id = cp.company_id
INNER JOIN products p ON cp.product_id = p.id
WHERE c.is_active = true
  AND ca.location IS NOT NULL
  AND ST_DWithin(ca.location, ST_MakePoint($3::float8, $4::float8)::geography, $5::int4)
GROUP BY c.id, ca.location
ORDER BY distance_km ASC
LIMIT $1 OFFSET $2
`

type GetActiveCompaniesWithLocationParams struct {
	Limit   int32
	Offset  int32
	Column3 float64
	Column4 float64
	Column5 int32
}

type GetActiveCompaniesWithLocationRow struct {
	ID               int32
	Name             string
	Cnpj             string
	Bio              string
	Picture          string
	PhoneNumbers     []string
	PixKey           string
	SubscriptionID   sql.NullInt32
	Rating           float64
	ShippingFee      int32
	OwnerID          sql.NullInt32
	AffiliateBalance int32
	DeliveryModes    []string
	ExternalID       string
	IsActive         bool
	CreatedAt        time.Time
	UpdatedAt        time.Time
	Addresses        pgtype.JSON
	Products         pgtype.JSON
	TotalCount       int64
	DistanceKm       pgtype.Numeric
}

func (q *Queries) GetActiveCompaniesWithLocation(ctx context.Context, arg GetActiveCompaniesWithLocationParams) ([]GetActiveCompaniesWithLocationRow, error) {
	rows, err := q.db.Query(ctx, getActiveCompaniesWithLocation,
		arg.Limit,
		arg.Offset,
		arg.Column3,
		arg.Column4,
		arg.Column5,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetActiveCompaniesWithLocationRow
	for rows.Next() {
		var i GetActiveCompaniesWithLocationRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Cnpj,
			&i.Bio,
			&i.Picture,
			&i.PhoneNumbers,
			&i.PixKey,
			&i.SubscriptionID,
			&i.Rating,
			&i.ShippingFee,
			&i.OwnerID,
			&i.AffiliateBalance,
			&i.DeliveryModes,
			&i.ExternalID,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Addresses,
			&i.Products,
			&i.TotalCount,
			&i.DistanceKm,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllCompanies = `-- name: GetAllCompanies :many
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'name', p.name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
      'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      )
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products,
  COUNT(*) OVER() AS total_count
FROM companies c
LEFT JOIN company_addresses ca ON c.id = ca.company_id
LEFT JOIN company_products cp ON c.id = cp.company_id
LEFT JOIN products p ON cp.product_id = p.id
GROUP BY c.id
LIMIT $1 OFFSET $2
`

type GetAllCompaniesParams struct {
	Limit  int32
	Offset int32
}

type GetAllCompaniesRow struct {
	ID               int32
	Name             string
	Cnpj             string
	Bio              string
	Picture          string
	PhoneNumbers     []string
	PixKey           string
	SubscriptionID   sql.NullInt32
	Rating           float64
	ShippingFee      int32
	OwnerID          sql.NullInt32
	AffiliateBalance int32
	DeliveryModes    []string
	ExternalID       string
	IsActive         bool
	CreatedAt        time.Time
	UpdatedAt        time.Time
	Addresses        pgtype.JSON
	Products         pgtype.JSON
	TotalCount       int64
}

func (q *Queries) GetAllCompanies(ctx context.Context, arg GetAllCompaniesParams) ([]GetAllCompaniesRow, error) {
	rows, err := q.db.Query(ctx, getAllCompanies, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllCompaniesRow
	for rows.Next() {
		var i GetAllCompaniesRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Cnpj,
			&i.Bio,
			&i.Picture,
			&i.PhoneNumbers,
			&i.PixKey,
			&i.SubscriptionID,
			&i.Rating,
			&i.ShippingFee,
			&i.OwnerID,
			&i.AffiliateBalance,
			&i.DeliveryModes,
			&i.ExternalID,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Addresses,
			&i.Products,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCompaniesByOwnerID = `-- name: GetCompaniesByOwnerID :many
SELECT external_id
FROM companies
WHERE owner_id = $1 AND is_active = true
`

func (q *Queries) GetCompaniesByOwnerID(ctx context.Context, ownerID sql.NullInt32) ([]string, error) {
	rows, err := q.db.Query(ctx, getCompaniesByOwnerID, ownerID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []string
	for rows.Next() {
		var external_id string
		if err := rows.Scan(&external_id); err != nil {
			return nil, err
		}
		items = append(items, external_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCompanyByExternalID = `-- name: GetCompanyByExternalID :one
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'name', p.name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
       'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      )
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products
FROM companies c
LEFT JOIN company_addresses ca ON c.id = ca.company_id
LEFT JOIN company_products cp ON c.id = cp.company_id
LEFT JOIN products p ON cp.product_id = p.id
WHERE c.external_id = $1
GROUP BY c.id
`

type GetCompanyByExternalIDRow struct {
	ID               int32
	Name             string
	Cnpj             string
	Bio              string
	Picture          string
	PhoneNumbers     []string
	PixKey           string
	SubscriptionID   sql.NullInt32
	Rating           float64
	ShippingFee      int32
	OwnerID          sql.NullInt32
	AffiliateBalance int32
	DeliveryModes    []string
	ExternalID       string
	IsActive         bool
	CreatedAt        time.Time
	UpdatedAt        time.Time
	Addresses        pgtype.JSON
	Products         pgtype.JSON
}

func (q *Queries) GetCompanyByExternalID(ctx context.Context, externalID string) (GetCompanyByExternalIDRow, error) {
	row := q.db.QueryRow(ctx, getCompanyByExternalID, externalID)
	var i GetCompanyByExternalIDRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Cnpj,
		&i.Bio,
		&i.Picture,
		&i.PhoneNumbers,
		&i.PixKey,
		&i.SubscriptionID,
		&i.Rating,
		&i.ShippingFee,
		&i.OwnerID,
		&i.AffiliateBalance,
		&i.DeliveryModes,
		&i.ExternalID,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Addresses,
		&i.Products,
	)
	return i, err
}

const getCompanyByID = `-- name: GetCompanyByID :one
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at
FROM
  public.companies c
WHERE
  c.id = $1
`

type GetCompanyByIDRow struct {
	ID               int32
	Name             string
	Cnpj             string
	Bio              string
	Picture          string
	PhoneNumbers     []string
	PixKey           string
	SubscriptionID   sql.NullInt32
	Rating           float64
	ShippingFee      int32
	OwnerID          sql.NullInt32
	AffiliateBalance int32
	DeliveryModes    []string
	ExternalID       string
	IsActive         bool
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

func (q *Queries) GetCompanyByID(ctx context.Context, id int32) (GetCompanyByIDRow, error) {
	row := q.db.QueryRow(ctx, getCompanyByID, id)
	var i GetCompanyByIDRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Cnpj,
		&i.Bio,
		&i.Picture,
		&i.PhoneNumbers,
		&i.PixKey,
		&i.SubscriptionID,
		&i.Rating,
		&i.ShippingFee,
		&i.OwnerID,
		&i.AffiliateBalance,
		&i.DeliveryModes,
		&i.ExternalID,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getCompanyDetailsWithOwner = `-- name: GetCompanyDetailsWithOwner :one
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.commission_rate,
  c.cashback_rate,
  c.created_at,
  c.updated_at,
  u.external_id AS owner_external_id,
  u.name AS owner_name,
  u.email AS owner_email,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'name', p.name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
      'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      )
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products
FROM companies c
LEFT JOIN users u ON c.owner_id = u.id
LEFT JOIN company_addresses ca ON c.id = ca.company_id
LEFT JOIN company_products cp ON c.id = cp.company_id
LEFT JOIN products p ON cp.product_id = p.id
WHERE c.external_id = $1
GROUP BY c.id, u.external_id, u.name, u.email
`

type GetCompanyDetailsWithOwnerRow struct {
	ID               int32
	Name             string
	Cnpj             string
	Bio              string
	Picture          string
	PhoneNumbers     []string
	PixKey           string
	SubscriptionID   sql.NullInt32
	Rating           float64
	ShippingFee      int32
	OwnerID          sql.NullInt32
	AffiliateBalance int32
	DeliveryModes    []string
	ExternalID       string
	IsActive         bool
	CommissionRate   int32
	CashbackRate     int32
	CreatedAt        time.Time
	UpdatedAt        time.Time
	OwnerExternalID  sql.NullString
	OwnerName        sql.NullString
	OwnerEmail       sql.NullString
	Addresses        pgtype.JSON
	Products         pgtype.JSON
}

func (q *Queries) GetCompanyDetailsWithOwner(ctx context.Context, externalID string) (GetCompanyDetailsWithOwnerRow, error) {
	row := q.db.QueryRow(ctx, getCompanyDetailsWithOwner, externalID)
	var i GetCompanyDetailsWithOwnerRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Cnpj,
		&i.Bio,
		&i.Picture,
		&i.PhoneNumbers,
		&i.PixKey,
		&i.SubscriptionID,
		&i.Rating,
		&i.ShippingFee,
		&i.OwnerID,
		&i.AffiliateBalance,
		&i.DeliveryModes,
		&i.ExternalID,
		&i.IsActive,
		&i.CommissionRate,
		&i.CashbackRate,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.OwnerExternalID,
		&i.OwnerName,
		&i.OwnerEmail,
		&i.Addresses,
		&i.Products,
	)
	return i, err
}

const getCompanyProductsByCategory = `-- name: GetCompanyProductsByCategory :one
WITH company_info AS (
  SELECT
    c.id AS company_id,
    c.name AS company_name,
    c.external_id AS company_external_id
  FROM companies c
  WHERE c.external_id = $3
    AND c.is_active = true
),
base_products AS (
  SELECT
    p.id,
    p.name AS product_name,
    p.ean,
    p.description,
    p.image,
    p.brand,
    p.is_reviewed,
    p.is_active,
    p.is_18_plus,
    p.external_id,
    cp.price,
    cp.discount,
    cp.stock,
    cat.name AS category_name,
    cat.image AS category_image,
    cat.external_id AS category_external_id
  FROM company_products cp
  JOIN products p ON p.id = cp.product_id
  JOIN products_categories pc ON pc.product_id = p.id
  JOIN categories cat ON cat.id = pc.category_id
  JOIN company_info ci ON ci.company_id = cp.company_id
  WHERE cat.external_id = $4
    AND p.is_reviewed = true
    AND p.is_active = true
),
total AS (
  SELECT COUNT(*) AS total_count FROM base_products
),
paginated_products AS (
  SELECT id, product_name, ean, description, image, brand, is_reviewed, is_active, is_18_plus, external_id, price, discount, stock, category_name, category_image, category_external_id FROM base_products
  ORDER BY product_name
  LIMIT $1 OFFSET $2
)
SELECT
  (SELECT total_count FROM total),
  (SELECT category_name FROM base_products LIMIT 1),
  (SELECT category_image FROM base_products LIMIT 1),
  (SELECT category_external_id FROM base_products LIMIT 1),
  (SELECT company_name FROM company_info),
  (SELECT company_external_id FROM company_info),
  json_agg(
    jsonb_build_object(
      'name', p.product_name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'categories', jsonb_build_array(
        jsonb_build_object(
          'name', p.category_name,
          'image', p.category_image,
          'external_id', p.category_external_id
        )
      ),
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
      'price', p.price,
      'discount', p.discount,
      'stock', p.stock
    )
  ) AS products
FROM paginated_products p
`

type GetCompanyProductsByCategoryParams struct {
	Limit              int32
	Offset             int32
	CompanyExternalID  string
	CategoryExternalID string
}

type GetCompanyProductsByCategoryRow struct {
	TotalCount         int64
	CategoryName       string
	CategoryImage      string
	CategoryExternalID string
	CompanyName        string
	CompanyExternalID  string
	Products           pgtype.JSON
}

func (q *Queries) GetCompanyProductsByCategory(ctx context.Context, arg GetCompanyProductsByCategoryParams) (GetCompanyProductsByCategoryRow, error) {
	row := q.db.QueryRow(ctx, getCompanyProductsByCategory,
		arg.Limit,
		arg.Offset,
		arg.CompanyExternalID,
		arg.CategoryExternalID,
	)
	var i GetCompanyProductsByCategoryRow
	err := row.Scan(
		&i.TotalCount,
		&i.CategoryName,
		&i.CategoryImage,
		&i.CategoryExternalID,
		&i.CompanyName,
		&i.CompanyExternalID,
		&i.Products,
	)
	return i, err
}

const getWithdrawalHistoryWithPayouts = `-- name: GetWithdrawalHistoryWithPayouts :many
WITH company_info AS (
  SELECT id, external_id, name
  FROM companies
  WHERE external_id = $1
),
period_filter AS (
  SELECT
    CASE
      WHEN $4::text = '1m' THEN NOW() - INTERVAL '1 month'
      WHEN $4::text = '3m' THEN NOW() - INTERVAL '3 months'
      WHEN $4::text = '6m' THEN NOW() - INTERVAL '6 months'
      WHEN $4::text = '1y' THEN NOW() - INTERVAL '1 year'
      ELSE NOW() - INTERVAL '30 days' -- Default to 30 days
    END AS start_date
),
filtered_withdrawals AS (
  SELECT
    cw.id,
    cw.amount,
    cw.status,
    cw.correlation_id,
    cw.destination_pix_key,
    cw.comment,
    cw.end_to_end_id,
    cw.woovi_response,
    cw.finished_at,
    cw.created_at,
    ci.external_id as company_external_id,
    ci.name as company_name,
    json_agg(
      DISTINCT jsonb_build_object(
        'payout_id', ip.id,
        'invoice_id', ip.invoice_id,
        'order_id', i.order_id,
        'amount', ip.amount,
        'finalized_at', ip.finalized_at
      )
    ) FILTER (WHERE ip.id IS NOT NULL) as used_payouts
  FROM company_withdrawals cw
  JOIN company_info ci ON cw.company_id = ci.id
  LEFT JOIN withdrawal_payouts wp ON wp.withdrawal_id = cw.id
  LEFT JOIN invoice_payouts ip ON ip.id = wp.payout_id
  LEFT JOIN invoices i ON i.id = ip.invoice_id
  CROSS JOIN period_filter pf
  WHERE cw.created_at >= pf.start_date
  GROUP BY cw.id, ci.external_id, ci.name
  ORDER BY cw.created_at DESC
),
total_info AS (
  SELECT
    COUNT(*) as total_count,
    COALESCE(SUM(amount), 0) as total_amount
  FROM filtered_withdrawals
)
SELECT
  fw.id,
  fw.amount,
  fw.status,
  fw.correlation_id,
  fw.destination_pix_key,
  fw.comment,
  fw.end_to_end_id,
  fw.woovi_response,
  fw.finished_at,
  fw.created_at,
  fw.company_external_id,
  fw.company_name,
  fw.used_payouts,
  ti.total_count,
  ti.total_amount
FROM filtered_withdrawals fw
CROSS JOIN total_info ti
LIMIT $2 OFFSET $3
`

type GetWithdrawalHistoryWithPayoutsParams struct {
	ExternalID string
	Limit      int32
	Offset     int32
	Column4    string
}

type GetWithdrawalHistoryWithPayoutsRow struct {
	ID                int64
	Amount            int32
	Status            string
	CorrelationID     sql.NullString
	DestinationPixKey sql.NullString
	Comment           sql.NullString
	EndToEndID        sql.NullString
	WooviResponse     pgtype.JSONB
	FinishedAt        sql.NullTime
	CreatedAt         time.Time
	CompanyExternalID string
	CompanyName       string
	UsedPayouts       pgtype.JSON
	TotalCount        int64
	TotalAmount       interface{}
}

func (q *Queries) GetWithdrawalHistoryWithPayouts(ctx context.Context, arg GetWithdrawalHistoryWithPayoutsParams) ([]GetWithdrawalHistoryWithPayoutsRow, error) {
	rows, err := q.db.Query(ctx, getWithdrawalHistoryWithPayouts,
		arg.ExternalID,
		arg.Limit,
		arg.Offset,
		arg.Column4,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetWithdrawalHistoryWithPayoutsRow
	for rows.Next() {
		var i GetWithdrawalHistoryWithPayoutsRow
		if err := rows.Scan(
			&i.ID,
			&i.Amount,
			&i.Status,
			&i.CorrelationID,
			&i.DestinationPixKey,
			&i.Comment,
			&i.EndToEndID,
			&i.WooviResponse,
			&i.FinishedAt,
			&i.CreatedAt,
			&i.CompanyExternalID,
			&i.CompanyName,
			&i.UsedPayouts,
			&i.TotalCount,
			&i.TotalAmount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getWithdrawalsByCompanyAndPeriod = `-- name: GetWithdrawalsByCompanyAndPeriod :many
WITH company_info AS (
  SELECT id, external_id, name
  FROM companies
  WHERE external_id = $1
),
period_filter AS (
  SELECT
    CASE
      WHEN $4::text = '1m' THEN NOW() - INTERVAL '1 month'
      WHEN $4::text = '3m' THEN NOW() - INTERVAL '3 months'
      WHEN $4::text = '6m' THEN NOW() - INTERVAL '6 months'
      WHEN $4::text = '1y' THEN NOW() - INTERVAL '1 year'
      ELSE NOW() - INTERVAL '30 days' -- Default to 30 days
    END AS start_date
),
filtered_withdrawals AS (
  SELECT
    cw.id,
    cw.amount,
    cw.status,
    cw.correlation_id,
    cw.destination_pix_key,
    cw.comment,
    cw.end_to_end_id,
    cw.woovi_response,
    cw.created_at,
    ci.external_id as company_external_id,
    ci.name as company_name
  FROM company_withdrawals cw
  JOIN company_info ci ON cw.company_id = ci.id
  CROSS JOIN period_filter pf
  WHERE cw.created_at >= pf.start_date
  ORDER BY cw.created_at DESC
),
total_info AS (
  SELECT
    COUNT(*) as total_count,
    COALESCE(SUM(amount), 0) as total_amount
  FROM filtered_withdrawals
)
SELECT
  fw.id,
  fw.amount,
  fw.status,
  fw.correlation_id,
  fw.destination_pix_key,
  fw.comment,
  fw.end_to_end_id,
  fw.woovi_response,
  fw.created_at,
  fw.company_external_id,
  fw.company_name,
  ti.total_count,
  ti.total_amount
FROM filtered_withdrawals fw
CROSS JOIN total_info ti
LIMIT $2 OFFSET $3
`

type GetWithdrawalsByCompanyAndPeriodParams struct {
	ExternalID string
	Limit      int32
	Offset     int32
	Column4    string
}

type GetWithdrawalsByCompanyAndPeriodRow struct {
	ID                int64
	Amount            int32
	Status            string
	CorrelationID     sql.NullString
	DestinationPixKey sql.NullString
	Comment           sql.NullString
	EndToEndID        sql.NullString
	WooviResponse     pgtype.JSONB
	CreatedAt         time.Time
	CompanyExternalID string
	CompanyName       string
	TotalCount        int64
	TotalAmount       interface{}
}

func (q *Queries) GetWithdrawalsByCompanyAndPeriod(ctx context.Context, arg GetWithdrawalsByCompanyAndPeriodParams) ([]GetWithdrawalsByCompanyAndPeriodRow, error) {
	rows, err := q.db.Query(ctx, getWithdrawalsByCompanyAndPeriod,
		arg.ExternalID,
		arg.Limit,
		arg.Offset,
		arg.Column4,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetWithdrawalsByCompanyAndPeriodRow
	for rows.Next() {
		var i GetWithdrawalsByCompanyAndPeriodRow
		if err := rows.Scan(
			&i.ID,
			&i.Amount,
			&i.Status,
			&i.CorrelationID,
			&i.DestinationPixKey,
			&i.Comment,
			&i.EndToEndID,
			&i.WooviResponse,
			&i.CreatedAt,
			&i.CompanyExternalID,
			&i.CompanyName,
			&i.TotalCount,
			&i.TotalAmount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const removeProductsFromCompany = `-- name: RemoveProductsFromCompany :exec
WITH company AS (
  SELECT id AS company_id
  FROM companies
  WHERE companies.external_id = $1
),
product AS (
  SELECT id AS product_id
  FROM products
  WHERE products.external_id = $2
)
DELETE FROM company_products
WHERE company_id = (SELECT company_id FROM company)
  AND product_id = (SELECT product_id FROM product)
`

type RemoveProductsFromCompanyParams struct {
	CompanyExternalID string
	ProductExternalID string
}

func (q *Queries) RemoveProductsFromCompany(ctx context.Context, arg RemoveProductsFromCompanyParams) error {
	_, err := q.db.Exec(ctx, removeProductsFromCompany, arg.CompanyExternalID, arg.ProductExternalID)
	return err
}

const searchCompanies = `-- name: SearchCompanies :many
SELECT
  c.external_id,
  c.name,
  c.cnpj,
  c.phone_numbers,
  c.created_at,
  c.updated_at,
  COUNT(*) OVER() AS total_count
FROM companies c
WHERE (
  c.cnpj ILIKE '%' || $3::text || '%' OR
  c.name ILIKE '%' || $3::text || '%'
) AND c.is_active = true
ORDER BY c.created_at DESC
LIMIT $1 OFFSET $2
`

type SearchCompaniesParams struct {
	Limit       int32
	Offset      int32
	SearchQuery string
}

type SearchCompaniesRow struct {
	ExternalID   string
	Name         string
	Cnpj         string
	PhoneNumbers []string
	CreatedAt    time.Time
	UpdatedAt    time.Time
	TotalCount   int64
}

func (q *Queries) SearchCompanies(ctx context.Context, arg SearchCompaniesParams) ([]SearchCompaniesRow, error) {
	rows, err := q.db.Query(ctx, searchCompanies, arg.Limit, arg.Offset, arg.SearchQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SearchCompaniesRow
	for rows.Next() {
		var i SearchCompaniesRow
		if err := rows.Scan(
			&i.ExternalID,
			&i.Name,
			&i.Cnpj,
			&i.PhoneNumbers,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateCompanyAddress = `-- name: UpdateCompanyAddress :exec
UPDATE company_addresses
SET name = $1,
  street = $2,
  number = $3,
  complement = $4,
  neighborhood = $5,
  city = $6,
  state = $7,
  zip_code = $8,
  latitude = $9,
  longitude = $10,
  location = ST_SetSRID(ST_MakePoint($10, $9), 4326)
WHERE external_id = $11
`

type UpdateCompanyAddressParams struct {
	Name         string
	Street       string
	Number       string
	Complement   sql.NullString
	Neighborhood string
	City         string
	State        string
	ZipCode      string
	Latitude     sql.NullFloat64
	Longitude    sql.NullFloat64
	ExternalID   string
}

func (q *Queries) UpdateCompanyAddress(ctx context.Context, arg UpdateCompanyAddressParams) error {
	_, err := q.db.Exec(ctx, updateCompanyAddress,
		arg.Name,
		arg.Street,
		arg.Number,
		arg.Complement,
		arg.Neighborhood,
		arg.City,
		arg.State,
		arg.ZipCode,
		arg.Latitude,
		arg.Longitude,
		arg.ExternalID,
	)
	return err
}

const updateCompanyCashbackRate = `-- name: UpdateCompanyCashbackRate :exec
UPDATE companies
SET cashback_rate = $1
WHERE external_id = $2
`

type UpdateCompanyCashbackRateParams struct {
	CashbackRate int32
	ExternalID   string
}

func (q *Queries) UpdateCompanyCashbackRate(ctx context.Context, arg UpdateCompanyCashbackRateParams) error {
	_, err := q.db.Exec(ctx, updateCompanyCashbackRate, arg.CashbackRate, arg.ExternalID)
	return err
}

const updateCompanyComissionRate = `-- name: UpdateCompanyComissionRate :exec
UPDATE companies
SET commission_rate = $1
WHERE external_id = $2
`

type UpdateCompanyComissionRateParams struct {
	CommissionRate int32
	ExternalID     string
}

func (q *Queries) UpdateCompanyComissionRate(ctx context.Context, arg UpdateCompanyComissionRateParams) error {
	_, err := q.db.Exec(ctx, updateCompanyComissionRate, arg.CommissionRate, arg.ExternalID)
	return err
}

const updateCompanyData = `-- name: UpdateCompanyData :exec
UPDATE companies
SET bio = $1,
  phone_numbers = $2,
  delivery_modes = $3,
  shipping_fee = $4
WHERE external_id = $5
`

type UpdateCompanyDataParams struct {
	Bio           string
	PhoneNumbers  []string
	DeliveryModes []string
	ShippingFee   int32
	ExternalID    string
}

func (q *Queries) UpdateCompanyData(ctx context.Context, arg UpdateCompanyDataParams) error {
	_, err := q.db.Exec(ctx, updateCompanyData,
		arg.Bio,
		arg.PhoneNumbers,
		arg.DeliveryModes,
		arg.ShippingFee,
		arg.ExternalID,
	)
	return err
}

const updateCompanyImage = `-- name: UpdateCompanyImage :exec
UPDATE companies
SET picture = $1
WHERE external_id = $2
`

type UpdateCompanyImageParams struct {
	Picture    string
	ExternalID string
}

func (q *Queries) UpdateCompanyImage(ctx context.Context, arg UpdateCompanyImageParams) error {
	_, err := q.db.Exec(ctx, updateCompanyImage, arg.Picture, arg.ExternalID)
	return err
}

const updateCompanyOwner = `-- name: UpdateCompanyOwner :one
UPDATE companies
SET owner_id = $2, updated_at = now()
WHERE external_id = $1
RETURNING id, name, external_id, owner_id
`

type UpdateCompanyOwnerParams struct {
	ExternalID string
	OwnerID    sql.NullInt32
}

type UpdateCompanyOwnerRow struct {
	ID         int32
	Name       string
	ExternalID string
	OwnerID    sql.NullInt32
}

func (q *Queries) UpdateCompanyOwner(ctx context.Context, arg UpdateCompanyOwnerParams) (UpdateCompanyOwnerRow, error) {
	row := q.db.QueryRow(ctx, updateCompanyOwner, arg.ExternalID, arg.OwnerID)
	var i UpdateCompanyOwnerRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ExternalID,
		&i.OwnerID,
	)
	return i, err
}

const updateCompanyStatus = `-- name: UpdateCompanyStatus :one
UPDATE companies
SET is_active = $2, updated_at = now()
WHERE external_id = $1
RETURNING id, name, external_id, is_active, owner_id
`

type UpdateCompanyStatusParams struct {
	ExternalID string
	IsActive   bool
}

type UpdateCompanyStatusRow struct {
	ID         int32
	Name       string
	ExternalID string
	IsActive   bool
	OwnerID    sql.NullInt32
}

func (q *Queries) UpdateCompanyStatus(ctx context.Context, arg UpdateCompanyStatusParams) (UpdateCompanyStatusRow, error) {
	row := q.db.QueryRow(ctx, updateCompanyStatus, arg.ExternalID, arg.IsActive)
	var i UpdateCompanyStatusRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ExternalID,
		&i.IsActive,
		&i.OwnerID,
	)
	return i, err
}

const updateWithdrawalStatusByCorrelationID = `-- name: UpdateWithdrawalStatusByCorrelationID :execrows
UPDATE company_withdrawals
SET status = $1, finished_at = NOW()
WHERE correlation_id = $2
`

type UpdateWithdrawalStatusByCorrelationIDParams struct {
	Status        string
	CorrelationID sql.NullString
}

func (q *Queries) UpdateWithdrawalStatusByCorrelationID(ctx context.Context, arg UpdateWithdrawalStatusByCorrelationIDParams) (int64, error) {
	result, err := q.db.Exec(ctx, updateWithdrawalStatusByCorrelationID, arg.Status, arg.CorrelationID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const validateWithdrawalEligibility = `-- name: ValidateWithdrawalEligibility :one
WITH company_info AS (
  SELECT id, external_id, name
  FROM companies
  WHERE external_id = $1
)
SELECT
  ci.id as company_id,
  ci.external_id,
  ci.name,
  COALESCE(SUM(ip.amount), 0) as available_balance,
  CASE
    WHEN COALESCE(SUM(ip.amount), 0) >= $2 THEN true
    ELSE false
  END as is_eligible,
  $2 as minimum_threshold
FROM company_info ci
LEFT JOIN invoice_payouts ip ON ip.company_id = ci.id
  AND ip.status = 'available'
  AND ip.available_after <= NOW()
GROUP BY ci.id, ci.external_id, ci.name
`

type ValidateWithdrawalEligibilityParams struct {
	ExternalID string
	Amount     int32
}

type ValidateWithdrawalEligibilityRow struct {
	CompanyID        int32
	ExternalID       string
	Name             string
	AvailableBalance interface{}
	IsEligible       bool
	MinimumThreshold interface{}
}

func (q *Queries) ValidateWithdrawalEligibility(ctx context.Context, arg ValidateWithdrawalEligibilityParams) (ValidateWithdrawalEligibilityRow, error) {
	row := q.db.QueryRow(ctx, validateWithdrawalEligibility, arg.ExternalID, arg.Amount)
	var i ValidateWithdrawalEligibilityRow
	err := row.Scan(
		&i.CompanyID,
		&i.ExternalID,
		&i.Name,
		&i.AvailableBalance,
		&i.IsEligible,
		&i.MinimumThreshold,
	)
	return i, err
}
