// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: sessions.sql

package postgres

import (
	"context"
	"time"
)

const checkIfExistsActiveSession = `-- name: CheckIfExistsActiveSession :one
SELECT id, is_active FROM sessions WHERE user_id = $1 AND is_active = true
`

type CheckIfExistsActiveSessionRow struct {
	ID       int32
	IsActive bool
}

func (q *Queries) CheckIfExistsActiveSession(ctx context.Context, userID int32) (CheckIfExistsActiveSessionRow, error) {
	row := q.db.QueryRow(ctx, checkIfExistsActiveSession, userID)
	var i CheckIfExistsActiveSessionRow
	err := row.Scan(&i.ID, &i.IsActive)
	return i, err
}

const checkIfExistsActiveSessionByAppType = `-- name: CheckIfExistsActiveSessionByAppType :one
SELECT id, is_active, application_type FROM sessions
WHERE user_id = $1 AND application_type = $2 AND is_active = true
`

type CheckIfExistsActiveSessionByAppTypeParams struct {
	UserID          int32
	ApplicationType string
}

type CheckIfExistsActiveSessionByAppTypeRow struct {
	ID              int32
	IsActive        bool
	ApplicationType string
}

func (q *Queries) CheckIfExistsActiveSessionByAppType(ctx context.Context, arg CheckIfExistsActiveSessionByAppTypeParams) (CheckIfExistsActiveSessionByAppTypeRow, error) {
	row := q.db.QueryRow(ctx, checkIfExistsActiveSessionByAppType, arg.UserID, arg.ApplicationType)
	var i CheckIfExistsActiveSessionByAppTypeRow
	err := row.Scan(&i.ID, &i.IsActive, &i.ApplicationType)
	return i, err
}

const createSession = `-- name: CreateSession :exec
INSERT INTO sessions (user_id, access_token, refresh_token, device, ip, application_type)
  VALUES ($1, $2, $3, $4, $5, $6)
`

type CreateSessionParams struct {
	UserID          int32
	AccessToken     string
	RefreshToken    string
	Device          string
	Ip              string
	ApplicationType string
}

func (q *Queries) CreateSession(ctx context.Context, arg CreateSessionParams) error {
	_, err := q.db.Exec(ctx, createSession,
		arg.UserID,
		arg.AccessToken,
		arg.RefreshToken,
		arg.Device,
		arg.Ip,
		arg.ApplicationType,
	)
	return err
}

const getActiveSessionWithAppType = `-- name: GetActiveSessionWithAppType :one
SELECT id, is_active, application_type FROM sessions WHERE user_id = $1 AND is_active = true
`

type GetActiveSessionWithAppTypeRow struct {
	ID              int32
	IsActive        bool
	ApplicationType string
}

func (q *Queries) GetActiveSessionWithAppType(ctx context.Context, userID int32) (GetActiveSessionWithAppTypeRow, error) {
	row := q.db.QueryRow(ctx, getActiveSessionWithAppType, userID)
	var i GetActiveSessionWithAppTypeRow
	err := row.Scan(&i.ID, &i.IsActive, &i.ApplicationType)
	return i, err
}

const getActiveSessionsByUser = `-- name: GetActiveSessionsByUser :many
SELECT id, user_id, application_type, device, ip, created_at
FROM sessions
WHERE user_id = $1 AND is_active = true
`

type GetActiveSessionsByUserRow struct {
	ID              int32
	UserID          int32
	ApplicationType string
	Device          string
	Ip              string
	CreatedAt       time.Time
}

func (q *Queries) GetActiveSessionsByUser(ctx context.Context, userID int32) ([]GetActiveSessionsByUserRow, error) {
	rows, err := q.db.Query(ctx, getActiveSessionsByUser, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetActiveSessionsByUserRow
	for rows.Next() {
		var i GetActiveSessionsByUserRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.ApplicationType,
			&i.Device,
			&i.Ip,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSessionByAccessToken = `-- name: GetSessionByAccessToken :one
SELECT id, user_id, is_active, application_type FROM sessions WHERE access_token = $1 AND is_active = true
`

type GetSessionByAccessTokenRow struct {
	ID              int32
	UserID          int32
	IsActive        bool
	ApplicationType string
}

func (q *Queries) GetSessionByAccessToken(ctx context.Context, accessToken string) (GetSessionByAccessTokenRow, error) {
	row := q.db.QueryRow(ctx, getSessionByAccessToken, accessToken)
	var i GetSessionByAccessTokenRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.IsActive,
		&i.ApplicationType,
	)
	return i, err
}

const invalidateSession = `-- name: InvalidateSession :exec
UPDATE sessions SET is_active = false WHERE id = $1
`

func (q *Queries) InvalidateSession(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, invalidateSession, id)
	return err
}

const invalidateSessionsByUserAndAppType = `-- name: InvalidateSessionsByUserAndAppType :exec
UPDATE sessions SET is_active = false
WHERE user_id = $1 AND application_type = $2 AND is_active = true
`

type InvalidateSessionsByUserAndAppTypeParams struct {
	UserID          int32
	ApplicationType string
}

func (q *Queries) InvalidateSessionsByUserAndAppType(ctx context.Context, arg InvalidateSessionsByUserAndAppTypeParams) error {
	_, err := q.db.Exec(ctx, invalidateSessionsByUserAndAppType, arg.UserID, arg.ApplicationType)
	return err
}
