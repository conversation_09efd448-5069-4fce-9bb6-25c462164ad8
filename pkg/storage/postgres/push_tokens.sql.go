// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: push_tokens.sql

package postgres

import (
	"context"
	"database/sql"
	"time"
)

const deleteExpiredPushTokens = `-- name: DeleteExpiredPushTokens :exec
DELETE FROM push_tokens
WHERE expires_at < $1
`

func (q *Queries) DeleteExpiredPushTokens(ctx context.Context, expiresAt time.Time) error {
	_, err := q.db.Exec(ctx, deleteExpiredPushTokens, expiresAt)
	return err
}

const deleteUserDeviceToken = `-- name: DeleteUserDeviceToken :exec
DELETE FROM push_tokens 
WHERE user_id = $1 AND device_id = $2
`

type DeleteUserDeviceTokenParams struct {
	UserID   int32
	DeviceID string
}

func (q *Queries) DeleteUserDeviceToken(ctx context.Context, arg DeleteUserDeviceTokenParams) error {
	_, err := q.db.Exec(ctx, deleteUserDeviceToken, arg.UserID, arg.DeviceID)
	return err
}

const getActiveFCMTokensByUser = `-- name: GetActiveFCMTokensByUser :many
SELECT id, user_id, push_token, device_id, platform, fcm_token, app_version, application_type, device_model, os_version, expires_at, last_used_at, created_at, updated_at
FROM push_tokens
WHERE user_id = $1 AND expires_at > NOW()
`

type GetActiveFCMTokensByUserRow struct {
	ID              int32
	UserID          int32
	PushToken       string
	DeviceID        string
	Platform        string
	FcmToken        string
	AppVersion      string
	ApplicationType string
	DeviceModel     sql.NullString
	OsVersion       sql.NullString
	ExpiresAt       time.Time
	LastUsedAt      sql.NullTime
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func (q *Queries) GetActiveFCMTokensByUser(ctx context.Context, userID int32) ([]GetActiveFCMTokensByUserRow, error) {
	rows, err := q.db.Query(ctx, getActiveFCMTokensByUser, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetActiveFCMTokensByUserRow
	for rows.Next() {
		var i GetActiveFCMTokensByUserRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.PushToken,
			&i.DeviceID,
			&i.Platform,
			&i.FcmToken,
			&i.AppVersion,
			&i.ApplicationType,
			&i.DeviceModel,
			&i.OsVersion,
			&i.ExpiresAt,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getActiveFCMTokensByUserAndAppType = `-- name: GetActiveFCMTokensByUserAndAppType :many
SELECT id, user_id, push_token, device_id, platform, fcm_token, app_version, application_type, device_model, os_version, expires_at, last_used_at, created_at, updated_at
FROM push_tokens
WHERE user_id = $1 AND application_type = $2 AND expires_at > NOW()
`

type GetActiveFCMTokensByUserAndAppTypeParams struct {
	UserID          int32
	ApplicationType string
}

type GetActiveFCMTokensByUserAndAppTypeRow struct {
	ID              int32
	UserID          int32
	PushToken       string
	DeviceID        string
	Platform        string
	FcmToken        string
	AppVersion      string
	ApplicationType string
	DeviceModel     sql.NullString
	OsVersion       sql.NullString
	ExpiresAt       time.Time
	LastUsedAt      sql.NullTime
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func (q *Queries) GetActiveFCMTokensByUserAndAppType(ctx context.Context, arg GetActiveFCMTokensByUserAndAppTypeParams) ([]GetActiveFCMTokensByUserAndAppTypeRow, error) {
	rows, err := q.db.Query(ctx, getActiveFCMTokensByUserAndAppType, arg.UserID, arg.ApplicationType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetActiveFCMTokensByUserAndAppTypeRow
	for rows.Next() {
		var i GetActiveFCMTokensByUserAndAppTypeRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.PushToken,
			&i.DeviceID,
			&i.Platform,
			&i.FcmToken,
			&i.AppVersion,
			&i.ApplicationType,
			&i.DeviceModel,
			&i.OsVersion,
			&i.ExpiresAt,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getActivePushTokensByUserID = `-- name: GetActivePushTokensByUserID :many
SELECT
    id,
    user_id,
    push_token,
    device_id,
    platform,
    fcm_token,
    app_version,
    device_model,
    os_version,
    expires_at,
    last_used_at,
    created_at,
    updated_at
FROM push_tokens
WHERE user_id = $1 AND expires_at > NOW()
ORDER BY created_at DESC
`

type GetActivePushTokensByUserIDRow struct {
	ID          int32
	UserID      int32
	PushToken   string
	DeviceID    string
	Platform    string
	FcmToken    string
	AppVersion  string
	DeviceModel sql.NullString
	OsVersion   sql.NullString
	ExpiresAt   time.Time
	LastUsedAt  sql.NullTime
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

func (q *Queries) GetActivePushTokensByUserID(ctx context.Context, userID int32) ([]GetActivePushTokensByUserIDRow, error) {
	rows, err := q.db.Query(ctx, getActivePushTokensByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetActivePushTokensByUserIDRow
	for rows.Next() {
		var i GetActivePushTokensByUserIDRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.PushToken,
			&i.DeviceID,
			&i.Platform,
			&i.FcmToken,
			&i.AppVersion,
			&i.DeviceModel,
			&i.OsVersion,
			&i.ExpiresAt,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const refreshFCMTokenExpiration = `-- name: RefreshFCMTokenExpiration :exec
UPDATE push_tokens
SET expires_at = $1, updated_at = NOW()
WHERE user_id = $2 AND device_id = $3
`

type RefreshFCMTokenExpirationParams struct {
	ExpiresAt time.Time
	UserID    int32
	DeviceID  string
}

func (q *Queries) RefreshFCMTokenExpiration(ctx context.Context, arg RefreshFCMTokenExpirationParams) error {
	_, err := q.db.Exec(ctx, refreshFCMTokenExpiration, arg.ExpiresAt, arg.UserID, arg.DeviceID)
	return err
}

const upsertFCMToken = `-- name: UpsertFCMToken :exec
INSERT INTO push_tokens (
    user_id,
    push_token,
    device_id,
    platform,
    fcm_token,
    app_version,
    application_type,
    device_model,
    os_version,
    expires_at,
    created_at,
    updated_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
) ON CONFLICT (user_id, device_id, application_type)
DO UPDATE SET
    fcm_token = EXCLUDED.fcm_token,
    platform = EXCLUDED.platform,
    app_version = EXCLUDED.app_version,
    device_model = EXCLUDED.device_model,
    os_version = EXCLUDED.os_version,
    expires_at = EXCLUDED.expires_at,
    updated_at = EXCLUDED.updated_at
`

type UpsertFCMTokenParams struct {
	UserID          int32
	PushToken       string
	DeviceID        string
	Platform        string
	FcmToken        string
	AppVersion      string
	ApplicationType string
	DeviceModel     sql.NullString
	OsVersion       sql.NullString
	ExpiresAt       time.Time
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func (q *Queries) UpsertFCMToken(ctx context.Context, arg UpsertFCMTokenParams) error {
	_, err := q.db.Exec(ctx, upsertFCMToken,
		arg.UserID,
		arg.PushToken,
		arg.DeviceID,
		arg.Platform,
		arg.FcmToken,
		arg.AppVersion,
		arg.ApplicationType,
		arg.DeviceModel,
		arg.OsVersion,
		arg.ExpiresAt,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	return err
}
