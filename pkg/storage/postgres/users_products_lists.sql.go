// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: users_products_lists.sql

package postgres

import (
	"context"
	"time"

	"github.com/jackc/pgtype"
)

const checkIfUserProductsListBelongsToUser = `-- name: CheckIfUserProductsListBelongsToUser :one
SELECT
  EXISTS (
    SELECT 1
    FROM user_products_lists
    WHERE user_id = $1
      AND external_id = $2
  ) AS exists
`

type CheckIfUserProductsListBelongsToUserParams struct {
	UserID     int32
	ExternalID string
}

func (q *Queries) CheckIfUserProductsListBelongsToUser(ctx context.Context, arg CheckIfUserProductsListBelongsToUserParams) (bool, error) {
	row := q.db.QueryRow(ctx, checkIfUserProductsListBelongsToUser, arg.UserID, arg.ExternalID)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const deleteMissingUserProductsListItems = `-- name: DeleteMissingUserProductsListItems :exec
WITH product_ids AS (
  SELECT id
  FROM products
  WHERE external_id = ANY($2::text[]) -- $external_ids será o nome do parâmetro
)
DELETE FROM user_products_lists_items
WHERE list_id = $1
  AND product_id NOT IN (SELECT id FROM product_ids)
`

type DeleteMissingUserProductsListItemsParams struct {
	ListID      int32
	ExternalIds []string
}

func (q *Queries) DeleteMissingUserProductsListItems(ctx context.Context, arg DeleteMissingUserProductsListItemsParams) error {
	_, err := q.db.Exec(ctx, deleteMissingUserProductsListItems, arg.ListID, arg.ExternalIds)
	return err
}

const deleteUserProductsList = `-- name: DeleteUserProductsList :exec
DELETE FROM user_products_lists
WHERE external_id = $1
  AND user_id = $2
`

type DeleteUserProductsListParams struct {
	ExternalID string
	UserID     int32
}

func (q *Queries) DeleteUserProductsList(ctx context.Context, arg DeleteUserProductsListParams) error {
	_, err := q.db.Exec(ctx, deleteUserProductsList, arg.ExternalID, arg.UserID)
	return err
}

const getUserProductsListByExternalId = `-- name: GetUserProductsListByExternalId :one
SELECT
  user_products_lists.id AS id,
  user_products_lists.name AS name,
  user_products_lists.icon_url AS icon_url,
  user_products_lists.external_id AS external_id,
  user_products_lists.is_public AS is_public,
  user_products_lists.created_at AS created_at,
  user_products_lists.updated_at AS updated_at,
  json_agg(
    jsonb_build_object(
      'quantity', user_products_lists_items.quantity,
      'name', Products.name,
      'ean', Products.ean,
      'description', Products.description,
      'image', Products.image,
      'brand', Products.brand,
      'external_id', Products.external_id,
      'categories', (
        SELECT json_agg(jsonb_build_object('external_id', c.external_id, 'name', c.name, 'image', c.image))
        FROM products_categories pc
        JOIN categories c ON c.id = pc.category_id
        WHERE pc.product_id = Products.id
      )
    )
  ) AS products
FROM
  user_products_lists
LEFT JOIN user_products_lists_items ON user_products_lists.id = user_products_lists_items.list_id
LEFT JOIN products AS Products ON user_products_lists_items.product_id = Products.id
WHERE
  (user_products_lists.user_id = $1)
  AND (user_products_lists.external_id = $2)
  AND (Products.is_reviewed = TRUE)
  AND (Products.is_active = TRUE)
GROUP BY
  user_products_lists.id
ORDER BY
  user_products_lists.updated_at DESC
LIMIT 1
`

type GetUserProductsListByExternalIdParams struct {
	UserID     int32
	ExternalID string
}

type GetUserProductsListByExternalIdRow struct {
	ID         int32
	Name       string
	IconUrl    string
	ExternalID string
	IsPublic   bool
	CreatedAt  time.Time
	UpdatedAt  time.Time
	Products   pgtype.JSON
}

func (q *Queries) GetUserProductsListByExternalId(ctx context.Context, arg GetUserProductsListByExternalIdParams) (GetUserProductsListByExternalIdRow, error) {
	row := q.db.QueryRow(ctx, getUserProductsListByExternalId, arg.UserID, arg.ExternalID)
	var i GetUserProductsListByExternalIdRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.IconUrl,
		&i.ExternalID,
		&i.IsPublic,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Products,
	)
	return i, err
}

const getUserProductsLists = `-- name: GetUserProductsLists :many
SELECT
  COUNT(*) OVER() AS total_count,
  user_products_lists.id AS id,
  user_products_lists.name AS name,
  user_products_lists.icon_url AS icon_url,
  user_products_lists.external_id AS external_id,
  user_products_lists.is_public AS is_public,
  user_products_lists.created_at AS created_at,
  user_products_lists.updated_at AS updated_at,
  json_agg(
    jsonb_build_object(
      'quantity', user_products_lists_items.quantity,
      'name', Products.name,
      'ean', Products.ean,
      'description', Products.description,
      'image', Products.image,
      'brand', Products.brand,
      'external_id', Products.external_id,
      'categories', (
        SELECT json_agg(jsonb_build_object('external_id', c.external_id, 'name', c.name, 'image', c.image))
        FROM products_categories pc
        JOIN categories c ON c.id = pc.category_id
        WHERE pc.product_id = Products.id
      )
    )
  ) AS products
FROM
  user_products_lists
LEFT JOIN user_products_lists_items ON user_products_lists.id = user_products_lists_items.list_id
LEFT JOIN products AS Products ON user_products_lists_items.product_id = Products.id
WHERE
  (user_products_lists.user_id = $1)
  AND (Products.is_reviewed = TRUE)
  AND (Products.is_active = TRUE)
GROUP BY
  user_products_lists.id
ORDER BY
  user_products_lists.updated_at DESC
LIMIT $2 OFFSET $3
`

type GetUserProductsListsParams struct {
	UserID int32
	Limit  int32
	Offset int32
}

type GetUserProductsListsRow struct {
	TotalCount int64
	ID         int32
	Name       string
	IconUrl    string
	ExternalID string
	IsPublic   bool
	CreatedAt  time.Time
	UpdatedAt  time.Time
	Products   pgtype.JSON
}

func (q *Queries) GetUserProductsLists(ctx context.Context, arg GetUserProductsListsParams) ([]GetUserProductsListsRow, error) {
	rows, err := q.db.Query(ctx, getUserProductsLists, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetUserProductsListsRow
	for rows.Next() {
		var i GetUserProductsListsRow
		if err := rows.Scan(
			&i.TotalCount,
			&i.ID,
			&i.Name,
			&i.IconUrl,
			&i.ExternalID,
			&i.IsPublic,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Products,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const upsertProductsList = `-- name: UpsertProductsList :one
WITH upsert_list AS (
  INSERT INTO user_products_lists (user_id, name, icon_url, is_public, external_id)
  VALUES ($1, $2, $3, $4, $5)
  ON CONFLICT (user_id, name) DO UPDATE
    SET updated_at = now()
  RETURNING id,external_id,
            (xmax = 0) AS is_insert  -- true se foi INSERT, false se foi UPDATE
)
SELECT id,external_id,
       CASE WHEN is_insert THEN 'create' ELSE 'update' END AS action
FROM upsert_list
`

type UpsertProductsListParams struct {
	UserID     int32
	Name       string
	IconUrl    string
	IsPublic   bool
	ExternalID string
}

type UpsertProductsListRow struct {
	ID         int32
	ExternalID string
	Action     string
}

func (q *Queries) UpsertProductsList(ctx context.Context, arg UpsertProductsListParams) (UpsertProductsListRow, error) {
	row := q.db.QueryRow(ctx, upsertProductsList,
		arg.UserID,
		arg.Name,
		arg.IconUrl,
		arg.IsPublic,
		arg.ExternalID,
	)
	var i UpsertProductsListRow
	err := row.Scan(&i.ID, &i.ExternalID, &i.Action)
	return i, err
}

const upsertUserProductsListItem = `-- name: UpsertUserProductsListItem :exec
WITH product AS (
  SELECT id
  FROM products
  WHERE external_id = $1  -- external_id do produto
)
INSERT INTO user_products_lists_items (list_id, product_id, quantity)
SELECT $2, id, $3  -- list_id, product_id (obtido do JOIN), quantity
FROM product
ON CONFLICT (list_id, product_id) DO UPDATE
  SET quantity = EXCLUDED.quantity
`

type UpsertUserProductsListItemParams struct {
	ExternalID string
	ListID     int32
	Quantity   int32
}

func (q *Queries) UpsertUserProductsListItem(ctx context.Context, arg UpsertUserProductsListItemParams) error {
	_, err := q.db.Exec(ctx, upsertUserProductsListItem, arg.ExternalID, arg.ListID, arg.Quantity)
	return err
}
