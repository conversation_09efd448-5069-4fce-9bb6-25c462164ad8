import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

//To allow this host, add "f4ad1777-e765-439d-be82-cc4e0afc4ed7.lovableproject.com" to `server.allowedHosts` in vite.config.js.

export default defineConfig(({ mode }) => ({
  base: '/',
  server: {
    allowedHosts: [
      "f4ad1777-e765-439d-be82-cc4e0afc4ed7.lovableproject.com",
      "localhost",
    ],
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html'),
      },
    },
  },
  // Ensure service worker and config files are copied to dist
  publicDir: 'public',

}));
