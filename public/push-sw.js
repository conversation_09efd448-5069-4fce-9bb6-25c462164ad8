// Secure Push Notification Service Worker
// Handles background push notifications using Web Push API (no Firebase)

console.log('🔔 [Push-SW] Secure push service worker loaded');

// Handle push events
self.addEventListener('push', (event) => {
  console.log('🔔 [Push-SW] Push event received:', event);

  if (!event.data) {
    console.log('🔔 [Push-SW] Push event has no data');
    return;
  }

  try {
    const payload = event.data.json();
    console.log('🔔 [Push-SW] Push payload:', payload);

    // Handle order status updates
    if (payload.type === 'order_status_update') {
      const isNewOrder = !payload.old_status || payload.old_status === '';
      const title = isNewOrder ? '🆕 Novo Pedido' : '📦 Status Atualizado';
      const orderId = payload.order_id ? payload.order_id.slice(-8) : 'N/A';
      const body = `Pedido ${orderId} - ${payload.status_description || 'Status atualizado'}`;

      const notificationOptions = {
        title: title,
        body: body,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: `order-${payload.order_id}`, // Replace previous notifications for same order
        requireInteraction: false,
        silent: false,
        data: {
          type: 'order_status_update',
          order_id: payload.order_id,
          company_id: payload.company_id,
          new_status: payload.new_status,
          old_status: payload.old_status,
          url: '/partner/orders',
          timestamp: Date.now(),
        },
        actions: [
          {
            action: 'view',
            title: 'Ver Pedidos',
          },
          {
            action: 'dismiss',
            title: 'Dispensar',
          }
        ]
      };

      event.waitUntil(
        self.registration.showNotification(title, notificationOptions)
      );

      // Send message to main thread if app is open
      event.waitUntil(
        self.clients.matchAll({ includeUncontrolled: true }).then((clients) => {
          clients.forEach((client) => {
            client.postMessage({
              type: 'PUSH_NOTIFICATION',
              payload: payload,
            });
          });
        })
      );

      return;
    }

    // Handle other notification types
    const title = payload.title || 'IzyMercado';
    const body = payload.body || 'Nova notificação recebida';

    event.waitUntil(
      self.registration.showNotification(title, {
        body: body,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        data: payload,
      })
    );

  } catch (error) {
    console.error('🔔 [Push-SW] Error handling push event:', error);
    
    // Show generic notification on error
    event.waitUntil(
      self.registration.showNotification('IzyMercado', {
        body: 'Nova notificação recebida',
        icon: '/favicon.ico',
        badge: '/favicon.ico',
      })
    );
  }
});

// Handle notification click events
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 [Push-SW] Notification clicked:', event);

  event.notification.close();

  const data = event.notification.data || {};
  const action = event.action;

  // Handle action buttons
  if (action === 'dismiss') {
    return; // Just close the notification
  }

  // Determine URL to open
  let urlToOpen = '/';
  
  if (data.type === 'order_status_update') {
    urlToOpen = data.url || '/partner/orders';
  }

  // Focus existing window or open new one
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window open
      for (const client of clientList) {
        if (client.url.includes(self.location.origin)) {
          // Focus existing window and navigate to the URL
          client.focus();
          client.postMessage({
            type: 'NOTIFICATION_CLICKED',
            data: data,
            url: urlToOpen,
          });
          return;
        }
      }

      // Open new window if none exists
      return clients.openWindow(self.location.origin + urlToOpen);
    })
  );
});

// Handle notification close events
self.addEventListener('notificationclose', (event) => {
  console.log('🔔 [Push-SW] Notification closed:', event);
  
  const data = event.notification.data || {};
  
  if (data.type === 'order_status_update') {
    console.log('🔔 [Push-SW] Order notification dismissed:', data.order_id);
  }
});

// Service worker installation
self.addEventListener('install', (event) => {
  console.log('🔔 [Push-SW] Service worker installing');
  self.skipWaiting();
});

// Service worker activation
self.addEventListener('activate', (event) => {
  console.log('🔔 [Push-SW] Service worker activating');
  event.waitUntil(self.clients.claim());
});

// Handle messages from the main thread
self.addEventListener('message', (event) => {
  console.log('🔔 [Push-SW] Message received from main thread:', event.data);

  const { type, data } = event.data || {};

  switch (type) {
    case 'CLEAR_NOTIFICATIONS':
      // Clear all notifications
      self.registration.getNotifications().then((notifications) => {
        notifications.forEach((notification) => {
          notification.close();
        });
        console.log('🔔 [Push-SW] All notifications cleared');
      });
      break;
      
    case 'UPDATE_BADGE':
      // Update badge count (if supported)
      if ('setAppBadge' in navigator) {
        navigator.setAppBadge(data.count || 0);
      }
      break;
      
    default:
      console.log('🔔 [Push-SW] Unknown message type:', type);
  }
});
