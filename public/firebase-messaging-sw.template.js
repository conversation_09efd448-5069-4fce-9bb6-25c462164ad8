// Firebase messaging service worker for background notifications
// Version: 1.2.0 - Graceful configuration handling
try {
  importScripts('https://www.gstatic.com/firebasejs/10.13.0/firebase-app-compat.js');
  importScripts('https://www.gstatic.com/firebasejs/10.13.0/firebase-messaging-compat.js');
} catch (error) {
  console.error('[firebase-messaging-sw.js] Failed to load Firebase scripts:', error);
}

// Firebase configuration - injected during build
// These values will be replaced by the build process from environment variables
const firebaseConfig = {
  apiKey: "__VITE_FIREBASE_API_KEY__",
  authDomain: "__VITE_FIREBASE_AUTH_DOMAIN__",
  projectId: "__VITE_FIREBASE_PROJECT_ID__",
  storageBucket: "__VITE_FIREBASE_STORAGE_BUCKET__",
  messagingSenderId: "__VITE_FIREBASE_MESSAGING_SENDER_ID__",
  appId: "__VITE_FIREBASE_APP_ID__"
};

// Check if Firebase configuration is valid
const isConfigValid = () => {
  return Object.values(firebaseConfig).every(value =>
    value && typeof value === 'string' && !value.startsWith('PLACEHOLDER_') && !value.startsWith('__VITE_')
  );
};

// Initialize Firebase in service worker only if configuration is valid
let messaging = null;

if (typeof firebase !== 'undefined' && isConfigValid()) {
  try {
    firebase.initializeApp(firebaseConfig);
    messaging = firebase.messaging();
    console.log('[firebase-messaging-sw.js] Firebase messaging initialized successfully');
  } catch (error) {
    console.error('[firebase-messaging-sw.js] Failed to initialize Firebase:', error);
  }
} else {
  console.warn('[firebase-messaging-sw.js] Firebase not configured or scripts not loaded. Push notifications disabled.');
}

// Handle background messages (only if messaging is available)
if (messaging) {
  messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);
  console.log('[firebase-messaging-sw.js] Payload data structure:', {
    hasNotification: !!payload.notification,
    hasData: !!payload.data,
    dataKeys: payload.data ? Object.keys(payload.data) : [],
    dataType: payload.data?.type,
    fullData: payload.data
  });

  // Extract notification data
  const notificationTitle = payload.notification?.title || 'Izy Mercado';
  const notificationOptions = {
    body: payload.notification?.body || 'Nova notificação',
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    tag: payload.data?.type || 'general',
    data: payload.data,
    actions: [
      {
        action: 'view',
        title: 'Ver Detalhes'
      },
      {
        action: 'dismiss',
        title: 'Dispensar'
      }
    ],
    requireInteraction: true,
    silent: false
  };

  // Show notification
  self.registration.showNotification(notificationTitle, notificationOptions);

  // Store background notification for later processing when tab becomes active
  try {
    const backgroundNotifications = JSON.parse(localStorage.getItem('fcm_background_notifications') || '[]');
    const notificationData = {
      id: `bg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      title: notificationTitle,
      body: notificationOptions.body,
      type: payload.data?.type || 'general',
      data: payload.data || {},
      timestamp: new Date().toISOString(),
      processed: false
    };

    backgroundNotifications.push(notificationData);

    // Keep only last 50 notifications to prevent localStorage bloat
    if (backgroundNotifications.length > 50) {
      backgroundNotifications.splice(0, backgroundNotifications.length - 50);
    }

    localStorage.setItem('fcm_background_notifications', JSON.stringify(backgroundNotifications));
    console.log('[firebase-messaging-sw.js] Background notification stored:', notificationData);

    // Notify all clients about the stored background notification
    self.clients.matchAll({ includeUncontrolled: true }).then((clients) => {
      clients.forEach((client) => {
        client.postMessage({
          type: 'BACKGROUND_NOTIFICATION_STORED',
          notification: notificationData
        });
      });
    });

  } catch (error) {
    console.error('[firebase-messaging-sw.js] Failed to store background notification:', error);
  }
  });
} else {
  console.log('[firebase-messaging-sw.js] Messaging not available - background message handling disabled');
}

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received:', event);

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Handle notification click - navigate to appropriate page
  const notificationData = event.notification.data;
  let targetUrl = '/';

  // Determine target URL based on notification type
  if (notificationData) {
    switch (notificationData.type) {
      case 'payment_confirmed':
        targetUrl = '/partner/orders';
        break;
      case 'new_order':
      case 'new_order_received':
        targetUrl = '/partner/orders';
        break;
      case 'order_status_update':
        targetUrl = `/partner/orders?order=${notificationData.order_id}`;
        break;
      default:
        targetUrl = '/partner/orders';
    }
  }

  // Open or focus the app window
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url.includes(targetUrl) && 'focus' in client) {
          return client.focus();
        }
      }

      // If no window is open, open a new one
      if (clients.openWindow) {
        return clients.openWindow(targetUrl);
      }
    })
  );
});

// Handle push event (additional handling if needed)
self.addEventListener('push', (event) => {
  console.log('[firebase-messaging-sw.js] Push event received:', event);

  // Firebase messaging handles this automatically, but we can add custom logic here if needed
  // Don't return true - let Firebase handle the push event
});
