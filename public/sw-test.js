// Simple test service worker to verify registration works
console.log('🔔 [SW-Test] Test service worker loaded');

self.addEventListener('install', (event) => {
  console.log('🔔 [SW-Test] Service worker installing');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('🔔 [SW-Test] Service worker activating');
  event.waitUntil(self.clients.claim());
});

self.addEventListener('message', (event) => {
  console.log('🔔 [SW-Test] Message received:', event.data);
  
  if (event.data && event.data.type === 'TEST') {
    event.ports[0].postMessage({
      type: 'TEST_RESPONSE',
      message: 'Service worker is working!'
    });
  }
});
