// Firebase Service Worker Test Script
// This script helps verify that the Firebase service worker is accessible
// Usage: Add <script src="/firebase-sw-test.js"></script> to your HTML or run in console

console.log('🔧 Firebase Service Worker Test Script Loaded');

// Test if service worker is accessible
async function testServiceWorker() {
  console.log('🔧 Testing Firebase service worker accessibility...');
  
  try {
    // Test if the service worker file is accessible
    const response = await fetch('/firebase-messaging-sw.js');
    
    if (response.ok) {
      const contentType = response.headers.get('content-type');
      console.log('✅ Service worker file is accessible');
      console.log(`   Content-Type: ${contentType}`);
      
      if (contentType && contentType.includes('javascript')) {
        console.log('✅ Service worker has correct MIME type');
      } else {
        console.error('❌ Service worker has incorrect MIME type');
        console.error('   Expected: application/javascript or text/javascript');
        console.error(`   Received: ${contentType}`);
      }
      
      // Check if content looks like JavaScript
      const content = await response.text();
      if (content.includes('firebase') && content.includes('messaging')) {
        console.log('✅ Service worker content appears valid');
      } else {
        console.error('❌ Service worker content appears invalid');
        console.error('   Content preview:', content.substring(0, 200) + '...');
      }
      
    } else {
      console.error('❌ Service worker file is not accessible');
      console.error(`   Status: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ Failed to test service worker:', error);
  }
}

// Test service worker registration
async function testServiceWorkerRegistration() {
  console.log('🔧 Testing service worker registration...');
  
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
      console.log('✅ Service worker registered successfully:', registration);
      return registration;
    } catch (error) {
      console.error('❌ Service worker registration failed:', error);
      return null;
    }
  } else {
    console.error('❌ Service workers not supported in this browser');
    return null;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🔧 Running Firebase Service Worker Tests...');
  console.log('=====================================');
  
  await testServiceWorker();
  console.log('');
  await testServiceWorkerRegistration();
  
  console.log('');
  console.log('🔧 Test completed. Check results above.');
  console.log('=====================================');
}

// Export functions for manual testing
if (typeof window !== 'undefined') {
  window.testFirebaseServiceWorker = runAllTests;
  window.testFirebaseSW = testServiceWorker;
  window.testFirebaseSWRegistration = testServiceWorkerRegistration;
  
  console.log('🔧 Firebase SW test functions available:');
  console.log('   - testFirebaseServiceWorker() - Run all tests');
  console.log('   - testFirebaseSW() - Test file accessibility');
  console.log('   - testFirebaseSWRegistration() - Test registration');
}
