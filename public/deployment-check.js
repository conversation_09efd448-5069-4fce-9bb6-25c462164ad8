// Deployment Verification Script for Firebase Service Worker
// Run this in production to diagnose service worker issues
// Usage: Add to HTML or run in browser console

console.log('🔧 Starting Firebase Service Worker Deployment Check...');
console.log('================================================');

async function checkServiceWorkerFile() {
  console.log('\n1. 📁 Checking service worker file accessibility...');
  
  try {
    const response = await fetch('/firebase-messaging-sw.js', {
      method: 'GET',
      cache: 'no-cache'
    });
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   URL: ${response.url}`);
    
    const contentType = response.headers.get('content-type');
    console.log(`   Content-Type: ${contentType}`);
    
    if (response.ok) {
      const content = await response.text();
      console.log(`   Content length: ${content.length} characters`);
      
      // Check if it's actually JavaScript
      if (content.includes('firebase') && content.includes('messaging')) {
        console.log('   ✅ Service worker content appears valid');
        
        // Check for configuration
        if (content.includes('PLACEHOLDER_') || content.includes('__VITE_')) {
          console.log('   ⚠️  Service worker contains placeholder values');
          console.log('   💡 Set Firebase environment variables in Cloudflare Pages');
        } else {
          console.log('   ✅ Service worker appears properly configured');
        }
      } else {
        console.log('   ❌ Service worker content appears invalid');
        console.log('   📄 Content preview:', content.substring(0, 200) + '...');
        
        if (content.includes('<html>') || content.includes('<!DOCTYPE')) {
          console.log('   🚨 Server is returning HTML instead of JavaScript!');
          console.log('   💡 This usually means the file is not found (404 page)');
        }
      }
    } else {
      console.log('   ❌ Service worker file not accessible');
      if (response.status === 404) {
        console.log('   💡 File not found - check deployment includes firebase-messaging-sw.js');
      }
    }
    
    return response.ok;
  } catch (error) {
    console.log('   ❌ Error fetching service worker:', error);
    return false;
  }
}

async function checkServiceWorkerRegistration() {
  console.log('\n2. 🔧 Testing service worker registration...');
  
  if (!('serviceWorker' in navigator)) {
    console.log('   ❌ Service workers not supported in this browser');
    return false;
  }
  
  try {
    // Unregister any existing service worker first
    const existingRegistrations = await navigator.serviceWorker.getRegistrations();
    for (const registration of existingRegistrations) {
      if (registration.scope.includes('firebase-cloud-messaging-push-scope')) {
        console.log('   🧹 Unregistering existing Firebase service worker...');
        await registration.unregister();
      }
    }
    
    // Try to register the service worker
    const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
      scope: '/'
    });
    
    console.log('   ✅ Service worker registered successfully');
    console.log(`   Scope: ${registration.scope}`);
    console.log(`   State: ${registration.installing ? 'installing' : registration.waiting ? 'waiting' : registration.active ? 'active' : 'unknown'}`);
    
    // Wait for it to be ready
    await navigator.serviceWorker.ready;
    console.log('   ✅ Service worker is ready');
    
    return true;
  } catch (error) {
    console.log('   ❌ Service worker registration failed:', error);
    
    if (error.message.includes('MIME type')) {
      console.log('   💡 MIME type error - server returning wrong content type');
      console.log('   💡 Check _headers file is deployed and configured correctly');
    } else if (error.message.includes('script evaluation failed')) {
      console.log('   💡 JavaScript syntax error in service worker');
      console.log('   💡 Check service worker content for syntax issues');
    }
    
    return false;
  }
}

async function checkFirebaseConfiguration() {
  console.log('\n3. 🔥 Checking Firebase configuration...');
  
  // Check if Firebase is loaded in main app
  if (typeof window !== 'undefined' && window.firebase) {
    console.log('   ✅ Firebase SDK loaded in main application');
  } else {
    console.log('   ⚠️  Firebase SDK not detected in main application');
  }
  
  // Check environment variables (they should be compiled into the app)
  const envVars = [
    'VITE_FIREBASE_API_KEY',
    'VITE_FIREBASE_AUTH_DOMAIN',
    'VITE_FIREBASE_PROJECT_ID',
    'VITE_FIREBASE_MESSAGING_SENDER_ID',
    'VITE_FIREBASE_APP_ID',
    'VITE_FIREBASE_VAPID_KEY'
  ];
  
  console.log('   Environment variables status:');
  envVars.forEach(varName => {
    // We can't actually check env vars in production, but we can check if the app seems configured
    console.log(`   - ${varName}: [compiled into app]`);
  });
  
  console.log('   💡 If Firebase is not working, check Cloudflare Pages environment variables');
}

async function checkBrowserSupport() {
  console.log('\n4. 🌐 Checking browser support...');
  
  const checks = [
    { name: 'Service Workers', supported: 'serviceWorker' in navigator },
    { name: 'Push Messaging', supported: 'PushManager' in window },
    { name: 'Notifications', supported: 'Notification' in window },
    { name: 'Fetch API', supported: 'fetch' in window },
  ];
  
  checks.forEach(check => {
    console.log(`   ${check.supported ? '✅' : '❌'} ${check.name}: ${check.supported ? 'Supported' : 'Not supported'}`);
  });
  
  if ('Notification' in window) {
    console.log(`   📢 Notification permission: ${Notification.permission}`);
  }
}

async function runFullDiagnostic() {
  console.log('🔧 Running complete Firebase Service Worker diagnostic...');
  console.log('================================================');
  
  const fileOk = await checkServiceWorkerFile();
  const registrationOk = await checkServiceWorkerRegistration();
  await checkFirebaseConfiguration();
  await checkBrowserSupport();
  
  console.log('\n📊 DIAGNOSTIC SUMMARY');
  console.log('====================');
  console.log(`Service Worker File: ${fileOk ? '✅ OK' : '❌ FAILED'}`);
  console.log(`Service Worker Registration: ${registrationOk ? '✅ OK' : '❌ FAILED'}`);
  
  if (fileOk && registrationOk) {
    console.log('🎉 Service worker appears to be working correctly!');
    console.log('💡 If push notifications still don\'t work, check Firebase configuration.');
  } else {
    console.log('🚨 Service worker issues detected. See details above.');
  }
  
  console.log('\n📋 Next steps if issues persist:');
  console.log('1. Check Cloudflare Pages deployment includes firebase-messaging-sw.js');
  console.log('2. Verify _headers and _redirects files are deployed');
  console.log('3. Clear Cloudflare cache and redeploy');
  console.log('4. Check Firebase environment variables in Cloudflare Pages');
}

// Auto-run diagnostic
if (typeof window !== 'undefined') {
  // Export functions for manual use
  window.checkFirebaseServiceWorker = runFullDiagnostic;
  window.checkSWFile = checkServiceWorkerFile;
  window.checkSWRegistration = checkServiceWorkerRegistration;
  
  console.log('🔧 Diagnostic functions available:');
  console.log('   - checkFirebaseServiceWorker() - Run full diagnostic');
  console.log('   - checkSWFile() - Check file accessibility');
  console.log('   - checkSWRegistration() - Test registration');
  
  // Run diagnostic after a short delay
  setTimeout(runFullDiagnostic, 1000);
}
