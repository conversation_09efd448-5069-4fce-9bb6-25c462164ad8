# Cloudflare Pages Headers Configuration
# This file ensures proper MIME types and caching for service workers

# Service Worker files - must be served with correct MIME type
/firebase-messaging-sw.js
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: no-cache, no-store, must-revalidate
  Service-Worker-Allowed: /
  X-Content-Type-Options: nosniff

# All JavaScript files
/*.js
  Content-Type: application/javascript; charset=utf-8
  X-Content-Type-Options: nosniff

# CSS files
/*.css
  Content-Type: text/css; charset=utf-8

# JSON files
/*.json
  Content-Type: application/json; charset=utf-8

# Web App Manifest
/manifest.json
  Content-Type: application/manifest+json; charset=utf-8

# HTML files
/*.html
  Content-Type: text/html; charset=utf-8

# Security headers for the entire site
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
