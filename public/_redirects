# Cloudflare Pages Redirects Configuration
# This file handles SPA routing and ensures service worker is accessible

# Service worker files must be served from root with exact path
/firebase-messaging-sw.js /firebase-messaging-sw.js 200!
/firebase-sw-test.js /firebase-sw-test.js 200!
/sw-test.js /sw-test.js 200!

# Static assets should be served directly
/assets/* /assets/:splat 200!
/*.js /:splat 200!
/*.css /:splat 200!
/*.json /:splat 200!
/*.png /:splat 200!
/*.ico /:splat 200!
/*.svg /:splat 200!

# API routes should not be redirected (if you have any)
/api/* /api/:splat 200

# All other routes should serve the SPA
/* /index.html 200
