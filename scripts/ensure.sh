#! /bin/bash

echo "- Checking air..."
if ! command -v air &> /dev/null
then
    echo "MISSING DEPENDENCY: air is not installed"
    go install github.com/air-verse/air@latest
    air -v
fi

echo "- Checking swag..."
swag_version=$(swag --version 2>&- | grep -oE v[0-9]\+.[0-9]\+.[0-9]\+ || echo "v0.0.0")
min_swag_version="v1.8.9"
if [[ $(echo -e "$min_swag_version\n$swag_version" | sort -V | head -1) != $min_swag_version ]];
then
    go install -tags 'swag' github.com/swaggo/swag/cmd/swag@$min_swag_version
fi
