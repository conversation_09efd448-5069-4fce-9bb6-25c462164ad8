#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { config } from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
const projectRoot = path.resolve(__dirname, '..');
config({ path: path.join(projectRoot, '.env') });

/**
 * Build script to replace Firebase configuration placeholders in service worker
 * This ensures sensitive configuration is not hardcoded in version control
 */

const swTemplatePath = path.join(projectRoot, 'public', 'firebase-messaging-sw.template.js');

// Check if this is development mode
const isDev = process.argv.includes('--dev');

// For production, we need to create the service worker in both public and dist
// This ensures it's available during build and gets copied to the final output
const swOutputPaths = isDev
  ? [path.join(projectRoot, 'public', 'firebase-messaging-sw.js')]  // Dev: only public
  : [
      path.join(projectRoot, 'public', 'firebase-messaging-sw.js'),  // Prod: public (for Vite to copy)
      path.join(projectRoot, 'dist', 'firebase-messaging-sw.js')     // Prod: dist (direct output)
    ];

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

// Validate that all required environment variables are present
const missingVars = Object.entries(firebaseConfig)
  .filter(([key, value]) => !value)
  .map(([key]) => `VITE_FIREBASE_${key.replace(/([A-Z])/g, '_$1').toUpperCase()}`);

if (missingVars.length > 0) {
  console.warn('⚠️  Missing Firebase environment variables:');
  missingVars.forEach(varName => console.warn(`   - ${varName}`));
  console.warn('\nFirebase service worker will be built with placeholder values.');
  console.warn('Push notifications will be disabled until proper configuration is provided.');

  // Use placeholder values instead of exiting
  Object.keys(firebaseConfig).forEach(key => {
    if (!firebaseConfig[key]) {
      firebaseConfig[key] = `PLACEHOLDER_${key.toUpperCase()}`;
    }
  });
}

try {
  // Read the service worker template
  let swContent = fs.readFileSync(swTemplatePath, 'utf8');
  
  // Replace placeholders with actual values
  Object.entries(firebaseConfig).forEach(([key, value]) => {
    const placeholder = `__VITE_FIREBASE_${key.replace(/([A-Z])/g, '_$1').toUpperCase()}__`;
    swContent = swContent.replace(new RegExp(placeholder, 'g'), value);
  });
  
  // Write the processed service worker to all output paths
  swOutputPaths.forEach(outputPath => {
    // Ensure directory exists
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write the service worker file
    fs.writeFileSync(outputPath, swContent);
  });

  const hasValidConfig = !Object.values(firebaseConfig).some(value =>
    value.startsWith('PLACEHOLDER_') || value.startsWith('__VITE_')
  );

  console.log(`✅ Firebase service worker built successfully (${isDev ? 'development' : 'production'})`);
  console.log(`   Template: ${path.relative(projectRoot, swTemplatePath)}`);
  swOutputPaths.forEach(outputPath => {
    console.log(`   Output: ${path.relative(projectRoot, outputPath)}`);
  });

  if (hasValidConfig) {
    console.log(`   Status: Firebase configured - push notifications enabled`);
  } else {
    console.log(`   Status: Firebase not configured - push notifications disabled`);
    console.log(`   Note: Set Firebase environment variables to enable push notifications`);
  }

} catch (error) {
  console.error('❌ Error building Firebase service worker:', error.message);
  // Don't exit with error in production builds - just warn
  if (isDev) {
    process.exit(1);
  } else {
    console.warn('⚠️  Service worker build failed, but continuing with production build...');
  }
}
