package main

import (
	"fmt"
)

type Product struct {
	Price      int    `json:"price"`
	Discount   int    `json:"discount"`
	Quantity   int    `json:"quantity"`
	ExternalID string `json:"external_id"`
}

func main() {
	// Example checkout products
	products := []Product{
		{Price: 1750, Discount: 5, Quantity: 1, ExternalID: "01JSF54A7XXP82H87M64C26AC8"},
		{Price: 2000, Discount: 10, Quantity: 2, ExternalID: "01JSFHYVFQEF8R335QNFDDJ6ZZ"},
	}

	// Invoice values after processing
	expectedAmount := 5263
	expectedDiscount := 487

	var totalAmount int
	var totalDiscount int

	for _, p := range products {
		fullPrice := p.Price * p.Quantity
		discountValue := (p.Price * p.Discount / 100) * p.Quantity
		totalAmount += fullPrice - discountValue
		totalDiscount += discountValue
	}

	fmt.Printf("Amount ok: %v\n", totalAmount == expectedAmount)
	fmt.Printf("Discount ok: %v\n", totalDiscount == expectedDiscount)
}
