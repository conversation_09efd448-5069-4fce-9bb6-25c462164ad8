#!/bin/bash

# Install psql if not already installed
if ! command -v psql &> /dev/null; then
    echo "psql not found. Installing PostgreSQL client..."
    apt-get update && apt-get install -y postgresql-client
fi

# Extract the database name from DATABASE_URL
DB_NAME=$(echo $DATABASE_URL | awk -F'/' '{print $NF}' | awk -F'?' '{print $1}')

# Construct the connection string for the default 'postgres' database
DEFAULT_DB_URL=$(echo $DATABASE_URL | sed "s|/${DB_NAME}?|/postgres?|")

# Check if the database exists, and create it if it doesn't
echo "Checking if database '$DB_NAME' exists..."
psql "${DEFAULT_DB_URL}" -tc "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME'" | grep -q 1
if [ $? -ne 0 ]; then
    echo "Database '$DB_NAME' does not exist. Creating it..."
    psql "${DEFAULT_DB_URL}" -c "CREATE DATABASE $DB_NAME;"
else
    echo "Database '$DB_NAME' already exists."
fi

# Import dump only if APP_ENV is development
if [ "$APP_ENV" = "development" ]; then
    # Get the last dump from the bkp folder
    DUMP_PATH=$(find ./bkp -type f -name "db_prod.sql" -printf "%T@ %p\n" | sort -nr | head -n1 | cut -d' ' -f2-)
    echo "Importing dump from $DUMP_PATH..."
    psql "$DATABASE_URL" < "$DUMP_PATH"
fi

# Run migrations
echo "Running migrations..."
mkdir -p ./build/bin && \
curl -s -L https://github.com/golang-migrate/migrate/releases/download/v4.14.1/migrate.linux-amd64.tar.gz | tar xz -C ./build/bin/ && \
mv ./build/bin/migrate.linux-amd64 ./build/bin/migrate && \
./build/bin/migrate -path ./pkg/storage/postgres/migrations -database "${DATABASE_URL}" -verbose up

# Sync sequence to max(id)
echo "Synchronizing products_id_seq with max(id)..."
psql "${DATABASE_URL}" -c "SELECT setval('products_id_seq', COALESCE((SELECT MAX(id) FROM products), 0));"
psql "${DATABASE_URL}" -c "SELECT setval('categories_id_seq', COALESCE((SELECT MAX(id) FROM categories), 0));"
