FROM metabase/metabase:latest

# Set environment variables for Metabase configuration
ENV MB_DB_TYPE=h2
ENV MB_DB_FILE=/metabase-data/metabase.db
ENV JAVA_OPTS="-Xmx1g -Djava.awt.headless=true"
ENV MB_JETTY_HOST=0.0.0.0
ENV MB_JETTY_PORT=3000

# Create volume mount point with proper permissions
RUN mkdir -p /metabase-data && chmod 777 /metabase-data

VOLUME ["/metabase-data"]

EXPOSE 3000

# Start Metabase directly
CMD ["java", "-jar", "/app/metabase.jar"]
