services:
  db:
    container_name: database
    build:
      context: .
      dockerfile: Dockerfile-db
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: izymercado
    ports:
      - 5432:5432
    networks:
      - keploy-network

  api:
    container_name: izymercado-api
    build:
      context: ./
      target: development
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
    env_file:
      - .env
    volumes:
      - ./:/go/src/main
    ports:
      - 8080:8080
    depends_on:
      - db
    networks:
      - keploy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      retries: 5
      start_period: 10s
      timeout: 10s

  metabase:
    container_name: metabase
    image: metabase/metabase:latest
    ports:
      - "3000:3000"
    environment:
      - MB_DB_TYPE=postgres
      - MB_DB_DBNAME=izymercado
      - MB_DB_PORT=5432
      - MB_DB_USER=postgres
      - MB_DB_PASS=postgres
      - MB_DB_HOST=db
    networks:
      - keploy-network
    depends_on:
      - db
    restart: unless-stopped
    volumes:
      - metabase-data:/metabase-data

networks:
  keploy-network:
    driver: bridge

volumes:
  metabase-data:

