-- Query performance monitoring:
EXPLAIN (ANALYZE, BUFFERS) 
SELECT ... FROM GetRecommendedPriceComparison($1);

-- Index usage verification:
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read
FROM pg_stat_user_indexes 
WHERE indexname LIKE 'idx_%price_comparison%';

-- Query timing in production:
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
WHERE query LIKE '%GetRecommendedPriceComparison%';