start: ensure swagger migrate
	bash scripts/start.sh

start-prod: migrate
	/izymercado-api

clean:
	docker rm -f izymercado-api

ensure:
	bash -x scripts/ensure.sh

dev: ensure
	DOCKER_BUILDKIT=1 docker-compose up -d --build
	make log

log:
	docker logs -f izymercado-api

swagger: ensure
	bash -x scripts/swagger.sh

migrate:
	bash scripts/migrate.sh

test:
	go test ./... -v -race

remove:
	docker rm -f $$(docker ps -aq)

# Metabase VM deployment commands
.PHONY: deploy-metabase
deploy-metabase:
	@echo "🚀 Deploying Metabase VM..."
	@chmod +x metabase/deploy.sh metabase/manage-vm.sh
	@./metabase/deploy.sh

.PHONY: metabase-start
metabase-start:
	@chmod +x metabase/manage-vm.sh
	@./metabase/manage-vm.sh start

.PHONY: metabase-stop
metabase-stop:
	@chmod +x metabase/manage-vm.sh
	@./metabase/manage-vm.sh stop

.PHONY: metabase-restart
metabase-restart:
	@chmod +x metabase/manage-vm.sh
	@./metabase/manage-vm.sh restart

.PHONY: metabase-status
metabase-status:
	@chmod +x metabase/manage-vm.sh
	@./metabase/manage-vm.sh status

.PHONY: metabase-logs
metabase-logs:
	@chmod +x metabase/manage-vm.sh
	@./metabase/manage-vm.sh logs

.PHONY: metabase-ssh
metabase-ssh:
	@chmod +x metabase/manage-vm.sh
	@./metabase/manage-vm.sh ssh

.PHONY: metabase-ip
metabase-ip:
	@chmod +x metabase/manage-vm.sh
	@./metabase/manage-vm.sh ip

.PHONY: metabase-backup
metabase-backup:
	@chmod +x metabase/manage-vm.sh
	@./metabase/manage-vm.sh backup

.PHONY: metabase-setup
metabase-setup:
	@echo "📋 Setting up Metabase environment..."
	@if [ ! -f "metabase/.env" ]; then \
		cp metabase/.env.example metabase/.env; \
		echo "✅ Created metabase/.env from template"; \
		echo "📝 Please edit metabase/.env with your configuration"; \
	else \
		echo "⚠️  metabase/.env already exists"; \
	fi

.PHONY: metabase-nginx-status
metabase-nginx-status:
	@echo "🔍 Checking Nginx status on Metabase VM..."
	@gcloud compute ssh metabase --zone=us-central1-a --command="sudo systemctl status nginx"

.PHONY: metabase-nginx-logs
metabase-nginx-logs:
	@echo "📋 Showing Nginx logs..."
	@gcloud compute ssh metabase --zone=us-central1-a --command="sudo tail -f /var/log/nginx/access.log"

.PHONY: metabase-nginx-restart
metabase-nginx-restart:
	@echo "🔄 Restarting Nginx..."
	@gcloud compute ssh metabase --zone=us-central1-a --command="sudo systemctl restart nginx"

.PHONY: metabase-nginx-test
metabase-nginx-test:
	@echo "🧪 Testing Nginx configuration..."
	@gcloud compute ssh metabase --zone=us-central1-a --command="sudo nginx -t"

.PHONY: metabase-domain-test
metabase-domain-test:
	@echo "🌐 Testing domain connectivity..."
	@echo "Testing DNS resolution..."
	@nslookup data.izymercado.com.br || echo "❌ DNS not resolved yet"
	@echo "Testing HTTP connectivity..."
	@curl -I http://data.izymercado.com.br || echo "❌ HTTP not accessible yet"
	@echo "Testing HTTPS connectivity..."
	@curl -I https://data.izymercado.com.br || echo "❌ HTTPS not accessible yet"

.PHONY: metabase-summary
metabase-summary:
	@chmod +x metabase/metabase-summary.sh
	@./metabase/metabase-summary.sh

.PHONY: metabase-complete-setup
metabase-complete-setup:
	@echo "� Running complete Metabase setup..."
	@chmod +x metabase/complete-setup.sh
	@./metabase/complete-setup.sh

.PHONY: metabase-open
metabase-open:
	@echo "🌐 Opening Metabase in browser..."
	@python3 -c "import webbrowser; webbrowser.open('http://34.42.186.107:3000')"

.PHONY: metabase-test-questions
metabase-test-questions:
	@echo "🧪 Testing all Metabase questions..."
	@chmod +x metabase/test-all-questions.sh
	@./metabase/test-all-questions.sh

.PHONY: metabase-configure-ssl
metabase-configure-ssl:
	@echo "🔒 Configuring SSL for Metabase..."
	@chmod +x metabase/configure-ssl.sh
	@./metabase/configure-ssl.sh