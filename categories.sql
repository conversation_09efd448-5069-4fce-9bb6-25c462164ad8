--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4 (Ubuntu 17.4-1.pgdg24.04+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (3, 'Pastas, Cremes e Doces', 'https://images.izymercado.com.br/01JSF4VDTMZ99CYNTVDFBG00M1.png', '01JSF4VDTMZ99CYNTVDFBG00M1', true, '2025-04-22 16:21:14.068723', '2025-04-22 16:21:14.068723');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (5, 'Grãos e Cereais', 'https://images.izymercado.com.br/01JSFFFQB9DBGAC005SXKP9M5Z.png', '01JSFFFQB9DBGAC005SXKP9M5Z', true, '2025-04-22 19:27:04.901259', '2025-04-22 19:27:04.901259');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (6, 'Pães e Massas', 'https://images.izymercado.com.br/01JSFFGY4SV58H2T9B95W848BV.png', '01JSFFGY4SV58H2T9B95W848BV', true, '2025-04-22 19:27:44.629554', '2025-04-22 19:27:44.629554');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (7, 'Carnes e proteínas', 'https://images.izymercado.com.br/01JSFGX900486AN9X0C6CDB010.png', '01JSFGX900486AN9X0C6CDB010', true, '2025-04-22 19:51:57.579536', '2025-04-22 19:51:57.579536');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (8, 'Produtos de Despensa', 'https://images.izymercado.com.br/01JSFH8M8FKZRSTBH2NTH4E839.png', '01JSFH8M8FKZRSTBH2NTH4E839', true, '2025-04-22 19:58:09.578548', '2025-04-22 19:58:09.578548');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (9, 'Snacks e Petiscos', 'https://images.izymercado.com.br/01JTH26H1SQET5F67HMGP702VP.png', '01JTH26H1SQET5F67HMGP702VP', true, '2025-05-05 20:29:02.779123', '2025-05-05 20:29:02.779123');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (10, 'Bebidas', 'https://images.izymercado.com.br/01JTV2E2DWT02900JWG482JWF2.png', '01JTV2E2DWT02900JWG482JWF2', true, '2025-05-09 17:45:34.219934', '2025-05-09 17:45:34.219934');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (11, 'Biscoitos e Bolachas', 'https://images.izymercado.com.br/01JV5GDSV7G7H3F291957VY032.png', '01JV5GDSV7G7H3F291957VY032', true, '2025-05-13 19:02:29.787138', '2025-05-13 19:02:29.787138');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (13, 'Cafés e Cápsulas', 'https://images.izymercado.com.br/01JW4DCF05421R54QB06YS9GZF.png', '01JW4DCF05421R54QB06YS9GZF', true, '2025-05-25 19:05:47.738227', '2025-05-25 19:05:47.738227');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (2, 'Laticínios', 'https://images.izymercado.com.br/01JSF3N6WD1NQPSTFHZMXNE76D.png', '01JSF3N6WD1NQPSTFHZMXNE76D', true, '2025-04-22 16:00:22.248268', '2025-04-22 16:00:22.248268');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (4, 'Limpeza e Higiene', 'https://images.izymercado.com.br/01JSFFEXFA76VX71STE2YDJW34.png', '01JSFFEXFA76VX71STE2YDJW34', true, '2025-04-22 19:26:38.402505', '2025-04-22 19:26:38.402505');
INSERT INTO public.categories (id, name, image, external_id, is_active, created_at, updated_at) VALUES (14, 'Pets', 'https://images.izymercado.com.br/01JZJFVXH15Y6PATA46WR5H307.png', '01JZJFVXH15Y6PATA46WR5H307', true, '2025-07-07 13:05:38.719591', '2025-07-07 13:05:38.719591');


--
-- Name: categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.categories_id_seq', 14, true);


--
-- PostgreSQL database dump complete
--

