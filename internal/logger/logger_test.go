package logger

import (
	"bytes"
	"context"
	"os"
	"strings"
	"testing"
	"time"
)

func TestLogger(t *testing.T) {
	// Test logger creation
	log := NewZerologLogger(os.Stdout)
	if log == nil {
		t.Fatal("Logger should not be nil")
	}

	// Test context functions
	ctx := context.Background()

	// Test OrderID context
	orderID := "test-order-123"
	ctxWithOrderID := WithOrderID(ctx, orderID)

	retrievedOrderID, ok := GetOrderID(ctxWithOrderID)
	if !ok {
		t.Fatal("Should be able to retrieve OrderID from context")
	}
	if retrievedOrderID != orderID {
		t.Fatalf("Expected OrderID %s, got %s", orderID, retrievedOrderID)
	}

	// Test UserID context
	userExternalID := "user_abc123"
	ctxWithUserID := WithUserID(ctx, userExternalID)

	retrievedUserID, ok := GetUserID(ctxWithUserID)
	if !ok {
		t.Fatal("Should be able to retrieve User<PERSON> from context")
	}
	if retrievedUserID != userExternalID {
		t.Fatalf("Expected UserExternalID %s, got %s", userExternalID, retrievedUserID)
	}

	// Test CompanyID context
	companyExternalID := "comp_abc123"
	ctxWithCompanyID := WithCompanyExternalID(ctx, companyExternalID)

	retrievedCompanyID, ok := GetCompanyExternalID(ctxWithCompanyID)
	if !ok {
		t.Fatal("Should be able to retrieve CompanyID from context")
	}
	if retrievedCompanyID != companyExternalID {
		t.Fatalf("Expected CompanyExternalID %s, got %s", companyExternalID, retrievedCompanyID)
	}

	// Test combined context
	ctxCombined := WithOrderID(ctx, orderID)
	ctxCombined = WithUserID(ctxCombined, userExternalID)
	ctxCombined = WithCompanyExternalID(ctxCombined, companyExternalID)

	// Test logging with context (this will output to stdout during test)
	log.WithContext(ctxCombined).Info("Test log message with full context")

	// Test convenience functions
	LogCheckoutStep(ctxCombined, "test_step", map[string]interface{}{
		"test_field": "test_value",
	})

	LogCouponValidation(ctxCombined, "TEST_COUPON", "success", map[string]interface{}{
		"discount": 100,
	})

	LogPaymentCreation(ctxCombined, "pix", 1500, map[string]interface{}{
		"split_amount": 1200,
	})
}

func TestGetLogger(t *testing.T) {
	log1 := GetLogger()
	log2 := GetLogger()

	// Should return the same instance (singleton)
	if log1 != log2 {
		t.Fatal("GetLogger should return the same instance")
	}
}

func TestGCPLogging(t *testing.T) {
	// Test GCP-specific logging features
	var buf bytes.Buffer
	logger := NewZerologLogger(&buf)

	ctx := context.Background()

	// Test GCP-specific methods
	logger.WithTrace("projects/test-project/traces/abc123").
		WithSpanID("span-456").
		WithOperation("test-op", "test-service").
		Info("GCP logging test")

	// Test trace extraction
	traceHeader := "abc123/456;o=1"
	extractedTrace := ExtractTraceFromRequest(traceHeader)
	if extractedTrace != "abc123" {
		t.Errorf("Expected trace ID 'abc123', got '%s'", extractedTrace)
	}

	// Test LogWithTrace
	LogWithTrace(ctx, "trace-123", SeverityInfo, "Test trace logging", map[string]interface{}{
		"test": "value",
	})

	// Test LogHTTPRequest
	LogHTTPRequest(ctx, "POST", "/api/test", "test-agent", 200, time.Millisecond*100, nil)

	// Test LogBusinessEvent
	LogBusinessEvent(ctx, "test", "test_event", map[string]interface{}{
		"event_data": "test",
	})

	output := buf.String()
	if output == "" {
		t.Error("Expected GCP log output, got empty string")
	}

	// Check for GCP-specific fields
	if !strings.Contains(output, "GCP logging test") {
		t.Error("Expected log message not found")
	}
}

func TestGCPEnvironmentDetection(t *testing.T) {
	// Test local environment (default)
	var buf bytes.Buffer
	logger := NewZerologLogger(&buf)
	logger.Info("Local test")

	localOutput := buf.String()

	// Test GCP environment
	os.Setenv("APP_ENV", "production")
	buf.Reset()
	logger = NewZerologLogger(&buf)
	logger.Info("GCP test")

	gcpOutput := buf.String()

	// Clean up
	os.Unsetenv("APP_ENV")

	if localOutput == "" || gcpOutput == "" {
		t.Error("Expected output from both local and GCP configurations")
	}

	// Both should contain the message, but formatting may differ
	if !strings.Contains(localOutput, "Local test") {
		t.Error("Local output should contain message")
	}
	if !strings.Contains(gcpOutput, "GCP test") {
		t.Error("GCP output should contain message")
	}
}
