package config

import (
	"fmt"
	"time"
)

// WithdrawalConfig holds configuration for the withdrawal system
type WithdrawalConfig struct {
	// Business rules
	MinimumWithdrawalAmount int32 `env:"WITHDRAWAL_MINIMUM_AMOUNT" envDefault:"50000"`  // R$ 500.00 in cents
	HoldingPeriodDays       int   `env:"WITHDRAWAL_HOLDING_PERIOD_DAYS" envDefault:"3"` // 3 days holding period
	MaxWithdrawalAmount     int32 `env:"WITHDRAWAL_MAX_AMOUNT" envDefault:"10000000"`   // R$ 100,000.00 in cents

	// Rate limiting
	MaxWithdrawalsPerDay   int `env:"WITHDRAWAL_MAX_PER_DAY" envDefault:"5"`    // Max withdrawals per company per day
	MaxWithdrawalsPerMonth int `env:"WITHDRAWAL_MAX_PER_MONTH" envDefault:"50"` // Max withdrawals per company per month

	// Processing timeouts
	ProcessingTimeout       time.Duration `env:"WITHDRAWAL_PROCESSING_TIMEOUT" envDefault:"30s"` // Timeout for withdrawal processing
	PayoutAllocationTimeout time.Duration `env:"PAYOUT_ALLOCATION_TIMEOUT" envDefault:"10s"`     // Timeout for payout allocation

	// Database settings
	MaxRetries int           `env:"WITHDRAWAL_MAX_RETRIES" envDefault:"3"`  // Max retry attempts
	RetryDelay time.Duration `env:"WITHDRAWAL_RETRY_DELAY" envDefault:"1s"` // Delay between retries

	// Monitoring and alerting
	EnableMetrics        bool  `env:"WITHDRAWAL_ENABLE_METRICS" envDefault:"true"`     // Enable metrics collection
	EnableAlerting       bool  `env:"WITHDRAWAL_ENABLE_ALERTING" envDefault:"true"`    // Enable alerting
	AlertThresholdAmount int32 `env:"WITHDRAWAL_ALERT_THRESHOLD" envDefault:"5000000"` // R$ 50,000.00 alert threshold

	// Security settings
	RequireIPWhitelist bool     `env:"WITHDRAWAL_REQUIRE_IP_WHITELIST" envDefault:"false"` // Require IP whitelist for admin endpoints
	AllowedIPs         []string `env:"WITHDRAWAL_ALLOWED_IPS" envSeparator:","`            // Comma-separated list of allowed IPs

	// Feature flags
	EnableMaterializedView bool `env:"WITHDRAWAL_ENABLE_MATERIALIZED_VIEW" envDefault:"true"` // Use materialized view for balance
	EnableBatchProcessing  bool `env:"WITHDRAWAL_ENABLE_BATCH_PROCESSING" envDefault:"true"`  // Enable batch processing
	EnableAutoRefresh      bool `env:"WITHDRAWAL_ENABLE_AUTO_REFRESH" envDefault:"true"`      // Enable auto refresh of views
}

// DefaultWithdrawalConfig returns the default configuration
func DefaultWithdrawalConfig() *WithdrawalConfig {
	return &WithdrawalConfig{
		MinimumWithdrawalAmount: 50000,    // R$ 500.00
		HoldingPeriodDays:       3,        // 3 days
		MaxWithdrawalAmount:     10000000, // R$ 100,000.00
		MaxWithdrawalsPerDay:    5,
		MaxWithdrawalsPerMonth:  50,
		ProcessingTimeout:       30 * time.Second,
		PayoutAllocationTimeout: 10 * time.Second,

		MaxRetries:             3,
		RetryDelay:             1 * time.Second,
		EnableMetrics:          true,
		EnableAlerting:         true,
		AlertThresholdAmount:   5000000, // R$ 50,000.00
		RequireIPWhitelist:     false,
		AllowedIPs:             []string{},
		EnableMaterializedView: true,
		EnableBatchProcessing:  true,
		EnableAutoRefresh:      true,
	}
}

// Validate validates the withdrawal configuration
func (c *WithdrawalConfig) Validate() error {
	if c.MinimumWithdrawalAmount <= 0 {
		return fmt.Errorf("minimum withdrawal amount must be positive")
	}

	if c.MaxWithdrawalAmount <= c.MinimumWithdrawalAmount {
		return fmt.Errorf("maximum withdrawal amount must be greater than minimum")
	}

	if c.HoldingPeriodDays < 0 {
		return fmt.Errorf("holding period days cannot be negative")
	}

	if c.MaxWithdrawalsPerDay <= 0 {
		return fmt.Errorf("max withdrawals per day must be positive")
	}

	if c.MaxWithdrawalsPerMonth <= 0 {
		return fmt.Errorf("max withdrawals per month must be positive")
	}

	if c.ProcessingTimeout <= 0 {
		return fmt.Errorf("processing timeout must be positive")
	}

	if c.PayoutAllocationTimeout <= 0 {
		return fmt.Errorf("payout allocation timeout must be positive")
	}

	if c.MaxRetries < 0 {
		return fmt.Errorf("max retries cannot be negative")
	}

	if c.RetryDelay < 0 {
		return fmt.Errorf("retry delay cannot be negative")
	}

	return nil
}

// GetMinimumWithdrawalAmountReais returns the minimum withdrawal amount in reais
func (c *WithdrawalConfig) GetMinimumWithdrawalAmountReais() float64 {
	return float64(c.MinimumWithdrawalAmount) / 100.0
}

// GetMaxWithdrawalAmountReais returns the maximum withdrawal amount in reais
func (c *WithdrawalConfig) GetMaxWithdrawalAmountReais() float64 {
	return float64(c.MaxWithdrawalAmount) / 100.0
}

// GetAlertThresholdAmountReais returns the alert threshold amount in reais
func (c *WithdrawalConfig) GetAlertThresholdAmountReais() float64 {
	return float64(c.AlertThresholdAmount) / 100.0
}

// IsIPAllowed checks if an IP address is allowed for admin operations
func (c *WithdrawalConfig) IsIPAllowed(ip string) bool {
	if !c.RequireIPWhitelist {
		return true
	}

	for _, allowedIP := range c.AllowedIPs {
		if allowedIP == ip {
			return true
		}
	}

	return false
}

// GetHoldingPeriodDuration returns the holding period as a time.Duration
func (c *WithdrawalConfig) GetHoldingPeriodDuration() time.Duration {
	return time.Duration(c.HoldingPeriodDays) * 24 * time.Hour
}
