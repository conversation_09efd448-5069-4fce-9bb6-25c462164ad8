package woovi

import (
	"context"
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"
)

type MockHTTPClient struct {
	DoFunc func(req *http.Request) (*http.Response, error)
}

func (m *MockHTTPClient) Do(req *http.Request) (*http.Response, error) {
	return m.DoFunc(req)
}

func TestCreatePixPayment(t *testing.T) {
	mockClient := &MockHTTPClient{
		DoFunc: func(req *http.Request) (*http.Response, error) {
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       io.NopCloser(strings.NewReader(`{"Charge": {"status": "success"}}`)),
			}, nil
		},
	}

	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		Pix<PERSON>ey:     "test-pix-key",
		HTTPClient: mockClient,
	})

	response, err := woovi.CreatePixPaymentCharge(CreatePixChargeParams{
		Context: context.Background(), // Add context
		Value:   100,
		OrderId: "123",
		Customer: Customer{
			Name:  "Test User",
			Email: "<EMAIL>",
			TaxID: "12345678900",
		},
		CompanyCNPJ:   "12345678000100",
		CompanyPixKey: "test-pix-key",
	})

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if response.Charge.Status != "success" {
		t.Fatalf("Expected status 'success', got %v", response.Charge.Status)
	}
}

func TestGetChargeStatus_Success(t *testing.T) {
	mockClient := &MockHTTPClient{
		DoFunc: func(req *http.Request) (*http.Response, error) {
			// Verify request method and endpoint
			if req.Method != http.MethodGet {
				t.Errorf("Expected GET request, got %s", req.Method)
			}
			if !strings.Contains(req.URL.Path, "/charge/test-order-id") {
				t.Errorf("Expected order ID in URL path, got %s", req.URL.Path)
			}

			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       io.NopCloser(strings.NewReader(`{"charge": {"status": "ACTIVE", "correlationID": "test-order-id", "paymentLinkID": "test-charge-id"}}`)),
			}, nil
		},
	}

	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		PixKey:     "test-pix-key",
		HTTPClient: mockClient,
	})

	ctx := context.Background()
	response, err := woovi.GetChargeStatus(ctx, "test-order-id")

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if response.Charge.Status != "ACTIVE" {
		t.Fatalf("Expected status 'ACTIVE', got %v", response.Charge.Status)
	}
}

func TestGetChargeStatus_NotFound(t *testing.T) {
	mockClient := &MockHTTPClient{
		DoFunc: func(req *http.Request) (*http.Response, error) {
			return &http.Response{
				StatusCode: http.StatusNotFound,
				Body:       io.NopCloser(strings.NewReader(`{"error": "charge not found"}`)),
			}, nil
		},
	}

	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		PixKey:     "test-pix-key",
		HTTPClient: mockClient,
	})

	ctx := context.Background()
	_, err := woovi.GetChargeStatus(ctx, "non-existent-order")

	if err == nil {
		t.Fatal("Expected error for non-existent charge, got nil")
	}

	if !strings.Contains(err.Error(), "charge not found") {
		t.Fatalf("Expected 'charge not found' error, got %v", err)
	}
}

func TestDeletePixPaymentCharge_Success(t *testing.T) {
	mockClient := &MockHTTPClient{
		DoFunc: func(req *http.Request) (*http.Response, error) {
			// Verify request method and endpoint
			if req.Method != http.MethodDelete {
				t.Errorf("Expected DELETE request, got %s", req.Method)
			}
			if !strings.Contains(req.URL.Path, "/charge/test-order-id") {
				t.Errorf("Expected order ID in URL path, got %s", req.URL.Path)
			}

			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       io.NopCloser(strings.NewReader(`{"success": true}`)),
			}, nil
		},
	}

	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		PixKey:     "test-pix-key",
		HTTPClient: mockClient,
	})

	ctx := context.Background()
	err := woovi.DeletePixPaymentCharge(ctx, "test-order-id")

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
}

func TestDeletePixPaymentCharge_NotFound(t *testing.T) {
	mockClient := &MockHTTPClient{
		DoFunc: func(req *http.Request) (*http.Response, error) {
			return &http.Response{
				StatusCode: http.StatusNotFound,
				Body:       io.NopCloser(strings.NewReader(`{"error": "charge not found"}`)),
			}, nil
		},
	}

	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		PixKey:     "test-pix-key",
		HTTPClient: mockClient,
	})

	ctx := context.Background()
	err := woovi.DeletePixPaymentCharge(ctx, "non-existent-order")

	if err == nil {
		t.Fatal("Expected error for non-existent charge, got nil")
	}

	if !strings.Contains(err.Error(), "charge not found") {
		t.Fatalf("Expected 'charge not found' error, got %v", err)
	}
}

func TestHandlePendingChargeOnCancellation_ActiveCharge(t *testing.T) {
	callCount := 0
	mockClient := &MockHTTPClient{
		DoFunc: func(req *http.Request) (*http.Response, error) {
			callCount++

			// First call: GET charge status
			if req.Method == http.MethodGet && callCount == 1 {
				return &http.Response{
					StatusCode: http.StatusOK,
					Body:       io.NopCloser(strings.NewReader(`{"charge": {"status": "ACTIVE", "correlationID": "test-order-id", "paymentLinkID": "test-charge-id"}}`)),
				}, nil
			}

			// Second call: DELETE charge
			if req.Method == http.MethodDelete && callCount == 2 {
				return &http.Response{
					StatusCode: http.StatusOK,
					Body:       io.NopCloser(strings.NewReader(`{"success": true}`)),
				}, nil
			}

			t.Errorf("Unexpected request: %s %s", req.Method, req.URL.Path)
			return nil, nil
		},
	}

	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		PixKey:     "test-pix-key",
		HTTPClient: mockClient,
	})

	ctx := context.Background()
	err := woovi.HandlePendingChargeOnCancellation(ctx, "test-order-id")

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if callCount != 2 {
		t.Fatalf("Expected 2 API calls (GET + DELETE), got %d", callCount)
	}
}

func TestHandlePendingChargeOnCancellation_NonActiveCharge(t *testing.T) {
	mockClient := &MockHTTPClient{
		DoFunc: func(req *http.Request) (*http.Response, error) {
			// Only GET call should be made
			if req.Method == http.MethodGet {
				return &http.Response{
					StatusCode: http.StatusOK,
					Body:       io.NopCloser(strings.NewReader(`{"charge": {"status": "COMPLETED", "correlationID": "test-order-id", "paymentLinkID": "test-charge-id"}}`)),
				}, nil
			}

			t.Errorf("Unexpected request: %s %s", req.Method, req.URL.Path)
			return nil, nil
		},
	}

	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		PixKey:     "test-pix-key",
		HTTPClient: mockClient,
	})

	ctx := context.Background()
	err := woovi.HandlePendingChargeOnCancellation(ctx, "test-order-id")

	if err != nil {
		t.Fatalf("Expected no error for non-active charge, got %v", err)
	}
}

func TestHandlePendingChargeOnCancellation_ChargeNotFound(t *testing.T) {
	mockClient := &MockHTTPClient{
		DoFunc: func(req *http.Request) (*http.Response, error) {
			// GET call returns 404
			if req.Method == http.MethodGet {
				return &http.Response{
					StatusCode: http.StatusNotFound,
					Body:       io.NopCloser(strings.NewReader(`{"error": "charge not found"}`)),
				}, nil
			}

			t.Errorf("Unexpected request: %s %s", req.Method, req.URL.Path)
			return nil, nil
		},
	}

	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		PixKey:     "test-pix-key",
		HTTPClient: mockClient,
	})

	ctx := context.Background()
	err := woovi.HandlePendingChargeOnCancellation(ctx, "non-existent-order")

	// Should not return error when charge is not found (graceful handling)
	if err != nil {
		t.Fatalf("Expected no error for non-existent charge (graceful handling), got %v", err)
	}
}

func TestHandlePendingChargeOnCancellation_EmptyOrderID(t *testing.T) {
	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		PixKey:     "test-pix-key",
		HTTPClient: &MockHTTPClient{},
	})

	ctx := context.Background()
	err := woovi.HandlePendingChargeOnCancellation(ctx, "")

	if err == nil {
		t.Fatal("Expected error for empty order ID, got nil")
	}

	if !strings.Contains(err.Error(), "order ID cannot be empty") {
		t.Fatalf("Expected 'order ID cannot be empty' error, got %v", err)
	}
}

func TestHandlePendingChargeOnCancellation_NetworkError(t *testing.T) {
	mockClient := &MockHTTPClient{
		DoFunc: func(req *http.Request) (*http.Response, error) {
			return nil, errors.New("network error")
		},
	}

	woovi := New(WooviConfig{
		APIKey:     "test-api-key",
		Url:        "https://api.test.com",
		PixKey:     "test-pix-key",
		HTTPClient: mockClient,
	})

	ctx := context.Background()
	err := woovi.HandlePendingChargeOnCancellation(ctx, "test-order-id")

	if err == nil {
		t.Fatal("Expected error for network failure, got nil")
	}

	if !strings.Contains(err.Error(), "failed to get charge status") {
		t.Fatalf("Expected 'failed to get charge status' error, got %v", err)
	}
}
