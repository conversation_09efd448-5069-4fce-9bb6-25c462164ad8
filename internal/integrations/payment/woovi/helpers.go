package woovi

import "regexp"

func isValidCPF(cpf string) bool {
	regex := regexp.MustCompile(`^\d{11}$`)
	return regex.MatchString(cpf)
}

func isValidCNPJ(cnpj string) bool {
	regex := regexp.MustCompile(`^\d{14}$`)
	return regex.MatchString(cnpj)
}

func isValidEmail(email string) bool {
	regex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return regex.MatchString(email)
}

func isValidPhoneNumber(phone string) bool {
	regex := regexp.MustCompile(`^(\+55)?\d{10,11}$`)
	return regex.MatchString(phone)
}

func isValidRandomKey(key string) bool {
	regex := regexp.MustCompile(`^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$`)
	return regex.MatchString(key)
}
