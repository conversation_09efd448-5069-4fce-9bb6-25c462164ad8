package woovi

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/izy-mercado/backend/internal/logger"
)

type Woovi struct {
	APIKey     string
	Url        string
	PixKey     string
	HTTPClient HTTPClient
}

type TransferBetweenSubAccounts struct {
	Value          int    `json:"value"`
	FromPixKey     string `json:"fromPixKey"`
	FromPixKeyType string `json:"fromPixKeyType"`
	ToPixKey       string `json:"toPixKey"`
	ToPixKeyType   string `json:"toPixKeyType"`
}

// WithdrawRequest represents the withdrawal request payload
type WithdrawRequest struct {
	Value int32 `json:"value"`
}

// WithdrawResponse represents the withdrawal response from Woovi
type WithdrawResponse struct {
	Transaction struct {
		Status           string `json:"status"`
		Value            int32  `json:"value"`
		CorrelationID    string `json:"correlationID"`
		DestinationAlias string `json:"destinationAlias"`
		Comment          string `json:"comment"`
		EndToEndId       string `json:"endToEndId,omitempty"`
		Time             string `json:"time,omitempty"`
	} `json:"transaction"`
}

type PixSuccessResponse struct {
	Charge struct {
		Status   string `json:"status"`
		Customer struct {
			Name  string `json:"name"`
			Email string `json:"email"`
			Phone string `json:"phone"`
			TaxID struct {
				TaxID string `json:"taxID"`
				Type  string `json:"type"`
			} `json:"taxID"`
		} `json:"customer"`
		Value          int       `json:"value"`
		Comment        string    `json:"comment"`
		CorrelationID  string    `json:"correlationID"`
		PaymentLinkID  string    `json:"paymentLinkID"`
		PaymentLinkURL string    `json:"paymentLinkUrl"`
		QrCodeImage    string    `json:"qrCodeImage"`
		ExpiresIn      int       `json:"expiresIn"`
		ExpiresDate    time.Time `json:"expiresDate"`
		CreatedAt      time.Time `json:"createdAt"`
		UpdatedAt      time.Time `json:"updatedAt"`
		BrCode         string    `json:"brCode"`
		AdditionalInfo []struct {
			Key   string `json:"key"`
			Value string `json:"value"`
		} `json:"additionalInfo"`
		PaymentMethods struct {
			Pix struct {
				Method         string        `json:"method"`
				TransactionID  string        `json:"transactionID"`
				Identifier     string        `json:"identifier"`
				AdditionalInfo []interface{} `json:"additionalInfo"`
				Fee            int           `json:"fee"`
				Value          int           `json:"value"`
				Status         string        `json:"status"`
				TxID           string        `json:"txId"`
				BrCode         string        `json:"brCode"`
				QrCodeImage    string        `json:"qrCodeImage"`
			} `json:"pix"`
		} `json:"paymentMethods"`
	} `json:"charge"`
}

type CreatePixChargeParams struct {
	Context               context.Context
	Value                 int32
	Discount              int32
	OrderId               string
	Customer              Customer
	CompanyName           string
	CompanyCNPJ           string
	CompanyPixKey         string
	CompanyCommissionRate int32
	CompanyCashbackRate   int32
	CompanyExternalID     string
	SplitValue            int32
	ShippingFee           int32
}

type CreateNewChargeWithSubAccountSplit struct {
	CorrelationID                     string           `json:"correlationID"`
	Value                             int32            `json:"value"`
	Customer                          Customer         `json:"customer"`
	Comment                           string           `json:"comment"`
	Splits                            []Split          `json:"splits"`
	AdditionalInfo                    []AdditionalInfo `json:"additionalInfo"`
	EnableCashbackExclusivePercentage bool             `json:"enableCashbackExclusivePercentage,omitempty"`
}

type AdditionalInfo struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Customer struct {
	Name  string `json:"name"`
	Email string `json:"email"`
	TaxID string `json:"taxID"`
	Phone string `json:"phone"`
}
type Split struct {
	Value     int32  `json:"value"`
	PixKey    string `json:"pixKey"`
	SplitType string `json:"splitType"`
}

type HTTPClient interface {
	Do(req *http.Request) (*http.Response, error)
}

type WooviConfig struct {
	APIKey     string
	Url        string
	PixKey     string
	HTTPClient HTTPClient
}

func New(cfg WooviConfig) *Woovi {
	if cfg.HTTPClient == nil {
		cfg.HTTPClient = http.DefaultClient
	}
	return &Woovi{
		APIKey:     cfg.APIKey,
		Url:        cfg.Url,
		PixKey:     cfg.PixKey,
		HTTPClient: cfg.HTTPClient,
	}
}

func (w *Woovi) CreatePixPaymentCharge(pix CreatePixChargeParams) (PixSuccessResponse, error) {
	endpoint := w.Url + "/charge?return_existing=true"

	// Get logger instance
	log := logger.GetLogger()

	// Log payment creation start
	log.WithContext(pix.Context).WithFields(map[string]interface{}{
		"order_id":       pix.OrderId,
		"value":          pix.Value,
		"discount":       pix.Discount,
		"split_value":    pix.SplitValue,
		"company_name":   pix.CompanyName,
		"company_cnpj":   pix.CompanyCNPJ,
		"customer_email": pix.Customer.Email,
		"endpoint":       endpoint,
	}).Info("Starting PIX payment charge creation")

	commissionRate := float64(pix.CompanyCommissionRate) / 10000

	charge := CreateNewChargeWithSubAccountSplit{
		CorrelationID: pix.OrderId,
		Value:         pix.Value,
		Customer: Customer{
			Name:  pix.Customer.Name,
			Email: pix.Customer.Email,
			Phone: pix.Customer.Phone,
			TaxID: pix.Customer.TaxID,
		},
		Comment:                           fmt.Sprintf("Compra realizada em %s. CNPJ: %s", pix.CompanyName, pix.CompanyCNPJ),
		EnableCashbackExclusivePercentage: true,
		Splits: []Split{
			{
				PixKey:    pix.CompanyPixKey,
				Value:     pix.SplitValue,
				SplitType: "SPLIT_SUB_ACCOUNT",
			},
		},
		AdditionalInfo: []AdditionalInfo{
			{
				Key:   "discount",
				Value: strconv.Itoa(int(pix.Discount)),
			},
			{
				Key:   "comissionRate",
				Value: strconv.FormatFloat(commissionRate, 'f', 2, 64),
			},
			{
				Key:   "companyExternalId",
				Value: pix.CompanyExternalID,
			},
			{
				Key:   "shippingFee",
				Value: strconv.Itoa(int(pix.ShippingFee)),
			},
		},
	}

	// Log charge details
	log.WithContext(pix.Context).WithFields(map[string]interface{}{
		"commission_rate":       commissionRate,
		"splits_count":          len(charge.Splits),
		"additional_info_count": len(charge.AdditionalInfo),
	}).Debug("PIX charge details prepared")

	payload, err := json.Marshal(charge)
	if err != nil {
		log.WithContext(pix.Context).WithError(err).Error("Error marshalling charge payload")
		return PixSuccessResponse{}, err
	}

	log.WithContext(pix.Context).WithFields(map[string]interface{}{
		"payload_size":    len(payload),
		"request_payload": string(payload),
		"order_id":        pix.OrderId,
	}).Info("Charge payload marshalled successfully")

	req, err := http.NewRequest(http.MethodPost, endpoint, bytes.NewReader(payload))
	if err != nil {
		log.WithContext(pix.Context).WithError(err).WithFields(map[string]interface{}{
			"endpoint":        endpoint,
			"request_payload": string(payload),
			"order_id":        pix.OrderId,
		}).Error("Error creating HTTP request")
		return PixSuccessResponse{}, err
	}

	req.Header.Add("content-type", "application/json")
	req.Header.Add("Authorization", w.APIKey)

	log.WithContext(pix.Context).WithField("endpoint", endpoint).Info("Sending PIX payment request to Woovi")

	res, err := w.HTTPClient.Do(req)
	if err != nil {
		log.WithContext(pix.Context).WithError(err).WithFields(map[string]interface{}{
			"endpoint":        endpoint,
			"request_payload": string(payload),
			"order_id":        pix.OrderId,
		}).Error("Error making HTTP request to Woovi")
		return PixSuccessResponse{}, err
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.WithContext(pix.Context).WithError(err).Error("Error reading response body from Woovi")
		return PixSuccessResponse{}, err
	}

	log.WithContext(pix.Context).WithFields(map[string]interface{}{
		"status_code":   res.StatusCode,
		"response_size": len(body),
	}).Debug("Received response from Woovi")

	if res.StatusCode != http.StatusOK {
		log.WithContext(pix.Context).WithFields(map[string]interface{}{
			"status_code":     res.StatusCode,
			"response_body":   string(body),
			"request_payload": string(payload),
			"endpoint":        endpoint,
			"order_id":        pix.OrderId,
			"value":           pix.Value,
			"company_name":    pix.CompanyName,
		}).Error("Woovi API returned error status")
		return PixSuccessResponse{}, fmt.Errorf("error creating pix payment: %v", string(body))
	}

	var successResponse PixSuccessResponse
	err = json.Unmarshal(body, &successResponse)
	if err != nil {
		log.WithContext(pix.Context).WithError(err).WithFields(map[string]interface{}{
			"response_body":   string(body),
			"request_payload": string(payload),
			"order_id":        pix.OrderId,
		}).Error("Error unmarshalling Woovi response")
		return PixSuccessResponse{}, err
	}

	// Log successful payment creation
	log.WithContext(pix.Context).WithFields(map[string]interface{}{
		"correlation_id":  successResponse.Charge.CorrelationID,
		"payment_link_id": successResponse.Charge.PaymentLinkID,
		"charge_status":   successResponse.Charge.Status,
		"expires_in":      successResponse.Charge.ExpiresIn,
	}).Info("PIX payment charge created successfully")

	return successResponse, nil
}

func (w *Woovi) GetChargeStatus(ctx context.Context, orderID string) (PixSuccessResponse, error) {
	endpoint := w.Url + "/charge/" + orderID

	// Get logger instance
	log := logger.GetLogger()

	// Log charge status check start
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"order_id": orderID,
		"endpoint": endpoint,
	}).Info("Starting charge status check")

	req, err := http.NewRequest(http.MethodGet, endpoint, nil)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"order_id": orderID,
			"endpoint": endpoint,
		}).Error("Error creating HTTP request for charge status")
		return PixSuccessResponse{}, err
	}

	req.Header.Add("content-type", "application/json")
	req.Header.Add("Authorization", w.APIKey)

	log.WithContext(ctx).WithField("endpoint", endpoint).Info("Sending charge status request to Woovi")

	res, err := w.HTTPClient.Do(req)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"order_id": orderID,
			"endpoint": endpoint,
		}).Error("Error making HTTP request to Woovi for charge status")
		return PixSuccessResponse{}, err
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithField("order_id", orderID).Error("Error reading response body from Woovi")
		return PixSuccessResponse{}, err
	}

	log.WithContext(ctx).WithFields(map[string]interface{}{
		"status_code":   res.StatusCode,
		"response_size": len(body),
		"order_id":      orderID,
	}).Debug("Received charge status response from Woovi")

	if res.StatusCode != http.StatusOK {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"status_code":   res.StatusCode,
			"response_body": string(body),
			"order_id":      orderID,
			"endpoint":      endpoint,
		}).Error("Woovi API returned error status for charge status")

		// Handle specific error cases
		if res.StatusCode == http.StatusNotFound {
			return PixSuccessResponse{}, fmt.Errorf("charge not found: %s", orderID)
		}
		if res.StatusCode == http.StatusUnauthorized {
			return PixSuccessResponse{}, fmt.Errorf("unauthorized access to Woovi API")
		}
		if res.StatusCode >= 500 {
			return PixSuccessResponse{}, fmt.Errorf("Woovi API server error (status %d): %v", res.StatusCode, string(body))
		}

		return PixSuccessResponse{}, fmt.Errorf("error getting charge status (status %d): %v", res.StatusCode, string(body))
	}

	var statusResponse PixSuccessResponse
	err = json.Unmarshal(body, &statusResponse)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"response_body": string(body),
			"order_id":      orderID,
		}).Error("Error unmarshalling Woovi charge status response")
		return PixSuccessResponse{}, err
	}

	// Log successful charge status retrieval
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"order_id":       orderID,
		"charge_status":  statusResponse.Charge.Status,
		"correlation_id": statusResponse.Charge.CorrelationID,
	}).Info("Charge status retrieved successfully")

	return statusResponse, nil
}

func (w *Woovi) DeletePixPaymentCharge(ctx context.Context, orderID string) error {
	endpoint := w.Url + "/charge/" + orderID

	// Get logger instance
	log := logger.GetLogger()

	// Log charge deletion start
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"order_id": orderID,
		"endpoint": endpoint,
	}).Info("Starting charge deletion")

	req, err := http.NewRequest(http.MethodDelete, endpoint, nil)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"order_id": orderID,
			"endpoint": endpoint,
		}).Error("Error creating HTTP request for charge deletion")
		return err
	}

	req.Header.Add("content-type", "application/json")
	req.Header.Add("Authorization", w.APIKey)

	log.WithContext(ctx).WithField("endpoint", endpoint).Info("Sending charge deletion request to Woovi")

	res, err := w.HTTPClient.Do(req)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"order_id": orderID,
			"endpoint": endpoint,
		}).Error("Error making HTTP request to Woovi for charge deletion")
		return err
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithField("order_id", orderID).Error("Error reading response body from Woovi")
		return err
	}

	log.WithContext(ctx).WithFields(map[string]interface{}{
		"status_code":   res.StatusCode,
		"response_size": len(body),
		"order_id":      orderID,
	}).Debug("Received charge deletion response from Woovi")

	if res.StatusCode != http.StatusOK {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"status_code":   res.StatusCode,
			"response_body": string(body),
			"order_id":      orderID,
			"endpoint":      endpoint,
		}).Error("Woovi API returned error status for charge deletion")

		// Handle specific error cases
		if res.StatusCode == http.StatusNotFound {
			log.WithContext(ctx).WithField("order_id", orderID).Warn("Charge not found for deletion, may have been already deleted")
			return fmt.Errorf("charge not found for deletion: %s", orderID)
		}
		if res.StatusCode == http.StatusUnauthorized {
			return fmt.Errorf("unauthorized access to Woovi API")
		}
		if res.StatusCode >= 500 {
			return fmt.Errorf("Woovi API server error during deletion (status %d): %v", res.StatusCode, string(body))
		}

		return fmt.Errorf("error deleting pix payment (status %d): %v", res.StatusCode, string(body))
	}

	// Log successful charge deletion
	log.WithContext(ctx).WithField("order_id", orderID).Info("Charge deleted successfully")

	return nil
}

// HandlePendingChargeOnCancellation checks charge status and deletes it if pending
// This method implements the logic required when an invoice is cancelled from mobile app
func (w *Woovi) HandlePendingChargeOnCancellation(ctx context.Context, orderID string) error {
	// Get logger instance
	log := logger.GetLogger()

	// Log start of charge handling
	log.WithContext(ctx).WithField("order_id", orderID).Info("Starting pending charge handling for cancellation")

	// Validate order ID
	if orderID == "" {
		log.WithContext(ctx).Error("Order ID is empty, cannot handle pending charge")
		return fmt.Errorf("order ID cannot be empty")
	}

	// Step 1: Check charge status
	chargeResponse, err := w.GetChargeStatus(ctx, orderID)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithField("order_id", orderID).Error("Failed to get charge status")

		// Handle specific error cases gracefully
		if strings.Contains(err.Error(), "charge not found") {
			log.WithContext(ctx).WithField("order_id", orderID).Warn("Charge not found, may have been already deleted or expired")
			return nil // Don't fail the cancellation if charge doesn't exist
		}
		if strings.Contains(err.Error(), "unauthorized") {
			log.WithContext(ctx).WithField("order_id", orderID).Error("Unauthorized access to Woovi API")
			return fmt.Errorf("failed to get charge status due to authorization: %w", err)
		}
		if strings.Contains(err.Error(), "server error") {
			log.WithContext(ctx).WithField("order_id", orderID).Error("Woovi API server error")
			return fmt.Errorf("failed to get charge status due to server error: %w", err)
		}

		return fmt.Errorf("failed to get charge status: %w", err)
	}

	chargeStatus := chargeResponse.Charge.Status
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"order_id":      orderID,
		"charge_status": chargeStatus,
	}).Info("Retrieved charge status")

	// Step 2: Check if charge is pending
	if chargeStatus != "ACTIVE" {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"order_id":      orderID,
			"charge_status": chargeStatus,
		}).Info("Charge is not active, skipping deletion")
		return nil
	}

	// Step 3: Delete active charge
	log.WithContext(ctx).WithField("order_id", orderID).Info("Charge is active, proceeding with deletion")

	err = w.DeletePixPaymentCharge(ctx, orderID)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithField("order_id", orderID).Error("Failed to delete active charge")

		// Handle specific deletion error cases
		if strings.Contains(err.Error(), "charge not found") {
			log.WithContext(ctx).WithField("order_id", orderID).Warn("Charge not found for deletion, may have been already processed or deleted")
			return nil // Don't fail if charge was already deleted
		}
		if strings.Contains(err.Error(), "unauthorized") {
			log.WithContext(ctx).WithField("order_id", orderID).Error("Unauthorized access during charge deletion")
			return fmt.Errorf("failed to delete active charge due to authorization: %w", err)
		}
		if strings.Contains(err.Error(), "server error") {
			log.WithContext(ctx).WithField("order_id", orderID).Error("Woovi API server error during deletion")
			return fmt.Errorf("failed to delete active charge due to server error: %w", err)
		}

		return fmt.Errorf("failed to delete active charge: %w", err)
	}

	log.WithContext(ctx).WithField("order_id", orderID).Info("Successfully handled active charge on cancellation")
	return nil
}

func (w *Woovi) TransferBetweenSubAccounts(value int32, toPixKey, CompanyExternalID string) ([]byte, error) {
	endpoint := "/subaccount/transfer"

	toPixKeyType := detectPixKeyType(toPixKey)
	payload := strings.NewReader(`{"value":` + strconv.Itoa(int(value)) + `,"fromPixKey":"` + w.PixKey + `","fromPixKeyType":"CNPJ","toPixKey":"` + toPixKey + `","toPixKeyType":"` + toPixKeyType + `","correlationId":"` + CompanyExternalID + `"}`)

	req, err := http.NewRequest("POST", endpoint, payload)
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return nil, err
	}

	req.Header.Add("content-type", "application/json")
	req.Header.Add("Authorization", w.APIKey)

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Printf("Error making request: %v", err)
		return nil, err
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Printf("Error reading response body: %v", err)
		return nil, err
	}

	return body, nil
}

func (w *Woovi) CreateSubAccount(key, name string) ([]byte, error) {
	endpoint := w.Url + "/subaccount"

	payload := strings.NewReader(`{"pixKey":"` + key + `","name":"` + name + `"}`)

	req, err := http.NewRequest("POST", endpoint, payload)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	req.Header.Add("content-type", "application/json")
	req.Header.Add("Authorization", w.APIKey)

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Printf("Error reading response body: %v", err)
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		log.Println("Error creating subaccount")
		return nil, errors.New(string(body))
	}

	return body, nil
}

// WithdrawFromSubAccount initiates a withdrawal from a sub-account
func (w *Woovi) WithdrawFromSubAccount(ctx context.Context, pixKey string, value int32) (WithdrawResponse, error) {
	endpoint := w.Url + "/subaccount/" + pixKey + "/withdraw"

	// Get logger instance
	log := logger.GetLogger()

	// Log withdrawal start
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"pix_key":  pixKey,
		"value":    value,
		"endpoint": endpoint,
	}).Info("Starting sub-account withdrawal")

	// Validate withdrawal amount
	if value <= 0 {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"pix_key": pixKey,
			"value":   value,
		}).Error("Invalid withdrawal amount: must be greater than zero")
		return WithdrawResponse{}, fmt.Errorf("withdrawal amount must be greater than zero")
	}

	withdrawRequest := WithdrawRequest{
		Value: value,
	}

	payload, err := json.Marshal(withdrawRequest)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("Error marshalling withdrawal request payload")
		return WithdrawResponse{}, err
	}

	log.WithContext(ctx).WithFields(map[string]interface{}{
		"pix_key":         pixKey,
		"value":           value,
		"payload_size":    len(payload),
		"request_payload": string(payload),
	}).Info("Withdrawal payload marshalled successfully")

	req, err := http.NewRequest(http.MethodPost, endpoint, bytes.NewReader(payload))
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"pix_key":         pixKey,
			"endpoint":        endpoint,
			"request_payload": string(payload),
		}).Error("Error creating HTTP request for withdrawal")
		return WithdrawResponse{}, err
	}

	req.Header.Add("content-type", "application/json")
	req.Header.Add("Authorization", w.APIKey)

	log.WithContext(ctx).WithField("endpoint", endpoint).Info("Sending withdrawal request to Woovi")

	res, err := w.HTTPClient.Do(req)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"pix_key":         pixKey,
			"endpoint":        endpoint,
			"request_payload": string(payload),
		}).Error("Error making HTTP request to Woovi for withdrawal")
		return WithdrawResponse{}, err
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithField("pix_key", pixKey).Error("Error reading response body from Woovi")
		return WithdrawResponse{}, err
	}

	log.WithContext(ctx).WithFields(map[string]interface{}{
		"pix_key":       pixKey,
		"value":         value,
		"status_code":   res.StatusCode,
		"response_body": string(body),
	}).Info("Received withdrawal response from Woovi")

	if res.StatusCode != http.StatusOK {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"pix_key":       pixKey,
			"value":         value,
			"status_code":   res.StatusCode,
			"response_body": string(body),
		}).Error("Withdrawal request failed")
		return WithdrawResponse{}, fmt.Errorf("withdrawal request failed with status %d: %s", res.StatusCode, string(body))
	}

	var withdrawResponse WithdrawResponse
	if err := json.Unmarshal(body, &withdrawResponse); err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"pix_key":       pixKey,
			"response_body": string(body),
		}).Error("Error unmarshalling withdrawal response")
		return WithdrawResponse{}, err
	}

	log.WithContext(ctx).WithFields(map[string]interface{}{
		"pix_key":        pixKey,
		"value":          value,
		"status":         withdrawResponse.Transaction.Status,
		"correlation_id": withdrawResponse.Transaction.CorrelationID,
	}).Info("Successfully initiated withdrawal")

	return withdrawResponse, nil
}

func detectPixKeyType(key string) string {
	key = strings.TrimSpace(key)

	switch {
	case isValidCPF(key):
		return "CPF"
	case isValidCNPJ(key):
		return "CNPJ"
	case isValidEmail(key):
		return "EMAIL"
	case isValidPhoneNumber(key):
		return "PHONE"
	case isValidRandomKey(key):
		return "RANDOM"
	default:
		return "Invalid"
	}
}

type AutoPaymentRequest struct {
	QrCode        string `json:"qrCode"`
	Comment       string `json:"comment"`
	CorrelationID string `json:"correlationID"`
}

type AutoPayment struct {
	PixQrCode string
	OrderId   string
	Comment   string
}

// For development only. Should not use in production
func (w *Woovi) CreateAutoPaymentForPixQrCode(ctx context.Context, payload AutoPayment) (PixSuccessResponse, error) {
	endpoint := w.Url + "/payment"

	// Get logger instance
	log := logger.GetLogger()

	// Log payment creation start
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"payload":  payload,
		"endpoint": endpoint,
	}).Info("Starting auto payment creation")

	// Create payment request
	paymentRequest := AutoPaymentRequest{
		QrCode:        payload.PixQrCode,
		Comment:       payload.Comment,
		CorrelationID: payload.OrderId,
	}

	// Create HTTP request
	payloadBytes, err := json.Marshal(paymentRequest)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("Error marshalling payment request payload")
		return PixSuccessResponse{}, err
	}
	req, err := http.NewRequest(http.MethodPost, endpoint, bytes.NewReader(payloadBytes))
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"pix_qr_code":     payload.PixQrCode,
			"endpoint":        endpoint,
			"request_payload": string(payloadBytes),
		}).Error("Error creating HTTP request for payment")
		return PixSuccessResponse{}, err
	}

	// Set request headers
	req.Header.Add("content-type", "application/json")
	req.Header.Add("Authorization", w.APIKey)

	// Log payment request send
	log.WithContext(ctx).WithField("endpoint", endpoint).Info("Sending payment request to Woovi")

	// Make HTTP request
	createPaymentResponse, err := w.HTTPClient.Do(req)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"pix_qr_code":     payload.PixQrCode,
			"endpoint":        endpoint,
			"request_payload": string(payloadBytes),
		}).Error("Error making HTTP request to Woovi for payment")
		return PixSuccessResponse{}, err
	}

	defer createPaymentResponse.Body.Close()

	body, err := io.ReadAll(createPaymentResponse.Body)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithField("pix_qr_code", payload.PixQrCode).Error("Error reading response body from Woovi")
		return PixSuccessResponse{}, err
	}

	// Check status Code
	if createPaymentResponse.StatusCode != http.StatusOK {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"pix_qr_code":   payload.PixQrCode,
			"status_code":   createPaymentResponse.StatusCode,
			"response_body": string(body),
		}).Error("Payment request failed")
		return PixSuccessResponse{}, fmt.Errorf("payment request failed with status %d: %s", createPaymentResponse.StatusCode, string(body))
	}

	// Add random delay between 3-5 seconds before approve payment request
	delayMs := 3000 + rand.Intn(2001) // 3000-5000 milliseconds
	delayDuration := time.Duration(delayMs) * time.Millisecond

	log.WithContext(ctx).WithFields(map[string]interface{}{
		"delay_ms":       delayMs,
		"correlation_id": payload.OrderId,
	}).Info("Starting random delay before approve payment request")

	time.Sleep(delayDuration)

	log.WithContext(ctx).WithFields(map[string]interface{}{
		"delay_ms":       delayMs,
		"correlation_id": payload.OrderId,
	}).Info("Random delay completed, proceeding with approve payment request")

	// Approve created payment by CorrelationID (OrderID)
	approvePaymentRequest := struct {
		CorrelationID string `json:"correlationID"`
	}{
		CorrelationID: payload.OrderId,
	}

	approvePayloadBytes, err := json.Marshal(approvePaymentRequest)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("Error marshalling approve payment request payload")
		return PixSuccessResponse{}, err
	}

	approveEndpoint := w.Url + "/payment/approve"
	req, err = http.NewRequest(http.MethodPost, approveEndpoint, bytes.NewReader(approvePayloadBytes))
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"correlation_id":  payload.OrderId,
			"endpoint":        approveEndpoint,
			"request_payload": string(approvePayloadBytes),
		}).Error("Error creating HTTP request for approve payment")
		return PixSuccessResponse{}, err
	}

	// Set request headers
	req.Header.Add("content-type", "application/json")
	req.Header.Add("Authorization", w.APIKey)

	// Log approve payment request send
	log.WithContext(ctx).WithField("endpoint", approveEndpoint).Info("Sending approve payment request to Woovi")

	// Make HTTP request
	approvePaymentResponse, err := w.HTTPClient.Do(req)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithFields(map[string]interface{}{
			"correlation_id": payload.OrderId,
			"endpoint":       approveEndpoint,
		}).Error("Error making HTTP request to Woovi for approve payment")
		return PixSuccessResponse{}, err
	}

	defer approvePaymentResponse.Body.Close()

	body, err = io.ReadAll(approvePaymentResponse.Body)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithField("correlation_id", payload.OrderId).Error("Error reading response body from Woovi")
		return PixSuccessResponse{}, err
	}

	// Check status Code
	if approvePaymentResponse.StatusCode != http.StatusOK {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"correlation_id": payload.OrderId,
			"status_code":    approvePaymentResponse.StatusCode,
			"response_body":  string(body),
		}).Error("Approve payment request failed")
		return PixSuccessResponse{}, fmt.Errorf("approve payment request failed with status %d: %s", approvePaymentResponse.StatusCode, string(body))
	}

	// Log successful payment
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"pix_qr_code": payload.PixQrCode,
		"status":      "approved",
	}).Info("Successfully created auto payment")

	return PixSuccessResponse{}, nil
}
