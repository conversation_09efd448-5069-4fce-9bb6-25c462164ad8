package storage

import (
	"context"
	"fmt"
	"io"
	"log"
	"mime"
	"path/filepath"

	"github.com/aws/aws-sdk-go-v2/aws"
	aws_config "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/izy-mercado/backend/internal/config"
)

type S3Client interface {
	PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)
	DeleteObject(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error)
}

type Storage struct {
	client S3Client
	bucket string
}

// New initializes Storage with authentication & S3 client setup
func New(env config.Storage) (*Storage, error) {
	cfg, err := aws_config.LoadDefaultConfig(context.TODO(),
		aws_config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			env.ACCESS_KEY_ID,
			env.ACCESS_KEY_SECRET,
			"",
		)),
		aws_config.WithRegion("auto"),
	)
	if err != nil {
		log.Printf("unable to load AWS config: %v", err)
		return nil, err
	}

	client := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.BaseEndpoint = aws.String(fmt.Sprintf("https://%s.r2.cloudflarestorage.com", env.ACCOUNT_ID))
	})

	return &Storage{
		client: client,
		bucket: env.BUCKET_NAME,
	}, nil
}

// UploadImage uploads the file to S3 (Cloudflare R2) and returns the public URL
func (s *Storage) UploadImage(ctx context.Context, filename string, file io.ReadSeeker) (string, error) {
	contentType := mime.TypeByExtension(filepath.Ext(filename))
	if contentType == "" {
		contentType = "application/octet-stream" // Fallback for unknown types
	}

	_, err := s.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:             &s.bucket,
		Key:                &filename,
		Body:               file,
		CacheControl:       aws.String("no-store, no-cache, must-revalidate, max-age=0"),
		ContentType:        aws.String(contentType),
		ContentDisposition: aws.String("inline"),
	})

	if err != nil {
		log.Printf("unable to upload file: %v", err)
		return "", err
	}

	return fmt.Sprintf("https://%s.izymercado.com.br/%s", s.bucket, filename), nil
}

// DeleteImage deletes the file from S3 (Cloudflare R2)
func (s *Storage) DeleteImage(ctx context.Context, filename string) error {
	_, err := s.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: &s.bucket,
		Key:    &filename,
	})

	if err != nil {
		log.Printf("unable to delete file: %v", err)
		return err
	}

	return nil
}
