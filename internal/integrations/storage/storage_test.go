package storage

import (
	"bytes"
	"context"
	"errors"
	"mime/multipart"
	"testing"

	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type mockS3Client struct {
	putObjectFunc    func(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)
	deleteObjectFunc func(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error)
}

func (m *mockS3Client) DeleteObject(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error) {
	if m.deleteObjectFunc != nil {
		return m.deleteObjectFunc(ctx, params, optFns...)
	}
	return nil, errors.New("DeleteObject not implemented")
}

func (m *mockS3Client) PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error) {
	return m.putObjectFunc(ctx, params, optFns...)
}

type mockFile struct {
	*bytes.Reader
}

func (m *mockFile) Close() error { return nil }

func NewMockFile(data string) multipart.File {
	return &mockFile{Reader: bytes.NewReader([]byte(data))}
}

func TestUploadImage(t *testing.T) {
	tests := []struct {
		name     string
		mockFunc func(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)
		want     string
		wantErr  bool
	}{
		{
			name: "Successful upload",
			mockFunc: func(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error) {
				return &s3.PutObjectOutput{}, nil
			},
			want:    "https://test-bucket.izymercado.com.br/test-image.jpg",
			wantErr: false,
		},
		{
			name: "Failed upload due to S3 error",
			mockFunc: func(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error) {
				return nil, errors.New("S3 upload error")
			},
			want:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockClient := &mockS3Client{putObjectFunc: tt.mockFunc, deleteObjectFunc: nil}
			storage := &Storage{
				client: mockClient,
				bucket: "test-bucket",
			}

			got, err := storage.UploadImage(context.Background(), "test-image.jpg", NewMockFile("test data"))

			if (err != nil) != tt.wantErr {
				t.Errorf("%s: UploadImage() error = %v, wantErr %v", tt.name, err, tt.wantErr)
			}

			if got != tt.want {
				t.Errorf("%s: UploadImage() got = %v, want %v", tt.name, got, tt.want)
			}
		})
	}
}

func TestDeleteImage(t *testing.T) {
	tests := []struct {
		name     string
		mockFunc func(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error)
		wantErr  bool
	}{
		{
			name: "Successful delete",
			mockFunc: func(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error) {
				return &s3.DeleteObjectOutput{}, nil
			},
			wantErr: false,
		},
		{
			name: "Failed delete due to S3 error",
			mockFunc: func(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error) {
				return nil, errors.New("S3 delete error")
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockClient := &mockS3Client{putObjectFunc: nil, deleteObjectFunc: tt.mockFunc}
			storage := &Storage{
				client: mockClient,
				bucket: "test-bucket",
			}

			err := storage.DeleteImage(context.Background(), "test-image.jpg")

			if (err != nil) != tt.wantErr {
				t.Errorf("%s: DeleteImage() error = %v, wantErr %v", tt.name, err, tt.wantErr)
			}
		})
	}
}
