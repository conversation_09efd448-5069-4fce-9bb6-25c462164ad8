package mail

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
)

type Mail struct {
	Body        MailBody
	Url         string
	MailerToken string
}

type MailBody struct {
	From        From      `json:"from"`
	To          []To      `json:"to"`
	MergeInfo   MergeInfo `json:"merge_info"`
	Template<PERSON>ey string    `json:"mail_template_key"`
}

type From struct {
	Name    string `json:"name"`
	Address string `json:"address"`
}

type To struct {
	EmailAddress EmailAddress `json:"email_address"`
}

type EmailAddress struct {
	Address string `json:"address"`
}

type MergeInfo struct {
	Code string `json:"Code"`
	Name string `json:"Name"`
}

type MailerConfig struct {
	MailerURL   string
	MailerToken string
	From        string
	To          string
	Code        string
	UserName    string
	TemplateKey string
}

func New(cfg MailerConfig) *Mail {
	return &Mail{
		Body: MailBody{
			To:          []To{{EmailAddress: EmailAddress{Address: cfg.To}}},
			From:        From{Name: "Izy Mercado - Suporte", Address: "<EMAIL>"},
			MergeInfo:   MergeInfo{Code: cfg.Code, Name: cfg.UserName},
			TemplateKey: cfg.TemplateKey,
		},
		Url:         cfg.MailerURL,
		MailerToken: cfg.MailerToken,
	}

}

func (m *Mail) Send() error {
	client := &http.Client{}
	jsonData, err := json.Marshal(m.Body)
	if err != nil {
		log.Println("error while marshalling data: ", err)
		return err
	}

	req, err := http.NewRequest("POST", m.Url, strings.NewReader(string(jsonData)))
	if err != nil {
		log.Println("error while creating request: ", err)
		return err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", m.MailerToken)

	res, err := client.Do(req)
	if err != nil {
		log.Println("response error: ", err)
		return err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusCreated {
		resBody := make([]byte, 1000)
		res.Body.Read(resBody)
		log.Printf("Response error status:%d \n%s ", res.StatusCode, string(resBody))
		return fmt.Errorf("error while sending email: %v", res.Status)
	}

	return nil
}
