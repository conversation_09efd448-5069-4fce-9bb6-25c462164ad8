package middlewares_test

import (
	"net/http"
	"testing"

	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
)

func mockDB() *postgres.Queries {
	return &postgres.Queries{}
}

func TestValidateToken(t *testing.T) {
	m := middlewares.New(&config.Environment{}, mockDB())
	scenarios := map[string]struct {
		token string
		want  string
	}{
		"invalid token": {
			token: "Bearer 1234fsedfsdfsdfsdfsdf",
			want:  "invalid token",
		},
		"missing token": {
			token: "dasdasdasdas",
			want:  "missing token",
		},
	}

	req, _ := http.NewRequest("GET", "/", nil)

	for name, scenario := range scenarios {
		t.Run(name, func(t *testing.T) {
			req.Header.Set("Authorization", scenario.token)
			_, _, err := m.ValidateToken(req)
			if err.Error() != scenario.want {
				t.Errorf("ValidateToken() = %v, want %v", err.Error(), scenario.want)
			}
		})
	}

}
