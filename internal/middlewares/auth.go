package middlewares

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"slices"
	"time"

	"github.com/go-chi/jwtauth/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
)

type Middlewares struct {
	client  *http.Client
	queries *postgres.Queries
	env     *config.Environment
}

type User struct {
	ID              int32
	ExternalID      string
	IsActive        bool
	SessionID       int32
	ApplicationType string
	Name            string
	Email           string
	CPF             string
	Phones          []string
	Roles           []string
}

type contextKey string

const UserContextKey contextKey = "user"
const ApplicationTypeContextKey contextKey = "application_type"

func New(env *config.Environment, queries *postgres.Queries) *Middlewares {
	transport := http.DefaultTransport.(*http.Transport)
	transportCopy := &http.Transport{
		Proxy:                 transport.Proxy,
		DialContext:           transport.DialContext,
		MaxIdleConnsPerHost:   100,
		MaxIdleConns:          200,
		IdleConnTimeout:       60 * time.Second,
		TLSHandshakeTimeout:   transport.TLSHandshakeTimeout,
		ExpectContinueTimeout: transport.ExpectContinueTimeout,
		ResponseHeaderTimeout: transport.ResponseHeaderTimeout,
	}

	return &Middlewares{
		client: &http.Client{
			Transport: transportCopy,
			Timeout:   10 * time.Second,
		},
		queries: queries,
		env:     env,
	}
}

// ApplicationTypeMiddleware validates and extracts the X-Application-Type header
func (m *Middlewares) ApplicationTypeMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		applicationType := r.Header.Get("X-Application-Type")

		if applicationType == "" {
			common.RespondError(w, errors.New("missing X-Application-Type header"), http.StatusBadRequest)
			return
		}

		// Validate application type
		if applicationType != "mobile" && applicationType != "partner-web" {
			common.RespondError(w, errors.New("invalid X-Application-Type header. Must be 'mobile' or 'partner-web'"), http.StatusBadRequest)
			return
		}

		// Store application type in context
		ctx := context.WithValue(r.Context(), ApplicationTypeContextKey, applicationType)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AdminPermissions is a middleware that checks if the user has the admin permissions
func (m *Middlewares) AdminPermissions(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		user, res, err := m.ValidateToken(r)
		if err != nil {
			log.Println("Middleware: error validating token: ", err)
			common.RespondError(w, err, http.StatusUnauthorized)
			return
		}

		if !user.IsActive {
			common.RespondError(w, errors.New("user is not allowed"), http.StatusForbidden)
			return
		}

		// Validate admin role through roles array, not database boolean
		if !slices.Contains(user.Roles, "admin") {
			common.RespondError(w, errors.New("insufficient permissions: admin role required"), http.StatusForbidden)
			return
		}

		r = res
		next.ServeHTTP(w, r)
	})
}

func (m *Middlewares) PartnerPermissions(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		user, res, err := m.ValidateToken(r)
		if err != nil {
			log.Println("Middleware: error validating token: ", err)
			common.RespondError(w, err, http.StatusUnauthorized)
			return
		}

		if !user.IsActive {
			common.RespondError(w, errors.New("user is not allowed"), http.StatusForbidden)
			return
		}

		// Check if user has partner or admin role (admins have access to everything)
		if !slices.Contains(user.Roles, "partner") && !slices.Contains(user.Roles, "admin") {
			common.RespondError(w, errors.New("insufficient permissions: partner or admin role required"), http.StatusForbidden)
			return
		}

		r = res
		next.ServeHTTP(w, r)
	})
}

// DefaultPermissions is a middleware that checks if the user has user role (all authenticated users)
func (m *Middlewares) DefaultPermissions(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		user, res, err := m.ValidateToken(r)
		if err != nil {
			log.Println("Middleware: error validating token: ", err)
			common.RespondError(w, err, http.StatusUnauthorized)
			return
		}

		if !user.IsActive {
			common.RespondError(w, errors.New("user is not allowed"), http.StatusForbidden)
			return
		}

		// Check if user has user role (all authenticated users should have this)
		if !slices.Contains(user.Roles, "user") && !slices.Contains(user.Roles, "admin") {
			common.RespondError(w, errors.New("insufficient permissions: user role required"), http.StatusForbidden)
			return
		}

		r = res
		next.ServeHTTP(w, r)
	})
}

// ValidateToken validates the token and returns the user
func (m *Middlewares) ValidateToken(r *http.Request) (User, *http.Request, error) {
	tokenAuth := jwtauth.New("HS256", []byte(m.env.Core.JWT_SECRET), nil)
	tokenString := jwtauth.TokenFromHeader(r)

	if tokenString == "" {
		return User{}, r, errors.New("missing token")
	}

	token, err := jwtauth.VerifyToken(tokenAuth, tokenString)
	if err != nil {
		log.Println("Failed to verify token: ", err)
		return User{}, r, errors.New("invalid token")
	}

	// Validate token claims
	claims, err := token.AsMap(r.Context())
	if err != nil {
		log.Println("Failed to get token claims: ", err)
		return User{}, r, errors.New("invalid token")
	}

	// Check token type
	if claims["type"] != "access" {
		errMessage := "invalid token type. should be `access`"
		log.Println(errMessage)
		return User{}, r, errors.New(errMessage)
	}

	externalID, ok := token.Get("sub")
	if !ok {
		return User{}, r, errors.New("missing sub property in jwt")
	}

	// Get session by access token to ensure we have the correct session with application type
	session, err := m.queries.GetSessionByAccessToken(r.Context(), tokenString)
	if err != nil {
		return User{}, r, errors.New("no active session found for this token")
	}

	// Get user info using the user ID from the session (double validation)
	userInfo, err := m.queries.GetUserByExternalID(r.Context(), externalID.(string))
	if err != nil {
		fmt.Println("Failed to get user by external ID: ", err)
		if err.Error() == "no rows in result set" {
			return User{}, r, errors.New("user not found")
		}
		return User{}, r, err
	}

	// Verify that the session belongs to the user from the JWT
	if session.UserID != userInfo.ID {
		return User{}, r, errors.New("session user mismatch")
	}

	if len(userInfo.PhoneNumbers) == 0 {
		userInfo.PhoneNumbers = []string{""}
	}

	// Get user roles with proper []string type
	roles, err := m.queries.GetUserRoles(r.Context(), externalID.(string))
	if err != nil {
		// If no roles found, default to empty slice
		roles = []string{}
	}

	user := User{
		ID:              userInfo.ID,
		ExternalID:      externalID.(string),
		IsActive:        userInfo.IsActive,
		SessionID:       session.ID,
		ApplicationType: session.ApplicationType,
		Name:            userInfo.Name,
		Email:           userInfo.Email,
		CPF:             userInfo.Cpf,
		Phones:          userInfo.PhoneNumbers,
		Roles:           roles,
	}

	// Set the user ID in the request context
	ctx := r.Context()

	ctx = context.WithValue(ctx, UserContextKey, user)
	r = r.WithContext(ctx)

	return user, r, nil
}
