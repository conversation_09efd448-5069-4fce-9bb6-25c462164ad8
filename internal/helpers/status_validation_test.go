package helpers

import (
	"testing"
)

func TestValidateStatusUpdate_CancelledStatus(t *testing.T) {
	tests := []struct {
		name             string
		currentStatus    string
		newStatus        string
		isUserRequest    bool
		isCompanyRequest bool
		expectError      bool
		errorMessage     string
	}{
		// User cancellation tests
		{
			name:             "User can cancel pending invoice",
			currentStatus:    "pending",
			newStatus:        "cancelled",
			isUserRequest:    true,
			isCompanyRequest: false,
			expectError:      false,
		},
		{
			name:             "User cannot cancel processing invoice",
			currentStatus:    "processing",
			newStatus:        "cancelled",
			isUserRequest:    true,
			isCompanyRequest: false,
			expectError:      true,
			errorMessage:     "user can only cancel invoice when status is pending",
		},
		{
			name:             "User cannot cancel preparing invoice",
			currentStatus:    "preparing",
			newStatus:        "cancelled",
			isUserRequest:    true,
			isCompanyRequest: false,
			expectError:      true,
			errorMessage:     "user can only cancel invoice when status is pending",
		},
		{
			name:             "User cannot cancel ready invoice",
			currentStatus:    "ready",
			newStatus:        "cancelled",
			isUserRequest:    true,
			isCompanyRequest: false,
			expectError:      true,
			errorMessage:     "user can only cancel invoice when status is pending",
		},
		{
			name:             "User cannot cancel delivering invoice",
			currentStatus:    "delivering",
			newStatus:        "cancelled",
			isUserRequest:    true,
			isCompanyRequest: false,
			expectError:      true,
			errorMessage:     "user can only cancel invoice when status is pending",
		},
		{
			name:             "User cannot cancel completed invoice",
			currentStatus:    "completed",
			newStatus:        "cancelled",
			isUserRequest:    true,
			isCompanyRequest: false,
			expectError:      true,
			errorMessage:     "user can only cancel invoice when status is pending",
		},
		// Company cancellation tests
		{
			name:             "Company cannot cancel pending invoice",
			currentStatus:    "pending",
			newStatus:        "cancelled",
			isUserRequest:    false,
			isCompanyRequest: true,
			expectError:      true,
			errorMessage:     "company can only cancel invoice when status is processing or preparing",
		},
		{
			name:             "Company can cancel processing invoice",
			currentStatus:    "processing",
			newStatus:        "cancelled",
			isUserRequest:    false,
			isCompanyRequest: true,
			expectError:      false,
		},
		{
			name:             "Company can cancel preparing invoice",
			currentStatus:    "preparing",
			newStatus:        "cancelled",
			isUserRequest:    false,
			isCompanyRequest: true,
			expectError:      false,
		},
		{
			name:             "Company cannot cancel ready invoice",
			currentStatus:    "ready",
			newStatus:        "cancelled",
			isUserRequest:    false,
			isCompanyRequest: true,
			expectError:      true,
			errorMessage:     "company can only cancel invoice when status is processing or preparing",
		},
		{
			name:             "Company cannot cancel delivering invoice",
			currentStatus:    "delivering",
			newStatus:        "cancelled",
			isUserRequest:    false,
			isCompanyRequest: true,
			expectError:      true,
			errorMessage:     "company can only cancel invoice when status is processing or preparing",
		},
		{
			name:             "Company cannot cancel completed invoice",
			currentStatus:    "completed",
			newStatus:        "cancelled",
			isUserRequest:    false,
			isCompanyRequest: true,
			expectError:      true,
			errorMessage:     "company can only cancel invoice when status is processing or preparing",
		},
		// Authorization tests
		{
			name:             "Cannot cancel without user or company authorization",
			currentStatus:    "pending",
			newStatus:        "cancelled",
			isUserRequest:    false,
			isCompanyRequest: false,
			expectError:      true,
			errorMessage:     "cancellation requires user or company authorization",
		},
		// Valid status tests
		{
			name:             "Cancelled status is valid",
			currentStatus:    "pending",
			newStatus:        "cancelled",
			isUserRequest:    true,
			isCompanyRequest: false,
			expectError:      false,
		},
		// Failed vs Cancelled separation tests
		{
			name:             "Failed status still works for system failures",
			currentStatus:    "processing",
			newStatus:        "failed",
			isUserRequest:    false,
			isCompanyRequest: false,
			expectError:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateStatusUpdate(tt.currentStatus, tt.newStatus, tt.isUserRequest, tt.isCompanyRequest)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errorMessage != "" && err.Error() != tt.errorMessage {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMessage, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestGetStatusDescription_CancelledStatus(t *testing.T) {
	tests := []struct {
		status   string
		expected string
	}{
		{"pending", "Aguardando pagamento"},
		{"failed", "Falha no pagamento"},
		{"expired", "Pagamento expirou"},
		{"processing", "Pagamento aprovado - Aguardando aceite do supermercado"},
		{"preparing", "Aceito pelo supermercado - Em processo de separação"},
		{"ready", "Pronto para retirada/entrega"},
		{"delivering", "Em rota de entrega"},
		{"completed", "Pedido finalizado"},
		{"cancelled", "Pedido cancelado"},
		{"unknown", "Status desconhecido"},
	}

	for _, tt := range tests {
		t.Run(tt.status, func(t *testing.T) {
			result := GetStatusDescription(tt.status)
			if result != tt.expected {
				t.Errorf("Expected '%s', got '%s'", tt.expected, result)
			}
		})
	}
}

func TestValidateStatusUpdate_NormalTransitions(t *testing.T) {
	// Test that normal status transitions still work correctly
	tests := []struct {
		name          string
		currentStatus string
		newStatus     string
		expectError   bool
	}{
		{"Pending to processing", "pending", "processing", false},
		{"Processing to preparing", "processing", "preparing", false},
		{"Preparing to ready", "preparing", "ready", false},
		{"Ready to delivering", "ready", "delivering", false},
		{"Delivering to completed", "delivering", "completed", false},
		{"Invalid transition", "completed", "pending", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateStatusUpdate(tt.currentStatus, tt.newStatus, false, false)

			if tt.expectError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}
