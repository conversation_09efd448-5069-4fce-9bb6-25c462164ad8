package helpers

import (
	"encoding/json"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestValidateSearchQuery(t *testing.T) {
	helper := New()

	tests := []struct {
		name        string
		query       string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid query - name",
			query:       "<PERSON>",
			expectError: false,
		},
		{
			name:        "Valid query - email",
			query:       "<EMAIL>",
			expectError: false,
		},
		{
			name:        "Valid query - CPF",
			query:       "12345678901",
			expectError: false,
		},
		{
			name:        "Valid query - with dots and hyphens",
			query:       "john.doe-123",
			expectError: false,
		},
		{
			name:        "Query too short",
			query:       "a",
			expectError: true,
			errorMsg:    "query must be at least 2 characters long",
		},
		{
			name:        "Empty query",
			query:       "",
			expectError: true,
			errorMsg:    "query must be at least 2 characters long",
		},
		{
			name:        "Query too long",
			query:       "this is a very long query that exceeds the maximum allowed length of 100 characters and should be rejected by the validation function",
			expectError: true,
			errorMsg:    "query must be at most 100 characters long",
		},
		{
			name:        "Query with SQL injection attempt - single quote",
			query:       "john'; DROP TABLE users; --",
			expectError: true,
			errorMsg:    "query contains invalid characters or patterns",
		},
		{
			name:        "Query with SQL injection attempt - double quote",
			query:       "john\" OR 1=1",
			expectError: true,
			errorMsg:    "query contains invalid characters or patterns",
		},
		{
			name:        "Query with SQL injection attempt - semicolon",
			query:       "john; DELETE FROM users",
			expectError: true,
			errorMsg:    "query contains invalid characters or patterns",
		},
		{
			name:        "Query with SQL injection attempt - comment",
			query:       "john -- comment",
			expectError: true,
			errorMsg:    "query contains invalid characters or patterns",
		},
		{
			name:        "Query with SQL injection attempt - SELECT",
			query:       "john UNION SELECT * FROM users",
			expectError: true,
			errorMsg:    "query contains invalid characters or patterns",
		},
		{
			name:        "Query with invalid characters - special symbols",
			query:       "john$%^&*()",
			expectError: true,
			errorMsg:    "query contains invalid characters",
		},
		{
			name:        "Query with invalid characters - brackets",
			query:       "john[test]",
			expectError: true,
			errorMsg:    "query contains invalid characters",
		},
		{
			name:        "Query with invalid characters - backslash",
			query:       "john\\test",
			expectError: true,
			errorMsg:    "query contains invalid characters",
		},
		{
			name:        "Valid minimum length query",
			query:       "jo",
			expectError: false,
		},
		{
			name:        "Valid query with numbers",
			query:       "john123",
			expectError: false,
		},
		{
			name:        "Valid query with spaces",
			query:       "john doe smith",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := helper.ValidateSearchQuery(tt.query)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateSearchQuery_EdgeCases(t *testing.T) {
	helper := New()

	// Create exactly 100 characters using strings.Repeat
	query100 := strings.Repeat("a", 100)
	assert.Len(t, query100, 100)
	err := helper.ValidateSearchQuery(query100)
	assert.NoError(t, err)

	// Test 101 characters (should be invalid)
	query101 := strings.Repeat("a", 101)
	assert.Len(t, query101, 101)
	err = helper.ValidateSearchQuery(query101)
	assert.Error(t, err)
	if err != nil {
		assert.Contains(t, err.Error(), "query must be at most 100 characters long")
	}
}

func TestCreatePaymentInfoDetails(t *testing.T) {
	brCode := "test-br-code"
	qrCodeImage := "test-qr-code-image"
	paymentLinkID := "test-payment-link-id"

	result, err := CreatePaymentInfoDetails(brCode, qrCodeImage, paymentLinkID)
	assert.NoError(t, err)

	// Parse the result to verify structure
	var infoDetails InfoDetails
	err = json.Unmarshal(result, &infoDetails)
	assert.NoError(t, err)

	assert.NotNil(t, infoDetails.Payment)
	assert.Equal(t, brCode, infoDetails.Payment.BrCode)
	assert.Equal(t, qrCodeImage, infoDetails.Payment.QrCodeImage)
	assert.Equal(t, paymentLinkID, infoDetails.Payment.PaymentLinkID)
}

func TestParsePaymentData(t *testing.T) {
	// Create test data
	testData := InfoDetails{
		Payment: &PaymentData{
			BrCode:        "test-br-code",
			QrCodeImage:   "test-qr-code-image",
			PaymentLinkID: "test-payment-link-id",
		},
	}

	jsonData, err := json.Marshal(testData)
	assert.NoError(t, err)

	// Test parsing
	result, err := ParsePaymentData(jsonData)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	assert.Equal(t, "test-br-code", result.BrCode)
	assert.Equal(t, "test-qr-code-image", result.QrCodeImage)
	assert.Equal(t, "test-payment-link-id", result.PaymentLinkID)
}

func TestParsePaymentData_EmptyData(t *testing.T) {
	result, err := ParsePaymentData([]byte{})
	assert.NoError(t, err)
	assert.Nil(t, result)
}
