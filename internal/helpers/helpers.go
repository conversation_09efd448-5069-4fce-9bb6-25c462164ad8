package helpers

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"strconv"
	"strings"
	"time"

	"math/rand"

	"slices"

	"github.com/go-playground/validator/v10"
	"github.com/oklog/ulid/v2"
)

var validUrls = []string{
	"https://images.izymercado.com.br/01JWYHVDV4PPZJ7ABZK3H4QAG9.png",
	"https://images.izymercado.com.br/014KG56DC01GG4TEB01ZEX7WFJ.png",
	"https://images.izymercado.com.br/01JTGX3C4Z60M9VM7E3VYT167K.png",
	"https://images.izymercado.com.br/01JTGX3VVZ7G1HBR81QTV4WDTF.png",
	"https://images.izymercado.com.br/01JTGX4419BDQYRT03HMTKNAHM.png",
	"https://images.izymercado.com.br/01JTGX4BP9TPFCSCK9Z9CQ56V8.png",
	"https://images.izymercado.com.br/01JTGX4H77S2K2E5QWD7TMZ2CZ.png",
	"https://images.izymercado.com.br/01JTGX4PEJTZB9M2B5W868F079.png",
	"https://images.izymercado.com.br/01JTGX4W5R573WQSXJ95V04D99.png",
}

type ValidationError struct {
	Field string
	Tag   string
	Value string
}

type Helper struct{}

func New() *Helper {
	return &Helper{}
}

func (h *Helper) ValidateRequest(s interface{}) []ValidationError {
	// validate request
	validate := validator.New()
	err := validate.Struct(s)
	if err != nil {
		var errors []ValidationError
		for _, err := range err.(validator.ValidationErrors) {
			var el ValidationError
			el.Field = err.Field()
			el.Tag = err.Tag()
			el.Value = err.Param()
			errors = append(errors, el)
		}
		return errors
	}
	return nil
}

func (h *Helper) GenerateULIDV2() string {
	return ulid.Make().String()
}

// Invoice utility functions

// NullStringToPtr converts a nullable interface to a string pointer
func NullStringToPtr(ns interface{}) *string {
	if ns == nil {
		return nil
	}
	if str, ok := ns.(string); ok && str != "" {
		return &str
	}
	return nil
}

// NullTimeToPtr converts a nullable time interface to a string pointer
func NullTimeToPtr(nt interface{}) *string {
	if nt == nil {
		return nil
	}
	// Handle different time types that might come from the database
	switch t := nt.(type) {
	case string:
		if t != "" {
			return &t
		}
	}
	return nil
}

// ValidateStatusUpdate validates a status update request
func ValidateStatusUpdate(currentStatus, newStatus string, isUserRequest bool, isCompanyRequest bool) error {
	// Check if new status is valid
	validStatuses := []string{"pending", "failed", "expired", "processing", "preparing", "ready", "delivering", "completed", "cancelled"}
	isValid := false
	for _, status := range validStatuses {
		if status == newStatus {
			isValid = true
			break
		}
	}
	if !isValid {
		return fmt.Errorf("invalid status value")
	}

	// Special validation for cancellation requests first
	if newStatus == "cancelled" {
		if isUserRequest {
			// Users can only cancel invoices when status is "pending"
			if currentStatus != "pending" {
				return fmt.Errorf("user can only cancel invoice when status is pending")
			}
		} else if isCompanyRequest {
			// Partners/companies can cancel invoices when status is "processing" or "preparing"
			companyCancellableStatuses := []string{"processing", "preparing"}
			canCancel := false
			for _, status := range companyCancellableStatuses {
				if status == currentStatus {
					canCancel = true
					break
				}
			}
			if !canCancel {
				return fmt.Errorf("company can only cancel invoice when status is processing or preparing")
			}
		} else {
			// Prevent cancellation if neither user nor company request
			return fmt.Errorf("cancellation requires user or company authorization")
		}
		// If we reach here, the cancellation is authorized, so allow it
		return nil
	}

	// Check if transition is allowed for non-cancellation requests
	allowedTransitions := map[string][]string{
		"pending":    {"failed", "expired", "processing"},
		"processing": {"failed", "expired", "preparing"},
		"preparing":  {"ready", "failed", "delivering"},
		"ready":      {"delivering", "completed", "failed"},
		"delivering": {"completed", "failed"},
		"completed":  {},
		"failed":     {},
		"expired":    {},
		"cancelled":  {},
	}

	allowed, exists := allowedTransitions[currentStatus]
	if !exists {
		return fmt.Errorf("invalid current status")
	}

	canTransition := false
	for _, allowedStatus := range allowed {
		if allowedStatus == newStatus {
			canTransition = true
			break
		}
	}
	if !canTransition {
		return fmt.Errorf("invalid status transition")
	}

	return nil
}

// GetStatusDescription returns a human-readable description of the status
func GetStatusDescription(status string) string {
	descriptions := map[string]string{
		"pending":    "Aguardando pagamento",
		"failed":     "Falha no pagamento",
		"expired":    "Pagamento expirou",
		"processing": "Pagamento aprovado - Aguardando aceite do supermercado",
		"preparing":  "Aceito pelo supermercado - Em processo de separação",
		"ready":      "Pronto para retirada/entrega",
		"delivering": "Em rota de entrega",
		"completed":  "Pedido finalizado",
		"cancelled":  "Pedido cancelado",
	}

	if desc, exists := descriptions[status]; exists {
		return desc
	}
	return "Status desconhecido"
}

func (h *Helper) IsULIDv2(s string) (bool, error) {
	// ULID deve ter 26 caracteres base32 (Crockford)
	if len(s) != 26 {
		return false, errors.New("invalid length")
	}

	// Tentar parsear como ULID
	id, err := ulid.ParseStrict(s)
	if err != nil {
		return false, err
	}

	// Verificar byte de versão: ULIDv2 tem 0x02 no byte 0
	// ULID é 16 bytes: [version|timestamp|entropy]
	bytes := id.Bytes()
	if len(bytes) != 16 {
		return false, errors.New("invalid ULID byte length")
	}

	return true, nil
}

func (h *Helper) GenerateRandomFiveDigitsCode() string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	otp := r.Intn(100000)
	return fmt.Sprintf("%05d", otp)
}

func (h *Helper) IsValidCPF(cpf string) bool {
	if len(cpf) != 11 {
		return false
	}

	digits := make([]int, 11)
	for i := 0; i < 11; i++ {
		d, err := strconv.Atoi(string(cpf[i]))
		if err != nil {
			return false
		}
		digits[i] = d
	}

	same := true
	for i := 1; i < 11; i++ {
		if digits[i] != digits[0] {
			same = false
			break
		}
	}
	if same {
		return false
	}

	sum := 0
	for i := 0; i < 9; i++ {
		sum += digits[i] * (10 - i)
	}
	firstCheck := (sum * 10) % 11
	if firstCheck == 10 {
		firstCheck = 0
	}
	if firstCheck != digits[9] {
		return false
	}

	sum = 0
	for i := 0; i < 10; i++ {
		sum += digits[i] * (11 - i)
	}
	secondCheck := (sum * 10) % 11
	if secondCheck == 10 {
		secondCheck = 0
	}
	return secondCheck == digits[10]
}

func (h *Helper) IsValidCNPJ(cnpj string) bool {
	if len(cnpj) != 14 {
		return false
	}

	digits := make([]int, 14)
	for i := 0; i < 14; i++ {
		d, err := strconv.Atoi(string(cnpj[i]))
		if err != nil {
			return false
		}
		digits[i] = d
	}

	same := true
	for i := 1; i < 14; i++ {
		if digits[i] != digits[0] {
			same = false
			break
		}
	}
	if same {
		return false
	}

	weights1 := []int{5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2}
	sum := 0
	for i := 0; i < 12; i++ {
		sum += digits[i] * weights1[i]
	}
	firstCheck := sum % 11
	if firstCheck < 2 {
		firstCheck = 0
	} else {
		firstCheck = 11 - firstCheck
	}
	if firstCheck != digits[12] {
		return false
	}

	weights2 := []int{6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2}
	sum = 0
	for i := 0; i < 13; i++ {
		sum += digits[i] * weights2[i]
	}
	secondCheck := sum % 11
	if secondCheck < 2 {
		secondCheck = 0
	} else {
		secondCheck = 11 - secondCheck
	}
	return secondCheck == digits[13]
}

func DeferParts(mr *multipart.Reader) (multipart.File, *multipart.FileHeader, map[string]string, error) {

	var (
		file        multipart.File
		fileHeader  *multipart.FileHeader
		formValues  = make(map[string]string)
		multiValues = make(map[string][]string) // Track multiple values for same key
	)

	for {
		part, err := mr.NextPart()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, nil, nil, fmt.Errorf("error reading multipart data: %v", err)
		}

		name := part.FormName()
		if name == "image" {
			buf := &bytes.Buffer{}
			_, err := io.Copy(buf, part)
			if err != nil {
				return nil, nil, nil, fmt.Errorf("error reading image: %v", err)
			}

			reader := bytes.NewReader(buf.Bytes())
			file = struct {
				io.Reader
				io.ReaderAt
				io.Seeker
				io.Closer
			}{
				Reader:   reader,
				ReaderAt: reader,
				Seeker:   reader,
				Closer:   io.NopCloser(reader),
			}

			fileHeader = &multipart.FileHeader{
				Filename: part.FileName(),
				Header:   part.Header,
			}
		} else {
			value, _ := io.ReadAll(part)
			valueStr := string(value)

			// Check if this key already exists
			if existingValue, exists := formValues[name]; exists {
				// If it exists, track multiple values
				if _, tracked := multiValues[name]; !tracked {
					// First time seeing multiple values, add the existing value
					multiValues[name] = []string{existingValue}
				}
				// Add the new value
				multiValues[name] = append(multiValues[name], valueStr)
				// Update formValues with comma-separated values
				formValues[name] = strings.Join(multiValues[name], ",")
			} else {
				// First occurrence of this key
				formValues[name] = valueStr
			}
		}
		part.Close()

	}
	return file, fileHeader, formValues, nil
}

func (h *Helper) CheckIconURL(url string) error {
	if slices.Contains(validUrls, url) {
		return nil
	}

	return fmt.Errorf("icon not found")
}

func (h *Helper) GetIconURLs() []string {
	return validUrls
}

// ParseJSONB transforma um campo JSONB em uma estrutura genérica.
func ParseJSONB[T any](data []byte) ([]T, error) {
	var result []T
	if err := json.Unmarshal(data, &result); err != nil {
		log.Printf("Failed to unmarshal JSONB into %T: %v\n", result, err)
		return nil, err
	}
	return result, nil
}

// ParseInt parses a string to int with a default value if the string is empty or invalid
func (h *Helper) ParseInt(value string, defaultValue int) (int, error) {
	if value == "" {
		return defaultValue, nil
	}
	return strconv.Atoi(value)
}

// FormatAddressAsText formats an address into a readable text string
func (h *Helper) FormatAddressAsText(street, number, complement, neighborhood, city, state, zipCode string) string {
	var parts []string

	// Add street and number
	if street != "" && number != "" {
		parts = append(parts, street+", "+number)
	} else if street != "" {
		parts = append(parts, street)
	}

	// Add complement if exists
	if complement != "" {
		parts = append(parts, complement)
	}

	// Add neighborhood
	if neighborhood != "" {
		parts = append(parts, neighborhood)
	}

	// Add city and state
	if city != "" && state != "" {
		parts = append(parts, city+" - "+state)
	} else if city != "" {
		parts = append(parts, city)
	}

	// Add zip code
	if zipCode != "" {
		parts = append(parts, "CEP: "+zipCode)
	}

	return strings.Join(parts, ", ")
}

// ValidateSearchQuery validates a search query to prevent SQL injection and ensure it's safe
func (h *Helper) ValidateSearchQuery(query string) error {
	// Check for minimum length
	if len(query) < 2 {
		return fmt.Errorf("query must be at least 2 characters long")
	}

	// Check for maximum length to prevent abuse
	if len(query) > 100 {
		return fmt.Errorf("query must be at most 100 characters long")
	}

	// Check for dangerous SQL characters/patterns
	dangerousPatterns := []string{
		"'", "\"", ";", "--", "/*", "*/", "xp_", "sp_", "DROP", "DELETE", "INSERT", "UPDATE", "UNION", "SELECT",
	}

	queryUpper := strings.ToUpper(query)
	for _, pattern := range dangerousPatterns {
		if strings.Contains(queryUpper, strings.ToUpper(pattern)) {
			return fmt.Errorf("query contains invalid characters or patterns")
		}
	}

	// Allow only alphanumeric characters, spaces, dots, hyphens, and @ symbol (for emails)
	for _, char := range query {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == ' ' || char == '.' || char == '-' || char == '@') {
			return fmt.Errorf("query contains invalid characters")
		}
	}

	return nil
}

// convertNullableCoordinates converts sql.NullFloat64 latitude and longitude to *float64 pointers
// Returns nil pointers if the database values are null
func (h *Helper) ConvertNullableCoordinates(lat, lng sql.NullFloat64) (*float64, *float64) {
	var latitude, longitude *float64
	if lat.Valid {
		latitude = &lat.Float64
	}
	if lng.Valid {
		longitude = &lng.Float64
	}
	return latitude, longitude
}

// PaymentData represents the payment information stored in info_details
type PaymentData struct {
	BrCode        string `json:"brCode"`
	QrCodeImage   string `json:"qrCodeImage"`
	PaymentLinkID string `json:"paymentLinkID,omitempty"`
}

// InfoDetails represents the structure of the info_details JSONB column
type InfoDetails struct {
	Payment *PaymentData `json:"payment,omitempty"`
}

// CreatePaymentInfoDetails creates a JSONB structure for payment data
func CreatePaymentInfoDetails(brCode, qrCodeImage, paymentLinkID string) ([]byte, error) {
	infoDetails := InfoDetails{
		Payment: &PaymentData{
			BrCode:        brCode,
			QrCodeImage:   qrCodeImage,
			PaymentLinkID: paymentLinkID,
		},
	}
	return json.Marshal(infoDetails)
}

// ParsePaymentData extracts payment data from info_details JSONB
func ParsePaymentData(infoDetailsBytes []byte) (*PaymentData, error) {
	if len(infoDetailsBytes) == 0 {
		return nil, nil
	}

	var infoDetails InfoDetails
	if err := json.Unmarshal(infoDetailsBytes, &infoDetails); err != nil {
		return nil, err
	}

	return infoDetails.Payment, nil
}
