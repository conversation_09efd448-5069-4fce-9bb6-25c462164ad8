package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"math/big"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/jackc/pgtype"
	"github.com/stretchr/testify/assert"
)

// MockPriceComparisonQueries is a mock for testing price comparison with location filtering
type MockPriceComparisonQueries struct {
	recommendedComparison         []postgres.GetRecommendedPriceComparisonRow
	recommendedComparisonLocation []postgres.GetRecommendedPriceComparisonWithLocationRow
	defaultComparison             []postgres.GetDefaultPriceComparisonRow
	defaultComparisonLocation     []postgres.GetDefaultPriceComparisonWithLocationRow
	hasAccess                     bool
	err                           error
}

func (m *MockPriceComparisonQueries) CheckIfUserProductsListBelongsToUser(ctx context.Context, arg postgres.CheckIfUserProductsListBelongsToUserParams) (bool, error) {
	return m.hasAccess, m.err
}

func (m *MockPriceComparisonQueries) GetRecommendedPriceComparison(ctx context.Context, externalID string) ([]postgres.GetRecommendedPriceComparisonRow, error) {
	return m.recommendedComparison, m.err
}

func (m *MockPriceComparisonQueries) GetRecommendedPriceComparisonWithLocation(ctx context.Context, arg postgres.GetRecommendedPriceComparisonWithLocationParams) ([]postgres.GetRecommendedPriceComparisonWithLocationRow, error) {
	return m.recommendedComparisonLocation, m.err
}

func (m *MockPriceComparisonQueries) GetDefaultPriceComparison(ctx context.Context, arg postgres.GetDefaultPriceComparisonParams) ([]postgres.GetDefaultPriceComparisonRow, error) {
	return m.defaultComparison, m.err
}

func (m *MockPriceComparisonQueries) GetDefaultPriceComparisonWithLocation(ctx context.Context, arg postgres.GetDefaultPriceComparisonWithLocationParams) ([]postgres.GetDefaultPriceComparisonWithLocationRow, error) {
	return m.defaultComparisonLocation, m.err
}

// testPriceComparisonHandler is a test version of PriceComparisonHandler for location filtering
type testPriceComparisonHandler struct {
	mockQueries *MockPriceComparisonQueries
}

// GetRecommendations is a simplified version for testing location-based filtering
func (h *testPriceComparisonHandler) GetRecommendations(w http.ResponseWriter, r *http.Request) {
	listExternalID := chi.URLParam(r, "list_external_id")
	if listExternalID == "" {
		http.Error(w, `{"code":"001","message":"list_external_id is required"}`, http.StatusBadRequest)
		return
	}

	// Parse location parameters
	latStr := r.URL.Query().Get("lat")
	longStr := r.URL.Query().Get("long")

	// Check if location parameters are provided
	hasLocationParams := latStr != "" && longStr != ""

	// If only one coordinate is provided, return empty array (fast fail)
	if (latStr != "" && longStr == "") || (latStr == "" && longStr != "") {
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode([]GetRecommendedPriceComparisonResponse{})
		return
	}

	var lat, long float64
	var err error

	// Parse and validate coordinates if provided
	if hasLocationParams {
		lat, err = strconv.ParseFloat(latStr, 64)
		if err != nil {
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode([]GetRecommendedPriceComparisonResponse{})
			return
		}

		long, err = strconv.ParseFloat(longStr, 64)
		if err != nil {
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode([]GetRecommendedPriceComparisonResponse{})
			return
		}

		// Validate coordinate ranges
		if lat < -90 || lat > 90 || long < -180 || long > 180 {
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode([]GetRecommendedPriceComparisonResponse{})
			return
		}
	}

	// Check access (simplified for test)
	hasAccess, err := h.mockQueries.CheckIfUserProductsListBelongsToUser(r.Context(), postgres.CheckIfUserProductsListBelongsToUserParams{})
	if err != nil || !hasAccess {
		http.Error(w, `{"code":"001","message":"access denied"}`, http.StatusForbidden)
		return
	}

	var recommendations []GetRecommendedPriceComparisonResponse

	if hasLocationParams {
		// Use location-based query
		result, err := h.mockQueries.GetRecommendedPriceComparisonWithLocation(r.Context(), postgres.GetRecommendedPriceComparisonWithLocationParams{})
		if err != nil {
			http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
			return
		}

		recommendations = make([]GetRecommendedPriceComparisonResponse, len(result))
		for i, o := range result {
			recommendations[i] = GetRecommendedPriceComparisonResponse{
				ExternalID:            o.CompanyID,
				Name:                  o.CompanyName,
				Picture:               o.CompanyPicture,
				TotalPrice:            int(o.TotalPrice),
				ShippingFee:           o.CompanyShippingFee,
				DeliveryModes:         o.CompanyDeliveryModes,
				MatchedProductsCount:  int(o.MatchedProductsCount),
				UnmatchedProductCount: int(o.UnmatchedProductCount),
			}
		}
	} else {
		// Use regular query without location filtering
		result, err := h.mockQueries.GetRecommendedPriceComparison(r.Context(), listExternalID)
		if err != nil {
			http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
			return
		}

		recommendations = make([]GetRecommendedPriceComparisonResponse, len(result))
		for i, o := range result {
			recommendations[i] = GetRecommendedPriceComparisonResponse{
				ExternalID:            o.CompanyID,
				Name:                  o.CompanyName,
				Picture:               o.CompanyPicture,
				TotalPrice:            int(o.TotalPrice),
				ShippingFee:           o.CompanyShippingFee,
				DeliveryModes:         o.CompanyDeliveryModes,
				MatchedProductsCount:  int(o.MatchedProductsCount),
				UnmatchedProductCount: int(o.UnmatchedProductCount),
			}
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(recommendations)
}

func TestGetRecommendationsWithLocationFiltering(t *testing.T) {
	tests := []struct {
		name                      string
		queryParams               map[string]string
		mockRecommendedComparison []postgres.GetRecommendedPriceComparisonRow
		mockRecommendedLocation   []postgres.GetRecommendedPriceComparisonWithLocationRow
		mockHasAccess             bool
		mockError                 error
		expectedStatus            int
		expectedCount             int
		expectLocationQuery       bool
	}{
		{
			name:        "No location parameters - use regular query",
			queryParams: map[string]string{},
			mockRecommendedComparison: []postgres.GetRecommendedPriceComparisonRow{
				{
					CompanyID:             "company-1",
					CompanyName:           "Company 1",
					CompanyPicture:        "https://example.com/1.jpg",
					CompanyShippingFee:    500,
					CompanyDeliveryModes:  []string{"delivery"},
					CompanyLatitude:       sql.NullFloat64{Float64: -5.7945, Valid: true},
					CompanyLongitude:      sql.NullFloat64{Float64: -35.2110, Valid: true},
					TotalPrice:            2500,
					MatchedProductsCount:  3,
					MatchedProducts:       []byte(`[]`),
					UnmatchedProducts:     []byte(`[]`),
					UnmatchedProductCount: 0,
				},
			},
			mockHasAccess:       true,
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       1,
			expectLocationQuery: false,
		},
		{
			name: "Valid coordinates - use location-based query",
			queryParams: map[string]string{
				"lat":  "-5.7945",
				"long": "-35.2110",
			},
			mockRecommendedLocation: []postgres.GetRecommendedPriceComparisonWithLocationRow{
				{
					CompanyID:             "nearby-company",
					CompanyName:           "Nearby Company",
					CompanyPicture:        "https://example.com/nearby.jpg",
					CompanyShippingFee:    300,
					CompanyDeliveryModes:  []string{"delivery", "pickup"},
					CompanyLatitude:       sql.NullFloat64{Float64: -5.7945, Valid: true},
					CompanyLongitude:      sql.NullFloat64{Float64: -35.2110, Valid: true},
					TotalPrice:            2000,
					MatchedProductsCount:  5,
					MatchedProducts:       []byte(`[]`),
					UnmatchedProducts:     []byte(`[]`),
					UnmatchedProductCount: 0,
					DistanceKm:            pgtype.Numeric{Int: big.NewInt(250), Exp: -2, Status: pgtype.Present}, // 2.5 km
				},
			},
			mockHasAccess:       true,
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       1,
			expectLocationQuery: true,
		},
		{
			name: "Only latitude provided - return empty array",
			queryParams: map[string]string{
				"lat": "-5.7945",
			},
			mockHasAccess:       true,
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       0,
			expectLocationQuery: false,
		},
		{
			name: "Invalid latitude - return empty array",
			queryParams: map[string]string{
				"lat":  "invalid",
				"long": "-35.2110",
			},
			mockHasAccess:       true,
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       0,
			expectLocationQuery: false,
		},
		{
			name: "Latitude out of range - return empty array",
			queryParams: map[string]string{
				"lat":  "95.0",
				"long": "-35.2110",
			},
			mockHasAccess:       true,
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       0,
			expectLocationQuery: false,
		},
		{
			name: "Valid coordinates but no companies within range",
			queryParams: map[string]string{
				"lat":  "-5.7945",
				"long": "-35.2110",
			},
			mockRecommendedLocation: []postgres.GetRecommendedPriceComparisonWithLocationRow{},
			mockHasAccess:           true,
			mockError:               nil,
			expectedStatus:          http.StatusOK,
			expectedCount:           0,
			expectLocationQuery:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockPriceComparisonQueries{
				recommendedComparison:         tt.mockRecommendedComparison,
				recommendedComparisonLocation: tt.mockRecommendedLocation,
				hasAccess:                     tt.mockHasAccess,
				err:                           tt.mockError,
			}

			// Create test handler
			testHandler := &testPriceComparisonHandler{
				mockQueries: mockQueries,
			}

			// Build URL with query parameters
			url := "/v1/price-comparison/recommendations/list_123"
			if len(tt.queryParams) > 0 {
				url += "?"
				params := make([]string, 0, len(tt.queryParams))
				for key, value := range tt.queryParams {
					params = append(params, key+"="+value)
				}
				url += strings.Join(params, "&")
			}

			// Create request with chi context
			req := httptest.NewRequest("GET", url, nil)
			rctx := chi.NewRouteContext()
			rctx.URLParams.Add("list_external_id", "list_123")
			req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

			w := httptest.NewRecorder()

			// Call handler
			testHandler.GetRecommendations(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response []GetRecommendedPriceComparisonResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check response structure
				assert.Equal(t, tt.expectedCount, len(response))

				// Verify correct query was used based on expected behavior
				if tt.expectLocationQuery && tt.expectedCount > 0 {
					// Should have used location-based query and returned companies
					assert.Greater(t, len(response), 0)
					assert.NotEmpty(t, response[0].ExternalID)
				}
			}
		})
	}
}
