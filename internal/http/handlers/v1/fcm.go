package handlers

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
)

type FCMHandler struct {
	queries *postgres.Queries
}

// RegisterFCMTokenRequest represents the request to register an FCM token
type RegisterFCMTokenRequest struct {
	FCMToken    string `json:"fcm_token" validate:"required"`
	DeviceID    string `json:"device_id" validate:"required"`
	Platform    string `json:"platform" validate:"required,oneof=web android ios"`
	AppVersion  string `json:"app_version"`
	DeviceModel string `json:"device_model"`
	OSVersion   string `json:"os_version"`
}

// RegisterFCMTokenResponse represents the response after registering an FCM token
type RegisterFCMTokenResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	ExpiresAt string `json:"expires_at"`
}

// DeleteFCMTokenResponse represents the response after deleting an FCM token
type DeleteFCMTokenResponse struct {
	Message string `json:"message"`
}

func NewFCMHandler(env *config.Environment, queries *postgres.Queries) *chi.Mux {
	h := &FCMHandler{
		queries: queries,
	}
	m := middlewares.New(env, queries)
	router := chi.NewRouter()

	// All FCM routes require authentication
	router.Group(func(r chi.Router) {
		r.Use(m.DefaultPermissions)
		r.Post("/register-token", h.RegisterFCMToken)
		r.Post("/refresh-token/{device_id}", h.RefreshFCMToken)
		r.Delete("/delete-token/{device_id}", h.DeleteFCMToken)
	})

	return router
}

// RegisterFCMToken godoc
// @Summary Register FCM Token
// @Description Register or update an FCM token for push notifications
// @Tags FCM
// @Accept json
// @Produce json
// @Security Bearer
// @Param payload body RegisterFCMTokenRequest true "FCM Token Registration"
// @Success 200 {object} common.SuccessResponse[RegisterFCMTokenResponse]
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} string "Unauthorized"
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/fcm/register-token [post]
func (h *FCMHandler) RegisterFCMToken(w http.ResponseWriter, r *http.Request) {
	var req RegisterFCMTokenRequest

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&req)
	if err != nil {
		common.RespondError(w, err)
		return
	}

	// Basic validation
	if req.FCMToken == "" || req.DeviceID == "" || req.Platform == "" {
		common.RespondError(w, errors.New("fcm_token, device_id e platform são obrigatórios"))
		return
	}

	// Get user from context (using existing middleware pattern)
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	// Get application type from user's current session
	applicationType := user.ApplicationType
	if applicationType == "" {
		common.RespondError(w, errors.New("application type not found in user session"), http.StatusInternalServerError)
		return
	}

	// Set expiration (90 days from now)
	expiresAt := time.Now().Add(90 * 24 * time.Hour)
	now := time.Now()

	// Convert optional fields to sql.NullString
	var deviceModel, osVersion sql.NullString
	if req.DeviceModel != "" {
		deviceModel = sql.NullString{String: req.DeviceModel, Valid: true}
	}
	if req.OSVersion != "" {
		osVersion = sql.NullString{String: req.OSVersion, Valid: true}
	}

	// Insert or update token in database using sqlc-generated method
	err = h.queries.UpsertFCMToken(r.Context(), postgres.UpsertFCMTokenParams{
		UserID:          user.ID,
		PushToken:       fmt.Sprintf("token_%d_%s_%s", user.ID, req.DeviceID, applicationType),
		DeviceID:        req.DeviceID,
		Platform:        req.Platform,
		FcmToken:        req.FCMToken,
		AppVersion:      req.AppVersion,
		ApplicationType: applicationType,
		DeviceModel:     deviceModel,
		OsVersion:       osVersion,
		ExpiresAt:       expiresAt,
		CreatedAt:       now,
		UpdatedAt:       now,
	})

	if err != nil {
		log.Printf("Error upserting push token: %v", err)
		common.RespondError(w, err)
		return
	}

	log.Printf("FCM token registered successfully for user %d, device %s, platform %s",
		user.ID, req.DeviceID, req.Platform)

	response := RegisterFCMTokenResponse{
		Success:   true,
		Message:   "Token FCM registrado com sucesso",
		ExpiresAt: expiresAt.Format(time.RFC3339),
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// RefreshFCMToken godoc
// @Summary Refresh FCM Token
// @Description Refresh an existing FCM token (extend expiration)
// @Tags FCM
// @Accept json
// @Produce json
// @Security Bearer
// @Param device_id path string true "Device ID"
// @Success 200 {object} common.SuccessResponse[RegisterFCMTokenResponse]
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} string "Unauthorized"
// @Failure 404 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/fcm/refresh-token/{device_id} [post]
func (h *FCMHandler) RefreshFCMToken(w http.ResponseWriter, r *http.Request) {
	deviceID := chi.URLParam(r, "device_id")
	if deviceID == "" {
		common.RespondError(w, errors.New("device_id é obrigatório"))
		return
	}

	// Get user from context
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	// Extend expiration by 90 days
	newExpiresAt := time.Now().Add(90 * 24 * time.Hour)

	err := h.queries.RefreshFCMTokenExpiration(r.Context(), postgres.RefreshFCMTokenExpirationParams{
		ExpiresAt: newExpiresAt,
		UserID:    user.ID,
		DeviceID:  deviceID,
	})

	if err != nil {
		log.Printf("Error refreshing push token: %v", err)
		common.RespondError(w, err)
		return
	}

	response := RegisterFCMTokenResponse{
		Success:   true,
		Message:   "Token FCM renovado com sucesso",
		ExpiresAt: newExpiresAt.Format(time.RFC3339),
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// DeleteFCMToken godoc
// @Summary Delete FCM Token
// @Description Delete an FCM token (logout/uninstall)
// @Tags FCM
// @Accept json
// @Produce json
// @Security Bearer
// @Param device_id path string true "Device ID"
// @Success 200 {object} common.SuccessResponse[DeleteFCMTokenResponse]
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} string "Unauthorized"
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/fcm/delete-token/{device_id} [delete]
func (h *FCMHandler) DeleteFCMToken(w http.ResponseWriter, r *http.Request) {
	deviceID := chi.URLParam(r, "device_id")
	if deviceID == "" {
		common.RespondError(w, errors.New("device_id é obrigatório"))
		return
	}

	// Get user from context
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	err := h.queries.DeleteUserDeviceToken(r.Context(), postgres.DeleteUserDeviceTokenParams{
		UserID:   user.ID,
		DeviceID: deviceID,
	})

	if err != nil {
		log.Printf("Error deleting push token: %v", err)
		common.RespondError(w, err)
		return
	}

	response := DeleteFCMTokenResponse{
		Message: "Token FCM removido com sucesso",
	}

	common.RespondSuccess(w, response, http.StatusOK)
}
