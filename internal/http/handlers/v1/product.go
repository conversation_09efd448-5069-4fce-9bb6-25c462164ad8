// Package handlers provides HTTP handlers for product-related endpoints.
package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	"log"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/helpers"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/internal/integrations/storage"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
	"github.com/jackc/pgtype"
	"github.com/jackc/pgx/v4/pgxpool"
)

type ProductHandler struct {
	queries *postgres.Queries
	storage *storage.Storage
	pool    *pgxpool.Pool
	env     *config.Environment
}

type CreateProductRequest struct {
	Ean        string                   `form:"ean" validate:"required" example:"1234567890123"`
	Name       string                   `form:"name" validate:"required" example:"Product Name"`
	Image      *multipart.FileHeader    `form:"image" validate:"required"`
	Categories []custom_models.Category `form:"categories" validate:"required"`
	Brand      string                   `form:"brand" validate:"required" example:"Brand XYZ"`
	Is18Plus   bool                     `form:"is_18_plus" example:"false"`
}

type UpdateProductDataRequest struct {
	Ean        string                   `json:"ean" example:"1234567890123" validate:"required"`
	Name       string                   `json:"name" example:"Product Name" validate:"required"`
	Categories []custom_models.Category `json:"categories" validate:"required,max=1,dive"`
	Brand      string                   `json:"brand" example:"Brand XYZ" validate:"required"`
	IsReviewed bool                     `json:"is_reviewed" example:"false"`
	Is18Plus   bool                     `json:"is_18_plus" example:"false"`
	IsActive   bool                     `json:"is_active" example:"true"`
}

type GetActiveProductsResponse struct {
	Ean        string                   `json:"ean" example:"7895000292035"`
	Name       string                   `json:"name" example:"água De Coco Taeq"`
	Image      string                   `json:"image" example:"https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg"`
	Categories []custom_models.Category `json:"categories" omitempty:"true"`
	Brand      string                   `json:"brand" example:"Taeq"`
	ExternalID string                   `json:"external_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZQ"`
	IsReviewed bool                     `json:"is_reviewed" example:"false"`
	Is18Plus   bool                     `json:"is_18_plus" example:"false"`
	IsActive   bool                     `json:"is_active" example:"true"`
}

type GetActiveProductsGroupedByCategoryResponse struct {
	CategoryName  string                       `json:"category_name" example:"Bebidas"`
	CategoryImage string                       `json:"category_image" example:"https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg"`
	Product       GetProductsGroupedByCategory `json:"products"`
}

type GetProductsGroupedByCategory struct {
	Ean             string            `json:"ean" example:"7895000292035"`
	Name            string            `json:"name" example:"água De Coco Taeq"`
	Image           string            `json:"image" example:"https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg"`
	Brand           string            `json:"brand" example:"Taeq"`
	ExternalID      string            `json:"external_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZQ"`
	Is18Plus        bool              `json:"is_18_plus" example:"false"`
	SimilarProducts []SimilarProducts `json:"similar_products" example:"false"`
}

type SimilarProducts struct {
	ExternalID string `json:"external_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZQ"`
	Name       string `json:"name" example:"água De Coco Taeq"`
	Brand      string `json:"brand" example:"Taeq"`
}

type GetActiveProductsSuccessResponse = common.SuccessResponse[GetActiveProductsResponse]
type GetActiveProductsSuccessPaginatedResponse = common.SuccessResponseWithPagination[[]GetActiveProductsResponse]
type CreateProductSuccessResponse = common.SuccessResponse[string]
type GetActiveProductsGroupedByCategorySuccessPaginatedResponse = common.SuccessResponseWithPagination[[]GetActiveProductsGroupedByCategoryResponse]

func NewProductHandler(env *config.Environment, queries *postgres.Queries, storage *storage.Storage, pool *pgxpool.Pool) *chi.Mux {
	h := &ProductHandler{
		queries: queries,
		storage: storage,
		pool:    pool,
		env:     env,
	}
	m := middlewares.New(env, queries)
	router := chi.NewRouter()
	adminRouter := router.With(m.AdminPermissions)
	partnerRouter := router.With(m.PartnerPermissions)

	adminRouter.Post("/", h.Create)
	adminRouter.Put("/{externalID}", h.UpdateProductData)
	adminRouter.Patch("/{externalID}", h.UpdateProductImage)
	partnerRouter.Get("/", h.GetActiveProducts)
	router.Get("/category/{externalID}", h.GetProductsGroupedByCategory)
	adminRouter.Get("/{ean}", h.GetProductByEan)

	return router
}

// Create godoc
// @Summary Create a new product
// @Description Create a new product
// @Tags Product
// @Security Bearer
// @Accept multipart/form-data
// @Produce json
// @Param ean formData string true "Product EAN" example(1234567890123)
// @Param name formData string true "Product name" example(Product Name)
// @Param image formData file true "Product image file"
// @Param categories formData string true "Product categories" example([{"name":"Bebidas","image":"https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg
// @Param brand formData string true "Product brand" example(Brand XYZ)
// @Param is_18_plus formData boolean false "Is 18+ product" example(false)
// @Success 201 {object} CreateProductSuccessResponse "Product created"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/product [post]
func (h *ProductHandler) Create(w http.ResponseWriter, r *http.Request) {
	r.Body = http.MaxBytesReader(w, r.Body, MultipartFormMaxSize)
	if r.Body != nil {
		defer r.Body.Close()
	}

	mr, err := r.MultipartReader()
	if err != nil {
		log.Printf("Error creating multipart reader: %v\n", err)
		common.RespondError(w, fmt.Errorf("could not create multipart reader: %v", err), http.StatusBadRequest)
		return
	}

	file, fileHeader, formValues, err := helpers.DeferParts(mr)
	if err != nil {
		log.Printf("Error getting form file: %v\n", err)
		common.RespondError(w, fmt.Errorf("error getting form file: %v", err), http.StatusBadRequest)
		return
	}

	if file == nil || fileHeader == nil {
		log.Println("File or file header is nil")
		common.RespondError(w, fmt.Errorf("file or file header is nil"), http.StatusBadRequest)
		return
	}

	var categories []custom_models.Category
	if err := json.Unmarshal([]byte(formValues["categories"]), &categories); err != nil {
		log.Printf("Error decoding categories: %v\n", err)
		common.RespondError(w, fmt.Errorf("error decoding categories: %v", err), http.StatusInternalServerError)
		return
	}

	// Prepare request payload
	payload := CreateProductRequest{
		Ean:        formValues["ean"],
		Name:       formValues["name"],
		Image:      fileHeader,
		Categories: categories,
		Brand:      formValues["brand"],
		Is18Plus:   formValues["is_18_plus"] == "true",
	}

	helper := helpers.New()
	if validationErrors := helper.ValidateRequest(&payload); validationErrors != nil {
		log.Printf("Validation errors: %v\n", validationErrors)
		common.RespondError(w, fmt.Errorf("%v", validationErrors), http.StatusBadRequest)
		return
	}

	// Validate content type for image
	contentType := fileHeader.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "image/") {
		log.Printf("Invalid file type: %s, only image files are allowed\n", contentType)
		common.RespondError(w, fmt.Errorf("invalid file type: %s, only image files are allowed", contentType), http.StatusBadRequest)
		return
	}

	// Generate external ID for product
	productExternalID := helper.GenerateULIDV2()

	// Begin transaction
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction:", err)
		common.RespondError(w, fmt.Errorf("failed to begin transaction: %v", err), http.StatusInternalServerError)
		return
	}
	defer tx.Rollback(r.Context()) // Ensure rollback if the transaction fails

	qtx := h.queries.WithTx(tx)

	// Upload image
	filename := fmt.Sprintf("%s%s", productExternalID, filepath.Ext(fileHeader.Filename))
	imageURL, err := h.storage.UploadImage(r.Context(), filename, file)
	if err != nil {
		log.Printf("Error uploading image: %v\n", err)
		common.RespondError(w, fmt.Errorf("error uploading image: %v", err), http.StatusInternalServerError)
		return
	}

	// Create product
	err = qtx.CreateProduct(r.Context(), postgres.CreateProductParams{
		Ean:        payload.Ean,
		Name:       payload.Name,
		Image:      sql.NullString{String: imageURL, Valid: true},
		Brand:      sql.NullString{String: payload.Brand, Valid: true},
		ExternalID: productExternalID,
		IsReviewed: true,
		Is18Plus:   payload.Is18Plus,
	})
	if err != nil {
		log.Printf("Error creating product: %v\n", err)
		// Remove uploaded image if product creation fails
		if err := h.storage.DeleteImage(r.Context(), filename); err != nil {
			log.Printf("Error deleting image: %v\n", err)
		}
		common.RespondError(w, fmt.Errorf("error creating product: %v", err), http.StatusInternalServerError)
		return
	}

	// Loop through categories and add to product
	for _, category := range categories {
		// Check if category exists
		existingCategory, err := qtx.CheckCategoryExists(r.Context(), category.ExternalID)
		if err != nil {
			log.Printf("Error checking category existence: %v\n", err)
			common.RespondError(w, fmt.Errorf("error checking category existence: %v", err), http.StatusInternalServerError)
			// Remove uploaded image if product creation fails
			if err := h.storage.DeleteImage(r.Context(), filename); err != nil {
				log.Printf("Error deleting image: %v\n", err)
			}
			return
		}
		if !existingCategory {
			log.Println("Category does not exist")
			common.RespondError(w, fmt.Errorf("category does not exist"), http.StatusBadRequest)
			// Remove uploaded image if product creation fails
			if err := h.storage.DeleteImage(r.Context(), filename); err != nil {
				log.Printf("Error deleting image: %v\n", err)
			}
			return
		}

		// Add category to product
		err = qtx.AddCategoryToProduct(r.Context(), postgres.AddCategoryToProductParams{
			ProductExternalID:  productExternalID,
			CategoryExternalID: category.ExternalID,
		})
		if err != nil {
			log.Printf("Error adding category to product: %v\n", err)
			common.RespondError(w, fmt.Errorf("error adding category to product: %v", err), http.StatusInternalServerError)
			// Remove uploaded image if product creation fails
			if err := h.storage.DeleteImage(r.Context(), filename); err != nil {
				log.Printf("Error deleting image: %v\n", err)
			}
			return
		}
	}

	// Commit transaction
	if err = tx.Commit(r.Context()); err != nil {
		log.Println("Failed to commit transaction:", err)
		common.RespondError(w, fmt.Errorf("failed to commit transaction: %v", err), http.StatusInternalServerError)
		return
	}

	// Success response
	common.RespondSuccess(w, productExternalID, http.StatusCreated)
}

// UpdateProductData godoc
// @Summary Update a product
// @Description Update a product
// @Tags Product
// @Security Bearer
// @Accept json
// @Produce json
// @Param payload body UpdateProductDataRequest true "Product data"
// @Param externalID path string true "External ID" example(01F9ZQZQZQZQZQZQZQZQZQZQZ)
// @Success 200 {object} interface{} "Product updated"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/product/{externalID} [put]
func (h *ProductHandler) UpdateProductData(w http.ResponseWriter, r *http.Request) {
	externalID := chi.URLParam(r, "externalID")
	if externalID == "" {
		common.RespondError(w, fmt.Errorf("invalid externalID"), 400)
		return
	}

	payload := UpdateProductDataRequest{}
	helper := helpers.New()

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	// Start transaction
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context()) // Ensure rollback if the transaction fails

	qtx := h.queries.WithTx(tx)

	// Update the product
	err = qtx.UpdateProduct(r.Context(), postgres.UpdateProductParams{
		Name:       payload.Name,
		Brand:      sql.NullString{String: payload.Brand, Valid: true},
		Ean:        payload.Ean,
		ExternalID: externalID,
		IsReviewed: payload.IsReviewed,
		Is18Plus:   payload.Is18Plus,
		IsActive:   payload.IsActive,
	})

	if err != nil {
		log.Printf("Error updating product: %v\n", err)
		common.RespondError(w, err)
		return
	}

	err = qtx.DeleteAllCategoriesFromProduct(r.Context(), externalID)
	if err != nil {
		log.Printf("Error deleting all categories from product: %v\n", err)
		common.RespondError(w, err)
		return
	}

	for _, category := range payload.Categories {
		// Check if the category exists
		existingCategory, err := qtx.CheckCategoryExists(r.Context(), category.ExternalID)
		if err != nil {
			log.Printf("Error checking category existence: %v\n", err)
			common.RespondError(w, err)
			return
		}
		if !existingCategory {
			log.Println("Category does not exist")
			common.RespondError(w, fmt.Errorf("category does not exist"), http.StatusBadRequest)
			return
		}

		// Add category to product
		err = qtx.AddCategoryToProduct(r.Context(), postgres.AddCategoryToProductParams{
			ProductExternalID:  externalID,
			CategoryExternalID: category.ExternalID,
		})
		if err != nil {
			log.Printf("Error adding category to product: %v\n", err)
			common.RespondError(w, err)
			return
		}
	}

	// Commit transaction if all operations are successful
	err = tx.Commit(r.Context())
	if err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// GetActiveProducts godoc
// @Summary Get active products
// @Description Get active products
// @Tags Product
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} GetActiveProductsSuccessPaginatedResponse "Paginated list of products"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/product [get]
func (h *ProductHandler) GetActiveProducts(w http.ResponseWriter, r *http.Request) {
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")
	isReviewed := r.URL.Query().Get("is_reviewed")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	offset := (page - 1) * limit

	products, err := h.queries.GetProductsWithCount(r.Context(), postgres.GetProductsWithCountParams{
		Offset: int32(offset),
		Limit:  int32(limit),
	})
	if err != nil {
		log.Printf("Error getting active products: %v\n", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	totalItems := 0
	response := make([]GetActiveProductsResponse, len(products))
	for i, product := range products {
		var categories []custom_models.Category
		if product.Categories.Status == pgtype.Present {
			categories, err = helpers.ParseJSONB[custom_models.Category](product.Categories.Bytes)
			if err != nil {
				log.Printf("Error parsing categories: %v\n", err)
				common.RespondError(w, fmt.Errorf("failed to unmarshal categories: %v", err), http.StatusInternalServerError)
				return
			}
		}

		totalItems = int(product.TotalCount)
		response[i] = GetActiveProductsResponse{
			Ean:        product.Ean,
			Name:       product.Name,
			Image:      product.Image.String,
			Categories: categories,
			Brand:      product.Brand.String,
			ExternalID: product.ExternalID,
			IsReviewed: product.IsReviewed,
			Is18Plus:   product.Is18Plus,
			IsActive:   product.IsActive,
		}
	}

	//return only is_reviewed products
	if isReviewed == "true" {
		filteredResponse := make([]GetActiveProductsResponse, 0)
		for _, product := range response {
			if product.IsReviewed {
				filteredResponse = append(filteredResponse, product)
			}
		}
		response = filteredResponse
		totalItems = len(filteredResponse)
	}

	common.RespondSuccessWithPagination(w, response, page, limit, totalItems)
}

// GetProductsGroupedByCategory godoc
// @Summary Get products grouped by category
// @Description Get products grouped by category
// @Tags Product
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Param externalID path string true "Category External ID" example(01F9ZQZQZQZQZQZQZQZQZQZQZ)
// @Success 200 {object} GetActiveProductsGroupedByCategorySuccessPaginatedResponse "Paginated list of products"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/product/category/{externalID} [get]
func (h *ProductHandler) GetProductsGroupedByCategory(w http.ResponseWriter, r *http.Request) {
	externalID := chi.URLParam(r, "externalID")
	if externalID == "" {
		common.RespondError(w, fmt.Errorf("invalid externalID"), 400)
		return
	}

	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")
	page := 1
	limit := 10
	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}
	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}
	offset := (page - 1) * limit

	products, err := h.queries.GetProductsByCategoryExternalId(r.Context(), postgres.GetProductsByCategoryExternalIdParams{
		Offset:     int32(offset),
		Limit:      int32(limit),
		ExternalID: externalID,
	})

	if err != nil {
		log.Printf("Error getting active products: %v\n", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	totalItems := 0
	response := make([]GetActiveProductsGroupedByCategoryResponse, len(products))
	for i, product := range products {
		similarProducts := make([]SimilarProducts, 0)
		if product.SimilarProducts.Status == pgtype.Present {
			similarProducts, err = helpers.ParseJSONB[SimilarProducts](product.SimilarProducts.Bytes)
			if err != nil {
				log.Printf("Error parsing similar products: %v\n", err)
				common.RespondError(w, fmt.Errorf("failed to unmarshal similar products: %v", err), http.StatusInternalServerError)
				return
			}
		}

		totalItems = int(product.TotalCount)
		response[i] = GetActiveProductsGroupedByCategoryResponse{
			CategoryName:  product.CategoryName,
			CategoryImage: product.CategoryImage,
			Product: GetProductsGroupedByCategory{
				Ean:             product.Ean,
				Name:            product.Name,
				Image:           product.Image.String,
				Brand:           product.Brand.String,
				ExternalID:      product.ExternalID,
				Is18Plus:        product.Is18Plus,
				SimilarProducts: similarProducts,
			},
		}
	}

	common.RespondSuccessWithPagination(w, response, page, limit, totalItems)
}

// UpdateProductImage godoc
// @Summary Update product image
// @Description Update product image
// @Tags Product
// @Security Bearer
// @Accept multipart/form-data
// @Produce json
// @Param image formData file true "Product image file"
// @Param externalID path string true "External ID" example(01F9ZQZQZQZQZQZQZQZQZQZQZ)
// @Success 200 {object} interface{} "Product image updated"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/product/{externalID} [patch]
func (h *ProductHandler) UpdateProductImage(w http.ResponseWriter, r *http.Request) {
	externalID := chi.URLParam(r, "externalID")
	if externalID == "" {
		common.RespondError(w, fmt.Errorf("invalid externalID"), 400)
		return
	}

	r.Body = http.MaxBytesReader(w, r.Body, MultipartFormMaxSize)
	if r.Body != nil {
		defer r.Body.Close()
	}

	mr, err := r.MultipartReader()
	if err != nil {
		log.Printf("Error creating multipart reader: %v\n", err)
		common.RespondError(w, fmt.Errorf("could not create multipart reader: %v", err), http.StatusBadRequest)
		return
	}

	file, fileHeader, _, err := helpers.DeferParts(mr)
	if err != nil {
		log.Printf("Error reading multipart form: %v\n", err)
		common.RespondError(w, fmt.Errorf("could not read multipart form: %v", err), http.StatusBadRequest)
		return
	}

	if file == nil || fileHeader == nil {
		log.Println("File or file header is nil")
		common.RespondError(w, fmt.Errorf("file is required"), http.StatusBadRequest)
		return
	}

	filename := fmt.Sprintf("%s%s", externalID, filepath.Ext(fileHeader.Filename))

	imageURL, err := h.storage.UploadImage(r.Context(), filename, file)
	if err != nil {
		log.Printf("Error uploading image: %v\n", err)
		common.RespondError(w, err)
		return
	}

	err = h.queries.UpdateProductImage(r.Context(), postgres.UpdateProductImageParams{
		Image:      sql.NullString{String: imageURL, Valid: true},
		ExternalID: externalID,
	})

	if err != nil {
		log.Printf("Error updating product image: %v\n", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// GetProductByEan godoc
// @Summary Get product by EAN
// @Description Get product by EAN
// @Tags Product
// @Accept json
// @Produce json
// @Param ean path string true "Product EAN" example(1234567890123)
// @Success 200 {object} GetActiveProductsSuccessResponse "Product data"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/product/{ean} [get]
func (h *ProductHandler) GetProductByEan(w http.ResponseWriter, r *http.Request) {
	ean := chi.URLParam(r, "ean")
	if ean == "" {
		common.RespondError(w, fmt.Errorf("invalid ean"), 400)
		return
	}

	product, err := h.queries.GetProductByEan(r.Context(), ean)
	if err != nil {
		log.Printf("Error getting product by EAN: %v\n", err)
		common.RespondError(w, err)
		return
	}

	var categories []custom_models.Category
	if product.Categories.Status == pgtype.Present {
		categories, err = helpers.ParseJSONB[custom_models.Category](product.Categories.Bytes)
		if err != nil {
			log.Printf("Error parsing categories: %v\n", err)
			common.RespondError(w, fmt.Errorf("failed to unmarshal categories: %v", err), http.StatusInternalServerError)
			return
		}
	}

	response := GetActiveProductsResponse{
		Ean:        product.Ean,
		Name:       product.Name,
		Image:      product.Image.String,
		Categories: categories,
		Brand:      product.Brand.String,
		ExternalID: product.ExternalID,
		IsReviewed: product.IsReviewed,
	}

	common.RespondSuccess(w, response, http.StatusOK)
}
