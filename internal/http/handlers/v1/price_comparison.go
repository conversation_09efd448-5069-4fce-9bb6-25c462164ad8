package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"math/big"
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/helpers"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
)

type PriceComparisonHandler struct {
	queries *postgres.Queries
	env     *config.Environment
}
type GetDefaultPriceComparisonRequest struct {
	Latitude       float64 `json:"latitude" validate:"required" example:"-5.9015168"`
	Longitude      float64 `json:"longitude" validate:"required" example:"-35.2485376"`
	ListExternalID string  `json:"list_external_id" validate:"required" example:"list_123"`
}

type GetDefaultPriceComparisonResponse struct {
	ExternalID            string            `json:"external_id" example:"01JV0K5T2ZGTBJT3RB6CMK2D0P"`
	Name                  string            `json:"name" example:"Supermercado Duas Irmãs"`
	Picture               string            `json:"picture" example:"https://example.com/picture.jpg"`
	Latitude              *float64          `json:"latitude,omitempty" example:"-5.9015168"`
	Longitude             *float64          `json:"longitude,omitempty" example:"-35.2485376"`
	TotalPrice            int               `json:"total_price" example:"1390"`
	DistanceKm            float64           `json:"distance_km" example:"1.5"`
	Rating                float64           `json:"rating" example:"4.5"`
	ShippingFee           int32             `json:"shipping_fee" example:"50"`
	DeliveryModes         []string          `json:"delivery_modes" example:"[\"delivery\",\"pickup\"]"`
	MatchedProductsCount  int               `json:"matched_products_count" example:"5"`
	MatchedProducts       MatchedProducts   `json:"matched_products"`
	UnmatchedProducts     UnmatchedProducts `json:"unmatched_products"`
	UnmatchedProductCount int               `json:"unmatched_product_count" example:"2"`
}

// SubstituteProduct represents a substitute product with company-specific pricing
type SubstituteProduct struct {
	ExternalID  string                   `json:"external_id" example:"01JSFHYVFQEF8R335QNFDDJ6ZZ"`
	Name        string                   `json:"name" example:"Produto Substituto"`
	EAN         string                   `json:"ean" example:"7891234567890"`
	Description string                   `json:"description" example:"Descrição do produto"`
	Image       string                   `json:"image" example:"https://example.com/image.jpg"`
	Brand       string                   `json:"brand" example:"Marca"`
	Is18Plus    bool                     `json:"is_18_plus" example:"false"`
	Price       int32                    `json:"price" example:"1250"`
	Discount    int32                    `json:"discount" example:"10"`
	Stock       int32                    `json:"stock" example:"50"`
	Categories  []custom_models.Category `json:"categories"`
	MatchReason string                   `json:"match_reason" example:"same_category"`
}

// Product represents a unified product structure used for both matched and unmatched products
type Product struct {
	ExternalID  string                   `json:"external_id" example:"01JSFHYVFQEF8R335QNFDDJ6ZZ"`
	Name        string                   `json:"name" example:"Produto"`
	EAN         string                   `json:"ean" example:"7891234567890"`
	Description string                   `json:"description" example:"Descrição do produto"`
	Image       string                   `json:"image" example:"https://example.com/image.jpg"`
	Brand       string                   `json:"brand" example:"Marca"`
	Is18Plus    bool                     `json:"is_18_plus" example:"false"`
	Price       *int32                   `json:"price,omitempty" example:"1250"`
	Discount    *int32                   `json:"discount,omitempty" example:"10"`
	Stock       int32                    `json:"stock" example:"50"`
	Quantity    int32                    `json:"quantity" example:"2"`
	Categories  []custom_models.Category `json:"categories"`
	Substitutes []SubstituteProduct      `json:"substitutes,omitempty"`
}

// MatchedProducts represents an array of matched products
type MatchedProducts []Product

// UnmatchedProducts represents an array of unmatched products with substitutes
type UnmatchedProducts []Product

// parseMatchedProducts optimizes JSONB parsing with improved error handling
func parseMatchedProducts(jsonData interface{}) MatchedProducts {
	if jsonData == nil {
		return MatchedProducts{}
	}

	// Try direct type assertion for []byte (most common case)
	if jsonBytes, ok := jsonData.([]byte); ok {
		result, err := helpers.ParseJSONB[Product](jsonBytes)
		if err != nil {
			log.Printf("Error parsing matched products from bytes: %v", err)
			return MatchedProducts{}
		}
		return MatchedProducts(result)
	}

	// Fallback: marshal then unmarshal for other interface{} types
	matchedBytes, err := json.Marshal(jsonData)
	if err != nil {
		log.Printf("Error marshaling matched products: %v", err)
		return MatchedProducts{}
	}

	result, err := helpers.ParseJSONB[Product](matchedBytes)
	if err != nil {
		log.Printf("Error parsing matched products (fallback): %v", err)
		return MatchedProducts{}
	}

	return MatchedProducts(result)
}

// parseUnmatchedProducts optimizes JSONB parsing with improved error handling
func parseUnmatchedProducts(jsonData interface{}) UnmatchedProducts {
	if jsonData == nil {
		return UnmatchedProducts{}
	}

	// Try direct type assertion for []byte (most common case)
	if jsonBytes, ok := jsonData.([]byte); ok {
		result, err := helpers.ParseJSONB[Product](jsonBytes)
		if err != nil {
			log.Printf("Error parsing unmatched products from bytes: %v", err)
			return UnmatchedProducts{}
		}
		return UnmatchedProducts(result)
	}

	// Fallback: marshal then unmarshal for other interface{} types
	unmatchedBytes, err := json.Marshal(jsonData)
	if err != nil {
		log.Printf("Error marshaling unmatched products: %v", err)
		return UnmatchedProducts{}
	}

	result, err := helpers.ParseJSONB[Product](unmatchedBytes)
	if err != nil {
		log.Printf("Error parsing unmatched products (fallback): %v", err)
		return UnmatchedProducts{}
	}

	return UnmatchedProducts(result)
}

// convertNullableCoordinates converts sql.NullFloat64 latitude and longitude to *float64 pointers
// Returns nil pointers if the database values are null
func convertNullableCoordinates(lat, lng sql.NullFloat64) (*float64, *float64) {
	var latitude, longitude *float64
	if lat.Valid {
		latitude = &lat.Float64
	}
	if lng.Valid {
		longitude = &lng.Float64
	}
	return latitude, longitude
}

type GetRecommendedPriceComparisonResponse struct {
	ExternalID            string            `json:"external_id" example:"01JV0K5T2ZGTBJT3RB6CMK2D0P"`
	Name                  string            `json:"name" example:"Supermercado Duas Irmãs"`
	Picture               string            `json:"picture" example:"https://example.com/picture.jpg"`
	Latitude              *float64          `json:"latitude,omitempty" example:"-5.9015168"`
	Longitude             *float64          `json:"longitude,omitempty" example:"-35.2485376"`
	TotalPrice            int               `json:"total_price" example:"1390"`
	MatchedProductsCount  int               `json:"matched_products_count" example:"5"`
	MatchedProducts       MatchedProducts   `json:"matched_products"`
	UnmatchedProducts     UnmatchedProducts `json:"unmatched_products"`
	UnmatchedProductCount int               `json:"unmatched_product_count" example:"2"`
	ShippingFee           int32             `json:"shipping_fee" example:"50"`
	DeliveryModes         []string          `json:"delivery_modes" example:"[\"delivery\",\"pickup\"]"`
}

func NewPriceComparisonHandler(env *config.Environment, queries *postgres.Queries) *chi.Mux {
	router := chi.NewRouter()
	h := &PriceComparisonHandler{
		queries: queries,
		env:     env,
	}

	m := middlewares.New(env, queries)
	defaultRouter := router.With(m.DefaultPermissions)

	defaultRouter.Post("/default", h.GetDefault)
	defaultRouter.Get("/recommendations/{list_external_id}", h.GetRecommendations)

	return router
}

// GetDefault godoc
// @Summary      Get default price comparison
// @Description  Get default price comparison
// @Tags         PriceComparison
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param payload body GetDefaultPriceComparisonRequest true "Get default price comparison request"
// @Success      200 {object} GetDefaultPriceComparisonResponse
// @Failure      400 {object} common.ErrorResponse
// @Failure      401 {object} common.ErrorResponse
// @Failure      500 {object} common.ErrorResponse
// @Router       /v1/price-comparison/default [post]
func (h *PriceComparisonHandler) GetDefault(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	helper := helpers.New()

	payload := GetDefaultPriceComparisonRequest{}

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		log.Println("Validation errors: ", validationErrors)
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	//Check if the listExternalID belongs to the user
	hasAccess, err := h.queries.CheckIfUserProductsListBelongsToUser(r.Context(), postgres.CheckIfUserProductsListBelongsToUserParams{
		UserID:     user.ID,
		ExternalID: payload.ListExternalID,
	})
	if err != nil {
		log.Println("Error checking if user products list belongs to user: ", err)
		common.RespondError(w, err)
		return
	}

	if !hasAccess {
		log.Println("User can only access their own products list")
		common.RespondError(w, fmt.Errorf("user can only access their own products list"), http.StatusForbidden)
		return
	}

	// Use location-filtered query with 10km distance limit
	distanceMeters := MaxDistanceKm * 1000 // Convert km to meters for PostGIS

	defaultPriceComparison, err := h.queries.GetDefaultPriceComparisonWithLocation(r.Context(), postgres.GetDefaultPriceComparisonWithLocationParams{
		ExternalID:    payload.ListExternalID,
		StMakepoint:   payload.Longitude, // longitude first for PostGIS
		StMakepoint_2: payload.Latitude,  // latitude second for PostGIS
		StDwithin:     distanceMeters,
	})
	if err != nil {
		log.Println("Error getting default price comparison with location: ", err)
		common.RespondError(w, err)
		return
	}

	response := make([]GetDefaultPriceComparisonResponse, len(defaultPriceComparison))
	for i, o := range defaultPriceComparison {
		dist := o.DistanceKm.Int
		exp := o.DistanceKm.Exp
		distFloat, _ := new(big.Float).SetInt(dist).Float64()
		distanciaKm := distFloat * math.Pow10(int(exp))
		distanciaKm = math.Round(distanciaKm*100) / 100

		// Parse matched and unmatched products from JSONB - optimized parsing
		matchedProducts := parseMatchedProducts(o.MatchedProducts)
		unmatchedProducts := parseUnmatchedProducts(o.UnmatchedProducts)

		// Handle nullable latitude and longitude
		latitude, longitude := convertNullableCoordinates(o.CompanyLatitude, o.CompanyLongitude)

		response[i] = GetDefaultPriceComparisonResponse{
			ExternalID:            o.CompanyExternalID,
			Name:                  o.CompanyName,
			Picture:               o.CompanyPicture,
			Latitude:              latitude,
			Longitude:             longitude,
			ShippingFee:           o.CompanyShippingFee,
			DeliveryModes:         o.CompanyDeliveryModes,
			TotalPrice:            int(o.TotalPrice),
			DistanceKm:            distanciaKm,
			Rating:                float64(o.Rating),
			MatchedProductsCount:  int(o.MatchedProductsCount),
			MatchedProducts:       matchedProducts,
			UnmatchedProducts:     unmatchedProducts,
			UnmatchedProductCount: int(o.UnmatchedProductCount),
		}
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// GetRecommendations godoc
// @Summary      Get price comparison recommendations
// @Description  Get price comparison recommendations with optional location-based filtering
// @Tags         PriceComparison
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param list_external_id path string true "List External ID" example("list_123")
// @Param lat query number false "Latitude for location-based filtering"
// @Param long query number false "Longitude for location-based filtering"
// @Success      200 {object} []GetDefaultPriceComparisonResponse
// @Failure      400 {object} common.ErrorResponse
// @Failure      401 {object} common.ErrorResponse
// @Failure      500 {object} common.ErrorResponse
// @Router       /v1/price-comparison/recommendations/{list_external_id} [get]
func (h *PriceComparisonHandler) GetRecommendations(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	listExternalID := chi.URLParam(r, "list_external_id")
	if listExternalID == "" {
		log.Println("List External ID is required")
		common.RespondError(w, fmt.Errorf("list_external_id is required"), http.StatusBadRequest)
		return
	}

	// Parse location parameters
	latStr := r.URL.Query().Get("lat")
	longStr := r.URL.Query().Get("long")

	// Check if location parameters are provided
	hasLocationParams := latStr != "" && longStr != ""

	// If only one coordinate is provided, return empty array (fast fail)
	if (latStr != "" && longStr == "") || (latStr == "" && longStr != "") {
		log.Printf("Incomplete location parameters: lat=%s, long=%s", latStr, longStr)
		common.RespondSuccess(w, []GetRecommendedPriceComparisonResponse{}, http.StatusOK)
		return
	}

	var lat, long float64
	var err error

	// Parse and validate coordinates if provided
	if hasLocationParams {
		lat, err = strconv.ParseFloat(latStr, 64)
		if err != nil {
			log.Printf("Invalid latitude: %v\n", err)
			common.RespondSuccess(w, []GetRecommendedPriceComparisonResponse{}, http.StatusOK)
			return
		}

		long, err = strconv.ParseFloat(longStr, 64)
		if err != nil {
			log.Printf("Invalid longitude: %v\n", err)
			common.RespondSuccess(w, []GetRecommendedPriceComparisonResponse{}, http.StatusOK)
			return
		}

		// Validate coordinate ranges
		if lat < -90 || lat > 90 {
			log.Printf("Latitude out of range: %f\n", lat)
			common.RespondSuccess(w, []GetRecommendedPriceComparisonResponse{}, http.StatusOK)
			return
		}

		if long < -180 || long > 180 {
			log.Printf("Longitude out of range: %f\n", long)
			common.RespondSuccess(w, []GetRecommendedPriceComparisonResponse{}, http.StatusOK)
			return
		}
	}

	//Check if the listExternalID belongs to the user
	hasAccess, err := h.queries.CheckIfUserProductsListBelongsToUser(r.Context(), postgres.CheckIfUserProductsListBelongsToUserParams{
		UserID:     user.ID,
		ExternalID: listExternalID,
	})
	if err != nil {
		log.Println("Error checking if user products list belongs to user: ", err)
		common.RespondError(w, err)
		return
	}

	if !hasAccess {
		log.Println("User can only access their own products list")
		common.RespondError(w, fmt.Errorf("user can only access their own products list"), http.StatusForbidden)
		return
	}

	var recommendations []GetRecommendedPriceComparisonResponse

	if hasLocationParams {
		// Use location-based query
		distanceMeters := MaxDistanceKm * 1000 // Convert km to meters for PostGIS

		result, err := h.queries.GetRecommendedPriceComparisonWithLocation(r.Context(), postgres.GetRecommendedPriceComparisonWithLocationParams{
			ExternalID:    listExternalID,
			StMakepoint:   long, // longitude first for PostGIS
			StMakepoint_2: lat,  // latitude second for PostGIS
			StDwithin:     distanceMeters,
		})
		if err != nil {
			log.Println("Error getting price comparison recommendations with location: ", err)
			common.RespondError(w, err)
			return
		}

		recommendations = make([]GetRecommendedPriceComparisonResponse, len(result))
		for i, o := range result {
			// Parse matched and unmatched products from JSONB - optimized parsing
			matchedProducts := parseMatchedProducts(o.MatchedProducts)
			unmatchedProducts := parseUnmatchedProducts(o.UnmatchedProducts)

			// Handle nullable latitude and longitude
			latitude, longitude := convertNullableCoordinates(o.CompanyLatitude, o.CompanyLongitude)

			recommendations[i] = GetRecommendedPriceComparisonResponse{
				ExternalID:            o.CompanyID,
				Name:                  o.CompanyName,
				Picture:               o.CompanyPicture,
				Latitude:              latitude,
				Longitude:             longitude,
				TotalPrice:            int(o.TotalPrice),
				ShippingFee:           o.CompanyShippingFee,
				DeliveryModes:         o.CompanyDeliveryModes,
				MatchedProductsCount:  int(o.MatchedProductsCount),
				MatchedProducts:       matchedProducts,
				UnmatchedProducts:     unmatchedProducts,
				UnmatchedProductCount: int(o.UnmatchedProductCount),
			}
		}
	} else {
		// Use regular query without location filtering
		result, err := h.queries.GetRecommendedPriceComparison(r.Context(), listExternalID)
		if err != nil {
			log.Println("Error getting price comparison recommendations: ", err)
			common.RespondError(w, err)
			return
		}

		recommendations = make([]GetRecommendedPriceComparisonResponse, len(result))
		for i, o := range result {
			// Parse matched and unmatched products from JSONB - optimized parsing
			matchedProducts := parseMatchedProducts(o.MatchedProducts)
			unmatchedProducts := parseUnmatchedProducts(o.UnmatchedProducts)

			// Handle nullable latitude and longitude
			latitude, longitude := convertNullableCoordinates(o.CompanyLatitude, o.CompanyLongitude)

			recommendations[i] = GetRecommendedPriceComparisonResponse{
				ExternalID:            o.CompanyID,
				Name:                  o.CompanyName,
				Picture:               o.CompanyPicture,
				Latitude:              latitude,
				Longitude:             longitude,
				TotalPrice:            int(o.TotalPrice),
				ShippingFee:           o.CompanyShippingFee,
				DeliveryModes:         o.CompanyDeliveryModes,
				MatchedProductsCount:  int(o.MatchedProductsCount),
				MatchedProducts:       matchedProducts,
				UnmatchedProducts:     unmatchedProducts,
				UnmatchedProductCount: int(o.UnmatchedProductCount),
			}
		}
	}

	common.RespondSuccess(w, recommendations, http.StatusOK)
}
