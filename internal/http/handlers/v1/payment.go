package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"strings"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/fcm"
	"github.com/izy-mercado/backend/internal/helpers"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/internal/integrations/payment/woovi"
	"github.com/izy-mercado/backend/internal/logger"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
	"github.com/jackc/pgtype"
	"github.com/jackc/pgx/v4/pgxpool"
)

type PaymentHandler struct {
	queries    *postgres.Queries
	pix        *woovi.Woovi
	pool       *pgxpool.Pool
	logger     logger.Logger
	fcmService *fcm.Service
}

type CheckoutRequest struct {
	CompanyExternalID string             `json:"company" validate:"required"`
	Products          []ProductsCheckout `json:"products" validate:"required"`
	PaymentMethod     string             `json:"payment_method" validate:"required,oneof=pix credit_card"`
	CreditCard        *CreditCard        `json:"credit_card" validate:"required_if=payment_method=credit_card omitempty"`
	CouponCode        string             `json:"coupon_code"`
	AddressExternalID string             `json:"address_external_id" validate:"required"`
	DeliveryMode      string             `json:"delivery_mode" validate:"required,oneof=delivery pickup"`
}

type ProductsCheckout struct {
	ExternalID string `json:"external_id" validate:"required"`
	Quantity   int32  `json:"quantity" validate:"required,gt=0"`
	Discount   int32  `json:"discount" swaggerignore:"true"`
	Price      int32  `json:"price" swaggerignore:"true"`
}

type CreditCard struct {
	Installments    int32                       `json:"installments"`
	CardNumber      string                      `json:"card_number"`
	CVV             string                      `json:"cvv"`
	ExpirationDate  string                      `json:"expiration_date"`
	CardHolderName  string                      `json:"card_holder_name"`
	CardHolderCPF   string                      `json:"card_holder_cpf"`
	CardHolderBirth string                      `json:"card_holder_birth"`
	BillingAddress  custom_models.AddressParams `json:"billing_address"`
}

type ChargeEvent struct {
	Account struct {
		Environment string `json:"environment,omitempty"`
	} `json:"account,omitempty"`
	Charge struct {
		AdditionalInfo []interface{} `json:"additionalInfo,omitempty"`
		BrCode         string        `json:"brCode,omitempty"`
		Comment        string        `json:"comment,omitempty"`
		CorrelationID  string        `json:"correlationID,omitempty"`
		CreatedAt      time.Time     `json:"createdAt,omitempty"`
		Customer       struct {
			CorrelationID string `json:"correlationID,omitempty"`
			Email         string `json:"email,omitempty"`
			Name          string `json:"name,omitempty"`
		} `json:"customer,omitempty"`
		Discount    int       `json:"discount,omitempty"`
		ExpiresDate time.Time `json:"expiresDate,omitempty"`
		ExpiresIn   int       `json:"expiresIn,omitempty"`
		Fee         int       `json:"fee,omitempty"`
		GlobalID    string    `json:"globalID,omitempty"`
		Identifier  string    `json:"identifier,omitempty"`
		PaidAt      time.Time `json:"paidAt,omitempty"`
		Payer       struct {
			Address struct {
				City         string `json:"city,omitempty"`
				Complement   string `json:"complement,omitempty"`
				Neighborhood string `json:"neighborhood,omitempty"`
				Number       string `json:"number,omitempty"`
				State        string `json:"state,omitempty"`
				Street       string `json:"street,omitempty"`
				Zipcode      string `json:"zipcode,omitempty"`
			} `json:"address,omitempty"`
			CorrelationID string `json:"correlationID,omitempty"`
			Email         string `json:"email,omitempty"`
			Name          string `json:"name,omitempty"`
			Phone         string `json:"phone,omitempty"`
			TaxID         struct {
				TaxID string `json:"taxID,omitempty"`
				Type  string `json:"type,omitempty"`
			} `json:"taxID,omitempty"`
		} `json:"payer,omitempty"`
		PaymentLinkID  string `json:"paymentLinkID,omitempty"`
		PaymentLinkURL string `json:"paymentLinkUrl,omitempty"`
		PaymentMethods struct {
			Pix struct {
				BrCode        string `json:"brCode,omitempty"`
				Fee           int    `json:"fee,omitempty"`
				Identifier    string `json:"identifier,omitempty"`
				Method        string `json:"method,omitempty"`
				QrCodeImage   string `json:"qrCodeImage,omitempty"`
				Status        string `json:"status,omitempty"`
				TransactionID string `json:"transactionID,omitempty"`
				TxID          string `json:"txId,omitempty"`
				Value         int    `json:"value,omitempty"`
			} `json:"pix,omitempty"`
		} `json:"paymentMethods,omitempty"`
		PixKey      string `json:"pixKey,omitempty"`
		QrCodeImage string `json:"qrCodeImage,omitempty"`
		Splits      []struct {
			PixKey        string `json:"pixKey,omitempty"`
			PixKeyType    string `json:"pixKeyType,omitempty"`
			SourceAccount string `json:"sourceAccount,omitempty"`
			SplitType     string `json:"splitType,omitempty"`
			Value         int    `json:"value,omitempty"`
		} `json:"splits,omitempty"`
		Status            string    `json:"status,omitempty"`
		TransactionID     string    `json:"transactionID,omitempty"`
		Type              string    `json:"type,omitempty"`
		UpdatedAt         time.Time `json:"updatedAt,omitempty"`
		Value             int       `json:"value,omitempty"`
		ValueWithDiscount int       `json:"valueWithDiscount,omitempty"`
	} `json:"charge,omitempty"`
	Company struct {
		ID    string `json:"id,omitempty"`
		Name  string `json:"name,omitempty"`
		TaxID string `json:"taxID,omitempty"`
	} `json:"company,omitempty"`
	Event  string `json:"event,omitempty"`
	Evento string `json:"evento,omitempty" example:"teste_webhook"`
	Pix    struct {
		Charge struct {
			AdditionalInfo []interface{} `json:"additionalInfo,omitempty"`
			BrCode         string        `json:"brCode,omitempty"`
			Comment        string        `json:"comment,omitempty"`
			CorrelationID  string        `json:"correlationID,omitempty"`
			CreatedAt      time.Time     `json:"createdAt,omitempty"`
			Customer       struct {
				CorrelationID string `json:"correlationID,omitempty"`
				Email         string `json:"email,omitempty"`
				Name          string `json:"name,omitempty"`
			} `json:"customer,omitempty"`
			Discount    int       `json:"discount,omitempty"`
			ExpiresDate time.Time `json:"expiresDate,omitempty"`
			ExpiresIn   int       `json:"expiresIn,omitempty"`
			Fee         int       `json:"fee,omitempty"`
			GlobalID    string    `json:"globalID,omitempty"`
			Identifier  string    `json:"identifier,omitempty"`
			PaidAt      time.Time `json:"paidAt,omitempty"`
			Payer       struct {
				Address struct {
					City         string `json:"city,omitempty"`
					Complement   string `json:"complement,omitempty"`
					Neighborhood string `json:"neighborhood,omitempty"`
					Number       string `json:"number,omitempty"`
					State        string `json:"state,omitempty"`
					Street       string `json:"street,omitempty"`
					Zipcode      string `json:"zipcode,omitempty"`
				} `json:"address,omitempty"`
				CorrelationID string `json:"correlationID,omitempty"`
				Email         string `json:"email,omitempty"`
				Name          string `json:"name,omitempty"`
				Phone         string `json:"phone,omitempty"`
				TaxID         struct {
					TaxID string `json:"taxID,omitempty"`
					Type  string `json:"type,omitempty"`
				} `json:"taxID,omitempty"`
			} `json:"payer,omitempty"`
			PaymentLinkID  string `json:"paymentLinkID,omitempty"`
			PaymentLinkURL string `json:"paymentLinkUrl,omitempty"`
			PixKey         string `json:"pixKey,omitempty"`
			QrCodeImage    string `json:"qrCodeImage,omitempty"`
			Splits         []struct {
				PixKey        string `json:"pixKey,omitempty"`
				PixKeyType    string `json:"pixKeyType,omitempty"`
				SourceAccount string `json:"sourceAccount,omitempty"`
				SplitType     string `json:"splitType,omitempty"`
				Value         int    `json:"value,omitempty"`
			} `json:"splits,omitempty"`
			Status            string    `json:"status,omitempty"`
			TransactionID     string    `json:"transactionID,omitempty"`
			Type              string    `json:"type,omitempty"`
			UpdatedAt         time.Time `json:"updatedAt,omitempty"`
			Value             int       `json:"value,omitempty"`
			ValueWithDiscount int       `json:"valueWithDiscount,omitempty"`
		} `json:"charge,omitempty"`
		CreatedAt time.Time `json:"createdAt,omitempty"`
		Customer  struct {
			CorrelationID string `json:"correlationID,omitempty"`
			Email         string `json:"email,omitempty"`
			Name          string `json:"name,omitempty"`
		} `json:"customer,omitempty"`
		EndToEndID  string `json:"endToEndId,omitempty"`
		GlobalID    string `json:"globalID,omitempty"`
		InfoPagador string `json:"infoPagador,omitempty"`
		Payer       struct {
			Address struct {
				ID         string `json:"_id,omitempty"`
				City       string `json:"city,omitempty"`
				Complement string `json:"complement,omitempty"`
				Country    string `json:"country,omitempty"`
				Location   struct {
					Coordinates []interface{} `json:"coordinates,omitempty"`
				} `json:"location,omitempty"`
				Neighborhood string `json:"neighborhood,omitempty"`
				Number       string `json:"number,omitempty"`
				State        string `json:"state,omitempty"`
				Street       string `json:"street,omitempty"`
				Zipcode      string `json:"zipcode,omitempty"`
			} `json:"address,omitempty"`
			CorrelationID string `json:"correlationID,omitempty"`
			Email         string `json:"email,omitempty"`
			Name          string `json:"name,omitempty"`
			Phone         string `json:"phone,omitempty"`
			TaxID         struct {
				TaxID string `json:"taxID,omitempty"`
				Type  string `json:"type,omitempty"`
			} `json:"taxID,omitempty"`
		} `json:"payer,omitempty"`
		Time          time.Time `json:"time,omitempty"`
		TransactionID string    `json:"transactionID,omitempty"`
		Type          string    `json:"type,omitempty"`
		Value         int32     `json:"value,omitempty"`
	} `json:"pix,omitempty"`
}

type StockErrorDetail struct {
	Requested int32 `json:"requested"`
	Available int32 `json:"available"`
}

// Enhanced stock error detail with complete product information
type EnhancedStockErrorDetail struct {
	ExternalID  string                   `json:"external_id"`
	Name        string                   `json:"name"`
	EAN         string                   `json:"ean"`
	Description string                   `json:"description"`
	Image       string                   `json:"image"`
	Brand       string                   `json:"brand"`
	Is18Plus    bool                     `json:"is_18_plus"`
	Price       int32                    `json:"price"`
	Discount    int32                    `json:"discount"`
	Stock       int32                    `json:"stock"`    // Available quantity
	Quantity    int32                    `json:"quantity"` // Requested quantity
	Categories  []custom_models.Category `json:"categories"`
}

type StockValidationError struct {
	OrderID string                      `json:"order_id"`
	Errors  map[string]StockErrorDetail `json:"errors"`
}

// Enhanced stock validation error with complete product information
type EnhancedStockValidationError struct {
	OrderID  string                     `json:"order_id"`
	Products []EnhancedStockErrorDetail `json:"products"`
}

type CheckoutSuccessResponse = common.SuccessResponse[woovi.PixSuccessResponse]

func NewPaymentHandler(env *config.Environment, queries *postgres.Queries, pool *pgxpool.Pool, log logger.Logger, fcmService *fcm.Service) *chi.Mux {
	h := &PaymentHandler{
		queries: queries,
		pix: woovi.New(woovi.WooviConfig{
			APIKey: env.Woovi.API_KEY,
			Url:    env.Woovi.URL,
			PixKey: env.Woovi.PIX_KEY,
		}),
		pool:       pool,
		logger:     log,
		fcmService: fcmService,
	}
	m := middlewares.New(env, queries)
	router := chi.NewRouter()
	router.Group(func(r chi.Router) {
		r.With(m.DefaultPermissions).Post("/checkout", h.Checkout)
		//TODO: add middleware for webhook integration
		r.Post("/process-paid-charge-event-callback", h.ProcessPaidChargeEventCallback)
		r.Post("/process-expired-charge-event-callback", h.ProcessExpiredChargeEventCallback)
	})

	return router
}

// Checkout godoc
// @Summary Checkout
// @Description Checkout
// @Tags Payment
// @Accept json
// @Produce json
// @Security Bearer
// @Param payload body CheckoutRequest true "Checkout Request"
// @Success 200 {object} CheckoutSuccessResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/payment/checkout [post]
func (h *PaymentHandler) Checkout(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	payload := CheckoutRequest{}
	helper := helpers.New()

	// Use injected logger for dependency injection
	log := h.logger

	// Add user context for logging
	ctx := logger.WithUserID(r.Context(), user.ExternalID)

	// Log checkout start
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"user_external_id": user.ExternalID,
		"user_email":       user.Email,
	}).Info("Starting checkout process")

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("Failed to decode checkout request body")
		common.RespondError(w, err)
		return
	}

	// Log checkout request details
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"company_external_id": payload.CompanyExternalID,
		"payment_method":      payload.PaymentMethod,
		"coupon_code":         payload.CouponCode,
		"products_count":      len(payload.Products),
		"address_external_id": payload.AddressExternalID,
	}).Debug("Checkout request details")

	// Log complete request for curl reproduction
	requestBody, _ := json.Marshal(payload)
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"method":      r.Method,
		"url":         r.URL.String(),
		"headers":     r.Header,
		"body":        string(requestBody),
		"remote_addr": r.RemoteAddr,
		"user_agent":  r.UserAgent(),
	}).Debug("Complete HTTP request for curl reproduction")

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		log.WithContext(ctx).WithField("validation_errors", validationErrors).Warn("Checkout request validation failed")
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	// Fetch user address by external ID
	userAddress, err := h.queries.GetUserAddressByExternalID(r.Context(), payload.AddressExternalID)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithField("address_external_id", payload.AddressExternalID).Error("Error getting user address by external id")
		common.RespondError(w, err, 500)
		return
	}

	// Format address as text
	addressText := helper.FormatAddressAsText(
		userAddress.Street,
		userAddress.Number,
		userAddress.Complement.String,
		userAddress.Neighborhood,
		userAddress.City,
		userAddress.State,
		userAddress.ZipCode,
	)

	//join all phones with comma if has more than one
	if len(user.Phones) == 0 {
		common.RespondError(w, errors.New("user has no phone number"))
		return
	}

	phoneNumberText := user.Phones[0]
	if len(user.Phones) > 1 {
		phoneNumberText = strings.Join(user.Phones, ",")
	}

	fmt.Printf("Payload: %+v\n", payload)

	company, err := h.queries.GetCompanyInfoWithProducts(r.Context(), payload.CompanyExternalID)
	if err != nil {
		log.WithContext(ctx).WithError(err).WithField("company_external_id", payload.CompanyExternalID).Error("Error getting company by external id")
		common.RespondError(w, err)
		return
	}

	// Handle interface{} type from JSON aggregation
	var companyProducts []custom_models.Product
	if company.Products != nil {
		if jsonBytes, ok := company.Products.([]byte); ok {
			companyProducts, err = helpers.ParseJSONB[custom_models.Product](jsonBytes)
		} else {
			// Fallback: marshal then unmarshal for other interface{} types
			jsonBytes, marshalErr := json.Marshal(company.Products)
			if marshalErr != nil {
				log.WithContext(ctx).WithError(marshalErr).Error("Error marshaling company products")
				common.RespondError(w, marshalErr)
				return
			}
			companyProducts, err = helpers.ParseJSONB[custom_models.Product](jsonBytes)
		}
	}
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("Error parsing company products")
		common.RespondError(w, err)
		return
	}

	fmt.Printf("Successfully parsed %d company products\n", len(companyProducts))

	stockMap := createProductMap(companyProducts)
	var amount int32
	var discount int32

	fmt.Printf("Company has %d products in catalog\n", len(companyProducts))
	fmt.Printf("Shipping fee: %d\n", company.ShippingFee)

	insufficientStock := make(map[string]StockErrorDetail, len(payload.Products))
	var enhancedInsufficientStock []EnhancedStockErrorDetail

	fmt.Printf("Starting product loop with %d products\n", len(payload.Products))
	for i, p := range payload.Products {
		fmt.Printf("Processing product %d: %s\n", i, p.ExternalID)
		fmt.Printf("Looking for product %s in stockMap\n", p.ExternalID)
		prod, exists := stockMap[p.ExternalID]
		fmt.Printf("Product %s exists: %t\n", p.ExternalID, exists)
		if exists {
			fmt.Printf("Product %s stock: %d, requested: %d\n", p.ExternalID, prod.Stock, p.Quantity)
		}

		// Handle product not found or insufficient stock
		if !exists {
			// Product not found in company
			insufficientStock[p.ExternalID] = StockErrorDetail{
				Requested: p.Quantity,
				Available: 0, // Product not available at this company
			}

			// For enhanced error, we can't provide product details since product doesn't exist
			enhancedProduct := EnhancedStockErrorDetail{
				ExternalID: p.ExternalID,
				Name:       "Product not found",
				Stock:      0,          // Available quantity (product not in catalog)
				Quantity:   p.Quantity, // Requested quantity
				Categories: []custom_models.Category{},
			}
			enhancedInsufficientStock = append(enhancedInsufficientStock, enhancedProduct)
			continue
		}

		if p.Quantity > prod.Stock {
			// Product exists but insufficient stock
			insufficientStock[p.ExternalID] = StockErrorDetail{
				Requested: p.Quantity,
				Available: prod.Stock,
			}

			// Add to enhanced format with complete product information
			enhancedProduct := EnhancedStockErrorDetail{
				ExternalID:  prod.ExternalID,
				Name:        prod.Name,
				EAN:         prod.Ean,
				Description: prod.Description,
				Image:       prod.Image,
				Brand:       prod.Brand,
				Is18Plus:    prod.Is18Plus,
				Price:       prod.Price,
				Discount:    prod.Discount,
				Stock:       prod.Stock, // Available quantity
				Quantity:    p.Quantity, // Requested quantity
				Categories:  prod.Categories,
			}
			enhancedInsufficientStock = append(enhancedInsufficientStock, enhancedProduct)
			continue
		}

		// Product exists and has sufficient stock - process normally
		fmt.Printf("Processing product %s: quantity=%d, price=%d, discount=%d%%\n",
			prod.ExternalID, p.Quantity, prod.Price, prod.Discount)

		// save complete products information on invoice
		payload.Products[i].Discount += prod.Discount
		payload.Products[i].Price = prod.Price

		// calculate total amount and discount
		amount += p.Quantity * prod.Price
		fmt.Printf("[1]Amount: %d, Discount: %d\n", amount, discount)
		discount += p.Quantity * (prod.Price * prod.Discount) / 100
		fmt.Printf("[2]Amount: %d, Discount: %d\n", amount, discount)
	}

	// Check for insufficient stock BEFORE any other validations
	if len(insufficientStock) > 0 {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"insufficient_stock": insufficientStock,
		}).Warn("Insufficient stock detected")

		// Create enhanced error with complete product information
		enhancedErr := &EnhancedStockValidationError{
			OrderID:  helpers.New().GenerateULIDV2(), // Generate order ID for error tracking
			Products: enhancedInsufficientStock,
		}

		// Log the complete response for debug purposes
		responseJSON, _ := json.Marshal(enhancedErr)
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"response_type": "INSUFFICIENT_STOCK",
			"response_body": string(responseJSON),
		}).Debug("Sending insufficient stock response")

		common.RespondError(w, enhancedErr, http.StatusConflict)
		return
	}

	amount -= discount

	// Add shipping fee to total amount only if delivery mode is "delivery"
	var shippingFee int32
	if payload.DeliveryMode == "delivery" {
		shippingFee = company.ShippingFee
		amount += shippingFee
	} else {
		shippingFee = 0
	}

	const MinimumAmount = 1500
	if amount < MinimumAmount {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"amount":         amount,
			"minimum_amount": MinimumAmount,
		}).Error("Minimum amount not reached")
		common.RespondError(w, errors.New("minimum amount not reached"), http.StatusBadRequest)
		return
	}

	var couponDiscount int32
	var couponOwnerType string

	// Calculate split amount based on new formulas
	var splitAmount int32
	commissionRate := float64(company.CommissionRate) / 10000
	orderValue := amount - shippingFee  // Order value without shipping (using conditional shippingFee)
	minCommissionRequired := int32(100) // R$ 1.00 - defined once for reuse

	// Fluxo com cupom
	if payload.CouponCode != "" {
		log.WithContext(ctx).WithField("coupon_code", payload.CouponCode).Info("Validating and applying coupon")

		couponDiscount, couponOwnerType, err = ValidateAndApplyCoupon(ValidateAndApplyCouponParams{
			Context:             ctx, // Use context with OrderID
			Queries:             h.queries,
			Pool:                h.pool,
			Logger:              h.logger, // Pass injected logger
			User:                user,
			CouponCode:          payload.CouponCode,
			CompanyID:           company.ID,
			Amount:              orderValue, // Use order value without shipping fee
			ShouldRegisterUsage: true,
		})
		if err != nil {
			log.WithContext(ctx).WithError(err).WithField("coupon_code", payload.CouponCode).Error("Coupon validation failed")
			common.RespondError(w, err, 400)
			return
		}

		// Validate coupon discount doesn't exceed order value (without shipping)
		if couponDiscount > orderValue {
			log.WithContext(ctx).WithFields(map[string]interface{}{
				"coupon_discount": couponDiscount,
				"order_value":     orderValue,
			}).Error("Coupon discount exceeds order value")
			common.RespondError(w, errors.New("coupon discount cannot exceed order value"), 400)
			return
		}

		if couponOwnerType == "admin" && couponDiscount > 0 {
			// Admin coupon: Platform absorbs coupon cost by reducing its commission
			// Must respect minimum commission requirement of R$ 1.00
			commission := int32(math.Round(float64(orderValue) * commissionRate))

			maxCouponAbsorption := commission - minCommissionRequired
			if maxCouponAbsorption < 0 {
				maxCouponAbsorption = 0
			}

			if couponDiscount <= maxCouponAbsorption {
				// Normal case: platform can absorb full coupon while keeping min commission
				splitAmount = (orderValue - commission) + shippingFee
			} else {
				// Edge case: coupon exceeds what platform can absorb - reject transaction
				log.WithContext(ctx).WithFields(map[string]interface{}{
					"coupon_discount":       couponDiscount,
					"commission":            commission,
					"min_commission":        minCommissionRequired,
					"max_coupon_absorption": maxCouponAbsorption,
					"coupon_code":           payload.CouponCode,
				}).Error("Admin coupon discount exceeds maximum platform absorption capacity")
				common.RespondError(w, errors.New("coupon discount is too high for this order"), 400)
				return
			}
		} else if couponOwnerType == "company" && couponDiscount > 0 {
			// Company coupon: split = ((valorPedido - cupom) + frete) - comissão
			// Coupon reduces order value, commission applies to original order value
			commission := int32(math.Round(float64(orderValue) * commissionRate))
			orderValueWithDiscount := orderValue - couponDiscount
			if orderValueWithDiscount < 0 {
				orderValueWithDiscount = 0
			}
			splitAmount = (orderValueWithDiscount + shippingFee) - commission
		}

		log.WithContext(ctx).WithFields(map[string]interface{}{
			"coupon_code":     payload.CouponCode,
			"discount_amount": couponDiscount,
			"owner_type":      couponOwnerType,
		}).Info("Coupon validated and applied successfully")
	}

	// Apply coupon discount to final payment amount (universal amount reduction)
	finalPaymentAmount := amount
	if couponDiscount > 0 {
		finalPaymentAmount = amount - couponDiscount
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"original_amount":      amount,
			"coupon_discount":      couponDiscount,
			"final_payment_amount": finalPaymentAmount,
		}).Info("Coupon discount applied to customer payment amount")
	}

	// Fluxo sem cupom
	if payload.CouponCode == "" || couponOwnerType == "" {
		// No coupon: split = valorPedido - comissão + frete
		commission := int32(math.Round(float64(orderValue) * commissionRate))
		splitAmount = (orderValue - commission) + shippingFee
	}

	// Validate split amount to prevent Woovi error
	// For all coupon types, validate against final payment amount (what customer actually pays)
	// We need to ensure there's always some commission left for the platform

	// Validate split amount against the final payment amount
	if splitAmount >= finalPaymentAmount {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"split_amount":         splitAmount,
			"final_payment_amount": finalPaymentAmount,
			"commission_rate":      commissionRate,
			"coupon_type":          couponOwnerType,
		}).Error("Split amount cannot be equal to or greater than payment amount")
		common.RespondError(w, errors.New("invalid payment configuration: insufficient commission margin"), 400)
		return
	}

	// Additional validation: ensure minimum commission
	effectiveCommission := finalPaymentAmount - splitAmount

	if effectiveCommission < minCommissionRequired {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"effective_commission":    effectiveCommission,
			"min_commission_required": minCommissionRequired,
			"coupon_type":             couponOwnerType,
		}).Error("Commission below minimum required threshold")
		common.RespondError(w, errors.New("invalid payment configuration: commission below minimum threshold"), 400)
		return
	}

	// From now on, we have a valid order. The invoice should be created regardless of errors
	orderID := helper.GenerateULIDV2()

	// Add OrderID to context for tracking
	ctx = logger.WithOrderID(ctx, orderID)

	// Log order creation
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"original_amount":      amount,
		"final_payment_amount": finalPaymentAmount,
		"coupon_discount":      couponDiscount,
		"total_discount":       discount,
		"shipping_fee":         shippingFee, // Use conditional shipping fee
		"delivery_mode":        payload.DeliveryMode,
		"products_count":       len(payload.Products),
		"order_id":             orderID,
	}).Info("Order created, starting checkout process")

	// Insufficient stock check has been moved earlier in the flow

	var res woovi.PixSuccessResponse
	if payload.PaymentMethod == "pix" {
		log.WithContext(ctx).WithFields(map[string]interface{}{
			"payment_method":       "pix",
			"split_amount":         splitAmount,
			"final_payment_amount": finalPaymentAmount,
			"original_amount":      amount,
			"coupon_discount":      couponDiscount,
			"order_id":             orderID,
		}).Info("Creating PIX payment charge")

		body, err := h.pix.CreatePixPaymentCharge(woovi.CreatePixChargeParams{
			Context:  ctx, // Pass context with OrderID
			Value:    finalPaymentAmount,
			Discount: couponDiscount,
			OrderId:  orderID,
			Customer: woovi.Customer{
				Name:  user.Name,
				Email: user.Email,
				TaxID: user.CPF,
				Phone: user.Phones[0],
			},
			CompanyPixKey:         company.PixKey,
			CompanyCNPJ:           company.Cnpj,
			CompanyName:           company.Name,
			CompanyCommissionRate: company.CommissionRate,
			CompanyCashbackRate:   company.CashbackRate,
			CompanyExternalID:     company.ExternalID,
			SplitValue:            splitAmount,
			ShippingFee:           shippingFee, // Use conditional shipping fee
		})
		res = body

		if err != nil {
			log.WithContext(ctx).WithError(err).Error("PIX payment charge creation failed")
			invoiceParams := postgres.CreateInvoiceParams{
				UserID:          user.ID,
				CompanyID:       company.ID,
				OrderID:         orderID,
				Amount:          finalPaymentAmount,
				Coupon:          sql.NullString{String: payload.CouponCode, Valid: payload.CouponCode != ""},
				CouponType:      sql.NullString{String: couponOwnerType, Valid: couponOwnerType != ""},
				Discount:        couponDiscount,
				Status:          "failed",
				Info:            sql.NullString{String: "PIX_PAYMENT_ERROR", Valid: true},
				InfoDetails:     pgtype.JSONB{Bytes: []byte(err.Error()), Status: pgtype.Present},
				PaymentMethod:   payload.PaymentMethod,
				UserAddress:     sql.NullString{String: addressText, Valid: true},
				UserPhoneNumber: sql.NullString{String: phoneNumberText, Valid: phoneNumberText != ""},
				ShippingFee:     shippingFee, // Use conditional shipping fee
				DeliveryMode:    payload.DeliveryMode,
				CompanyAmount:   splitAmount,
			}

			_, invoiceError := h.createInvoiceWithProductsTransaction(r.Context(), invoiceParams, payload.Products, companyProducts, log)

			//TODO: salvar erros no banco de dados ou enviar para APM (new relic/ sentry)
			if invoiceError != nil {
				deleteChargeError := h.pix.DeletePixPaymentCharge(ctx, orderID)
				if deleteChargeError != nil {
					log.WithContext(ctx).WithFields(map[string]interface{}{
						"pix_error":           err.Error(),
						"invoice_error":       invoiceError.Error(),
						"delete_charge_error": deleteChargeError.Error(),
						"order_id":            orderID,
					}).Error("Multiple errors occurred during payment processing")
					common.RespondError(w, errors.New("Pix Error: "+err.Error()+" - Invoice Error: "+invoiceError.Error()+" - Delete Charge Error: "+deleteChargeError.Error()))
					return
				}
				log.WithContext(ctx).WithFields(map[string]interface{}{
					"pix_error":     err.Error(),
					"invoice_error": invoiceError.Error(),
					"order_id":      orderID,
				}).Error("PIX and invoice errors occurred")
				common.RespondError(w, errors.New("Pix Error: "+err.Error()+" - Invoice Error: "+invoiceError.Error()))
				return
			}
			log.WithContext(ctx).WithError(err).Error("Error creating pix payment")
			common.RespondError(w, errors.New("Pix Error: "+err.Error()))
			return
		}
	}

	// Create payment info details with BrCode, QrCodeImage, and PaymentLinkID from Woovi response
	var infoDetails pgtype.JSONB
	if res.Charge.BrCode != "" || res.Charge.QrCodeImage != "" || res.Charge.PaymentLinkID != "" {
		paymentDataBytes, err := helpers.CreatePaymentInfoDetails(res.Charge.BrCode, res.Charge.QrCodeImage, res.Charge.PaymentLinkID)
		if err != nil {
			log.WithContext(ctx).WithError(err).WithField("order_id", orderID).Error("Error creating payment info details")
			infoDetails = pgtype.JSONB{Bytes: []byte{}, Status: pgtype.Null}
		} else {
			infoDetails = pgtype.JSONB{Bytes: paymentDataBytes, Status: pgtype.Present}
			log.WithContext(ctx).WithField("order_id", orderID).Info("Successfully created payment info details")
		}
	} else {
		infoDetails = pgtype.JSONB{Bytes: []byte{}, Status: pgtype.Null}
	}

	invoiceParams := postgres.CreateInvoiceParams{
		UserID:          user.ID,
		CompanyID:       company.ID,
		OrderID:         orderID,
		Amount:          finalPaymentAmount,
		Coupon:          sql.NullString{String: payload.CouponCode, Valid: payload.CouponCode != ""},
		CouponType:      sql.NullString{String: couponOwnerType, Valid: couponOwnerType != ""},
		Discount:        couponDiscount,
		Status:          "pending",
		PaymentMethod:   payload.PaymentMethod,
		Info:            sql.NullString{String: "", Valid: false},
		InfoDetails:     infoDetails,
		UserAddress:     sql.NullString{String: addressText, Valid: true},
		UserPhoneNumber: sql.NullString{String: phoneNumberText, Valid: phoneNumberText != ""},
		ShippingFee:     shippingFee, // Use conditional shipping fee
		DeliveryMode:    payload.DeliveryMode,
		CompanyAmount:   splitAmount,
	}

	_, err = h.createInvoiceWithProductsTransaction(r.Context(), invoiceParams, payload.Products, companyProducts, log)
	if err != nil {
		deleteChargeError := h.pix.DeletePixPaymentCharge(ctx, orderID)
		if deleteChargeError != nil {
			log.WithContext(ctx).WithFields(map[string]interface{}{
				"invoice_error":       err.Error(),
				"delete_charge_error": deleteChargeError.Error(),
				"order_id":            orderID,
			}).Error("PIX payment successful but invoice creation and charge deletion failed")
			common.RespondError(w, errors.New("Pix success - Invoice Error: "+err.Error()+" - Delete Charge Error: "+deleteChargeError.Error()))
			return
		}
		log.WithContext(ctx).WithError(err).Error("PIX payment successful but invoice creation failed")
		common.RespondError(w, errors.New("Pix success - Invoice Error: "+err.Error()))
		return
	}

	// Log successful checkout completion
	log.WithContext(ctx).WithFields(map[string]interface{}{
		"order_id":        orderID,
		"payment_method":  payload.PaymentMethod,
		"final_amount":    finalPaymentAmount,
		"coupon_discount": couponDiscount,
		"correlation_id":  res.Charge.CorrelationID,
		"payment_link_id": res.Charge.PaymentLinkID,
	}).Info("Checkout completed successfully")

	// Call woovi.CreateAutoPaymentForPixQrCode only in staging environment. Should not used in production
	if h.pix.Url == "https://api.woovi-sandbox.com/api/v1" {
		_, err = h.pix.CreateAutoPaymentForPixQrCode(ctx, woovi.AutoPayment{
			PixQrCode: res.Charge.BrCode,
			OrderId:   orderID,
			Comment:   fmt.Sprintf("Compra realizada em %s. CNPJ: %s", company.Name, company.Cnpj),
		})
		if err != nil {
			log.WithContext(ctx).WithError(err).Error("Error creating auto payment for PIX QR code")
		}
	}

	//TODO: ajustar DTO de resposta do checkout (PIX)
	common.RespondSuccess(w, res, http.StatusOK)
}

// ProcessPaidChargeEventCallback godoc
// @Summary Transfer between sub accounts callback
// @Description Transfer between sub accounts callback
// @Tags Payment
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param body body ChargeEvent true "Charge Event"
// @Success 200 {object} nil
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} string "Unauthorized"
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/payment/process-paid-charge-event-callback [post]
func (h *PaymentHandler) ProcessPaidChargeEventCallback(w http.ResponseWriter, r *http.Request) {
	var payload ChargeEvent

	// Use injected logger for dependency injection
	logger := h.logger

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		logger.WithError(err).Error("Error decoding request body")
		common.RespondError(w, err)
		return
	}

	if payload.Evento == "teste_webhook" {
		logger.Info("Test webhook event received")
		common.RespondSuccess[interface{}](w, nil, http.StatusOK)
		return
	}

	if payload.Event != "OPENPIX:CHARGE_COMPLETED" {
		logger.Error("Invalid event type")
		common.RespondError(w, errors.New("invalid event type"))
		return
	}

	// Check if this payment has already been processed (idempotency)
	existingInvoice, err := h.queries.GetInvoiceByOrderID(r.Context(), payload.Charge.CorrelationID)
	if err == nil && existingInvoice.Status == "processing" {
		logger.WithFields(map[string]interface{}{
			"order_id": payload.Charge.CorrelationID,
			"status":   existingInvoice.Status,
		}).Info("Payment already processed, skipping duplicate processing")
		common.RespondSuccess[interface{}](w, nil, http.StatusOK)
		return
	}

	//Transaction atualiza invoice e subtrai o stock do produto
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		logger.WithError(err).Error("Error beginning transaction")
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	// Use advisory lock to prevent concurrent processing of the same order
	lockKey := fmt.Sprintf("payment_processing_%s", payload.Charge.CorrelationID)
	_, err = tx.Exec(r.Context(), "SELECT pg_advisory_xact_lock(hashtext($1))", lockKey)
	if err != nil {
		logger.WithError(err).WithField("order_id", payload.Charge.CorrelationID).Error("Error acquiring advisory lock")
		common.RespondError(w, err)
		return
	}

	// Double-check after acquiring lock
	existingInvoice, err = qtx.GetInvoiceByOrderID(r.Context(), payload.Charge.CorrelationID)
	if err == nil && existingInvoice.Status == "processing" {
		logger.WithFields(map[string]interface{}{
			"order_id": payload.Charge.CorrelationID,
			"status":   existingInvoice.Status,
		}).Info("Payment already processed after lock, skipping")
		common.RespondSuccess[interface{}](w, nil, http.StatusOK)
		return
	}

	// Update invoice status
	updatedInvoice, err := qtx.UpdateInvoiceStatusByOrderID(r.Context(), postgres.UpdateInvoiceStatusByOrderIDParams{
		OrderID: payload.Charge.CorrelationID,
		Status:  "processing",
	})
	if err != nil {
		logger.WithError(err).Error("Error updating invoice status")
		common.RespondError(w, err)
		return
	}

	//TODO: review this process. Where is the best moment to update the stock?
	// Update products stock
	err = qtx.UpdateCompanyProductsStock(r.Context(), payload.Charge.CorrelationID)
	if err != nil {
		logger.WithError(err).Error("Error updating product stock")
		common.RespondError(w, err)
		return
	}

	// Delete products with stock 0
	err = qtx.DeleteProductsWithStockZero(r.Context(), updatedInvoice.CompanyID)
	if err != nil {
		logger.WithError(err).Error("Error deleting products with stock 0")
		common.RespondError(w, err)
		return
	}

	// Commit transaction
	if err := tx.Commit(r.Context()); err != nil {
		logger.WithError(err).Error("Failed to commit transaction")
		common.RespondError(w, err)
		return
	}

	// Send FCM notification for payment completion
	if h.fcmService != nil {
		// Get invoice details for notification
		invoice, err := h.queries.GetInvoiceByOrderID(r.Context(), payload.Charge.CorrelationID)
		if err != nil {
			logger.WithError(err).Error("Error getting invoice for FCM notification")
		} else {
			// Get company information
			company, err := h.queries.GetCompanyByID(r.Context(), invoice.CompanyID)
			if err != nil {
				logger.WithError(err).Error("Error getting company info for notification")
				company.Name = "Unknown Company"
				company.ExternalID = ""
			}

			// Get user external ID
			user, err := h.queries.GetMe(r.Context(), invoice.UserID)
			if err != nil {
				logger.WithError(err).Error("Error getting user info for notification")
			} else {
				var customerNotified bool

				// 1. Try to send notification to CUSTOMER (who made the payment)
				customerPayload := fcm.NotificationPayload{
					Type:              "invoice_status_update",
					OrderID:           payload.Charge.CorrelationID,
					NewStatus:         "processing",
					OldStatus:         invoice.Status,
					StatusDescription: helpers.GetStatusDescription("processing"),
					Message:           "Pagamento confirmado - Seu pedido está sendo processado",
					UpdatedBy:         "system",
					UpdaterName:       "Sistema de Pagamento",
					UserExternalID:    user.ExternalID,
					CompanyExternalID: company.ExternalID,
					CompanyName:       company.Name,
					Timestamp:         time.Now(),
				}
				result, err := h.fcmService.SendInvoiceStatusNotification(r.Context(), customerPayload)
				if err != nil {
					logger.WithError(err).Error("Error sending FCM notification to customer for paid PIX")
					customerNotified = false
				} else if result.Success {
					logger.WithFields(map[string]interface{}{
						"order_id":         payload.Charge.CorrelationID,
						"user_external_id": user.ExternalID,
						"message_id":       result.MessageID,
					}).Info("FCM notification sent to customer for paid PIX")
					customerNotified = true
				} else {
					logger.WithFields(map[string]interface{}{
						"order_id": payload.Charge.CorrelationID,
						"error":    result.Error,
					}).Error("FCM notification failed for customer paid PIX")
					customerNotified = false
				}

				// 2. ALWAYS send notification to PARTNER (company owner) - PRIORITY
				// Partner must know about confirmed payments regardless of customer notification status
				var partnerNotified bool
				if company.OwnerID.Valid {
					companyOwner, err := h.queries.GetMe(r.Context(), company.OwnerID.Int32)
					if err != nil {
						logger.WithError(err).Error("Error getting company owner info for notification")
						partnerNotified = false
					} else {
						// Partner gets priority notification about new paid orders
						partnerMessage := "Novo pedido 🎉\nVocê tem um novo pedido esperando sua aprovação!"

						partnerPayload := fcm.NotificationPayload{
							Type:              "new_order_received",
							OrderID:           payload.Charge.CorrelationID,
							NewStatus:         "processing",
							OldStatus:         invoice.Status,
							StatusDescription: helpers.GetStatusDescription("processing"),
							Message:           partnerMessage,
							UpdatedBy:         "system",
							UpdaterName:       "Sistema de Pagamento",
							UserExternalID:    companyOwner.ExternalID,
							CompanyExternalID: company.ExternalID,
							CompanyName:       company.Name,
							Timestamp:         time.Now(),
						}
						partnerResult, partnerErr := h.fcmService.SendInvoiceStatusNotification(r.Context(), partnerPayload)
						if partnerErr != nil {
							logger.WithError(partnerErr).Error("Error sending FCM notification to partner for paid PIX")
							partnerNotified = false
						} else if partnerResult.Success {
							logger.WithFields(map[string]interface{}{
								"order_id":            payload.Charge.CorrelationID,
								"partner_external_id": companyOwner.ExternalID,
								"message_id":          partnerResult.MessageID,
							}).Info("✅ FCM notification sent to partner for paid PIX")
							partnerNotified = true
						} else {
							logger.WithFields(map[string]interface{}{
								"order_id": payload.Charge.CorrelationID,
								"error":    partnerResult.Error,
							}).Error("FCM notification failed for partner paid PIX")
							partnerNotified = false
						}
					}
				} else {
					logger.WithFields(map[string]interface{}{"company_id": company.ID}).Warn("Company has no owner for FCM notification")
					partnerNotified = false
				}

				// 3. Final status check and logging
				if partnerNotified {
					if customerNotified {
						logger.WithFields(map[string]interface{}{"order_id": payload.Charge.CorrelationID}).Info("🎉 SUCCESS: Both customer and partner notified")
					} else {
						logger.WithFields(map[string]interface{}{"order_id": payload.Charge.CorrelationID}).Warn("⚠️ PARTIAL SUCCESS: Partner notified, customer failed")
					}
				} else {
					if customerNotified {
						logger.WithFields(map[string]interface{}{"order_id": payload.Charge.CorrelationID}).Warn("⚠️ PARTIAL SUCCESS: Customer notified, partner failed")
					} else {
						logger.WithFields(map[string]interface{}{"order_id": payload.Charge.CorrelationID}).Error("🚨 CRITICAL: Neither customer nor partner notified")
					}
				}
			}
		}
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// ProcessExpiredChargeEventCallback godoc
// @Summary Process expired charge event callback
// @Description Process expired charge event callback
// @Tags Payment
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param body body ChargeEvent true "Charge Event"
// @Success 200 {object} nil
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} string "Unauthorized"
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/payment/process-expired-charge-event-callback [post]
func (h *PaymentHandler) ProcessExpiredChargeEventCallback(w http.ResponseWriter, r *http.Request) {
	var payload ChargeEvent

	// Use injected logger for dependency injection
	logger := h.logger

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		logger.WithError(err).Error("Error decoding request body")
		common.RespondError(w, err)
		return
	}

	if payload.Evento == "teste_webhook" {
		logger.Info("Test webhook event received")
		common.RespondSuccess[interface{}](w, nil, http.StatusOK)
		return
	}

	if payload.Event != "OPENPIX:CHARGE_EXPIRED" {
		logger.Error("Invalid event type")
		common.RespondError(w, errors.New("invalid event type"))
		return
	}

	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		logger.WithError(err).Error("Error beginning transaction")
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	_, err = qtx.UpdateInvoiceStatusByOrderID(r.Context(), postgres.UpdateInvoiceStatusByOrderIDParams{
		OrderID: payload.Charge.CorrelationID,
		Status:  "expired",
	})
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"order_id": payload.Charge.CorrelationID,
		}).Error("Error updating invoice status")
		common.RespondError(w, err)
		return
	}

	if err := tx.Commit(r.Context()); err != nil {
		logger.WithError(err).Error("Failed to commit transaction")
		common.RespondError(w, err)
		return
	}

	// Send FCM notification for payment expiration
	if h.fcmService != nil {
		// Get invoice details for notification
		invoice, err := h.queries.GetInvoiceByOrderID(r.Context(), payload.Charge.CorrelationID)
		if err != nil {
			logger.WithError(err).Error("Error getting invoice for FCM notification")
		} else {
			// Get company information
			company, err := h.queries.GetCompanyByID(r.Context(), invoice.CompanyID)
			if err != nil {
				logger.WithError(err).Error("Error getting company info for notification")
				company.Name = "Unknown Company"
				company.ExternalID = ""
			}

			// Get user external ID
			user, err := h.queries.GetMe(r.Context(), invoice.UserID)
			if err != nil {
				logger.WithError(err).Error("Error getting user info for notification")
			} else {
				// Create and send FCM notification
				fcmPayload := fcm.NotificationPayload{
					Type:              "invoice_status_update",
					OrderID:           payload.Charge.CorrelationID,
					NewStatus:         "expired",
					OldStatus:         invoice.Status,
					StatusDescription: helpers.GetStatusDescription("expired"),
					Message:           "Pagamento expirado - Por favor, crie um novo pedido",
					UpdatedBy:         "system",
					UpdaterName:       "Sistema de Pagamento",
					UserExternalID:    user.ExternalID,
					CompanyExternalID: company.ExternalID,
					CompanyName:       company.Name,
					Timestamp:         time.Now(),
				}
				result, err := h.fcmService.SendInvoiceStatusNotification(r.Context(), fcmPayload)
				if err != nil {
					logger.WithError(err).Error("Error sending FCM notification for expired PIX")
				} else if result.Success {
					logger.WithFields(map[string]interface{}{
						"order_id":         payload.Charge.CorrelationID,
						"user_external_id": user.ExternalID,
						"message_id":       result.MessageID,
					}).Info("FCM notification sent for expired PIX")
				} else {
					logger.WithFields(map[string]interface{}{
						"order_id": payload.Charge.CorrelationID,
						"error":    result.Error,
					}).Warn("FCM notification failed for expired PIX")
				}
			}
		}
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// createInvoiceWithProductsTransaction creates an invoice and its products within a single transaction
func (h *PaymentHandler) createInvoiceWithProductsTransaction(ctx context.Context, invoiceParams postgres.CreateInvoiceParams, products []ProductsCheckout, companyProducts []custom_models.Product, log logger.Logger) (int64, error) {
	// Start transaction
	tx, err := h.pool.Begin(ctx)
	if err != nil {
		log.WithError(err).Error("Failed to begin transaction for invoice creation")
		return 0, err
	}
	defer tx.Rollback(ctx)

	// Create queries with transaction
	qtx := h.queries.WithTx(tx)

	// Create invoice
	invoiceID, err := qtx.CreateInvoice(ctx, invoiceParams)
	if err != nil {
		log.WithError(err).Error("Failed to create invoice in transaction")
		return 0, err
	}

	// Create invoice products
	err = h.createInvoiceProductsInTransaction(ctx, qtx, invoiceID, invoiceParams.OrderID, products, companyProducts, log)
	if err != nil {
		log.WithError(err).WithField("order_id", invoiceParams.OrderID).Error("Failed to create invoice products in transaction")
		return 0, err
	}

	// Commit transaction
	if err := tx.Commit(ctx); err != nil {
		log.WithError(err).WithField("order_id", invoiceParams.OrderID).Error("Failed to commit invoice creation transaction")
		return 0, err
	}

	log.WithFields(map[string]interface{}{
		"order_id": invoiceParams.OrderID,
	}).Info("Successfully created invoice with products in transaction")
	return invoiceID, nil
}

// createInvoiceProductsInTransaction creates invoice product records within an existing transaction
func (h *PaymentHandler) createInvoiceProductsInTransaction(ctx context.Context, qtx *postgres.Queries, invoiceID int64, orderID string, products []ProductsCheckout, companyProducts []custom_models.Product, log logger.Logger) error {
	// Create a map for quick lookup of company products
	productMap := createProductMap(companyProducts)

	for _, product := range products {
		companyProduct, exists := productMap[product.ExternalID]
		if !exists {
			log.WithFields(map[string]interface{}{
				"product_external_id": product.ExternalID,
				"order_id":            orderID,
			}).Warn("Product not found in company products during invoice creation")
			continue
		}

		// Use the product ID from companyProduct instead of database lookup
		// This eliminates N+1 queries since we already have this data
		productID := companyProduct.ID

		// Create the invoice product record
		err := qtx.CreateInvoiceProduct(ctx, postgres.CreateInvoiceProductParams{
			InvoiceID:         invoiceID,
			ProductID:         productID,
			Quantity:          product.Quantity,
			UnitPrice:         product.Price,
			Discount:          product.Discount,
			ProductName:       companyProduct.Name,
			ProductEan:        companyProduct.Ean,
			ProductExternalID: product.ExternalID,
			ProductBrand:      sql.NullString{String: companyProduct.Brand, Valid: companyProduct.Brand != ""},
			ProductImage:      sql.NullString{String: companyProduct.Image, Valid: companyProduct.Image != ""},
		})

		if err != nil {
			log.WithError(err).WithFields(map[string]interface{}{
				"product_external_id": product.ExternalID,
				"order_id":            orderID,
			}).Error("Failed to create invoice product")
			return err
		}
	}

	return nil
}

func (e *StockValidationError) Error() string {
	data := map[string]interface{}{
		"order_id": e.OrderID,
		"errors":   e.Errors,
	}
	b, _ := json.Marshal(data)
	return string(b)
}

func (e *EnhancedStockValidationError) Error() string {
	return "INSUFFICIENT_STOCK"
}

// createProductMap creates a lookup map for products by external ID
func createProductMap(products []custom_models.Product) map[string]custom_models.Product {
	productMap := make(map[string]custom_models.Product, len(products))
	for _, p := range products {
		productMap[p.ExternalID] = p
	}
	return productMap
}
