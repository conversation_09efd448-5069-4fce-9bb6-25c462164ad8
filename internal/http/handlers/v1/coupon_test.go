package handlers

import (
	"testing"
)

func TestCentsToReais(t *testing.T) {
	tests := []struct {
		name     string
		cents    int32
		expected float64
	}{
		{"Zero cents", 0, 0.00},
		{"One real", 100, 1.00},
		{"Two fifty", 250, 2.50},
		{"Ten reais", 1000, 10.00},
		{"Fifteen reais", 1500, 15.00},
		{"Large amount", 999999, 9999.99},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CentsToReais(tt.cents)
			if result != tt.expected {
				t.Errorf("CentsToReais(%d) = %.2f, want %.2f", tt.cents, result, tt.expected)
			}
		})
	}
}

func TestReaisToCents(t *testing.T) {
	tests := []struct {
		name     string
		reais    float64
		expected int32
	}{
		{"Zero reais", 0.00, 0},
		{"One real", 1.00, 100},
		{"Two fifty", 2.50, 250},
		{"Ten reais", 10.00, 1000},
		{"Fifteen reais", 15.00, 1500},
		{"Precision test", 2.99, 299},
		{"Rounding test", 2.995, 300}, // Should round up
		{"Large amount", 9999.99, 999999},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ReaisToCents(tt.reais)
			if result != tt.expected {
				t.Errorf("ReaisToCents(%.2f) = %d, want %d", tt.reais, result, tt.expected)
			}
		})
	}
}

func TestConvertCouponValueToCents(t *testing.T) {
	tests := []struct {
		name       string
		value      float64
		couponType string
		expected   int32
	}{
		// Fixed coupon tests
		{"Fixed 5 reais", 5.00, "fixed", 500},
		{"Fixed 10 reais", 10.00, "fixed", 1000},
		{"Fixed 2.50 reais", 2.50, "fixed", 250},

		// Percentage coupon tests
		{"Percentage 10%", 10.0, "percentage", 1000},
		{"Percentage 5%", 5.0, "percentage", 500},
		{"Percentage 15%", 15.0, "percentage", 1500},
		{"Percentage 0.5%", 0.5, "percentage", 50},

		// Invalid type
		{"Invalid type", 10.0, "invalid", 0},
		{"Empty type", 10.0, "", 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertCouponValueToCents(tt.value, tt.couponType)
			if result != tt.expected {
				t.Errorf("ConvertCouponValueToCents(%.2f, %s) = %d, want %d",
					tt.value, tt.couponType, result, tt.expected)
			}
		})
	}
}

func TestRoundTripConversion(t *testing.T) {
	// Test that converting cents to reais and back gives the same result
	testValues := []int32{0, 100, 250, 500, 1000, 1500, 2550, 9999}

	for _, cents := range testValues {
		t.Run("Round trip", func(t *testing.T) {
			reais := CentsToReais(cents)
			backToCents := ReaisToCents(reais)

			if backToCents != cents {
				t.Errorf("Round trip failed: %d -> %.2f -> %d", cents, reais, backToCents)
			}
		})
	}
}

func TestCouponValueConversions(t *testing.T) {
	// Test realistic coupon scenarios
	scenarios := []struct {
		name        string
		valueReais  float64
		couponType  string
		expectedMsg string
	}{
		{"R$ 5 fixed coupon", 5.00, "fixed", "Should convert to 500 cents"},
		{"10% percentage coupon", 10.0, "percentage", "Should convert to 1000 (10 * 100)"},
		{"R$ 2.50 fixed coupon", 2.50, "fixed", "Should convert to 250 cents"},
		{"5% percentage coupon", 5.0, "percentage", "Should convert to 500 (5 * 100)"},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			cents := ConvertCouponValueToCents(scenario.valueReais, scenario.couponType)

			// Verify the conversion makes sense
			if scenario.couponType == "fixed" {
				expectedCents := ReaisToCents(scenario.valueReais)
				if cents != expectedCents {
					t.Errorf("Fixed coupon conversion failed: %.2f -> %d, expected %d",
						scenario.valueReais, cents, expectedCents)
				}
			} else if scenario.couponType == "percentage" {
				expectedCents := int32(scenario.valueReais * 100)
				if cents != expectedCents {
					t.Errorf("Percentage coupon conversion failed: %.2f -> %d, expected %d",
						scenario.valueReais, cents, expectedCents)
				}
			}

			t.Logf("%s: %.2f %s -> %d cents",
				scenario.name, scenario.valueReais, scenario.couponType, cents)
		})
	}
}

func TestFormatCurrency(t *testing.T) {
	tests := []struct {
		name     string
		cents    int32
		expected string
	}{
		{"Zero", 0, "R$ 0.00"},
		{"One real", 100, "R$ 1.00"},
		{"Two fifty", 250, "R$ 2.50"},
		{"Ten reais", 1000, "R$ 10.00"},
		{"Large amount", 999999, "R$ 9999.99"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatCurrency(tt.cents)
			if result != tt.expected {
				t.Errorf("FormatCurrency(%d) = %s, want %s", tt.cents, result, tt.expected)
			}
		})
	}
}

func BenchmarkCentsToReais(b *testing.B) {
	for i := 0; i < b.N; i++ {
		CentsToReais(250)
	}
}

func BenchmarkReaisToCents(b *testing.B) {
	for i := 0; i < b.N; i++ {
		ReaisToCents(2.50)
	}
}

func BenchmarkConvertCouponValueToCents(b *testing.B) {
	for i := 0; i < b.N; i++ {
		ConvertCouponValueToCents(10.0, "fixed")
	}
}

func BenchmarkFormatCurrency(b *testing.B) {
	for i := 0; i < b.N; i++ {
		FormatCurrency(250)
	}
}
