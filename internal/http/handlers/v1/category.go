package handlers

import (
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strconv"

	"log"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/helpers"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/internal/integrations/storage"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
)

type CategoryHandler struct {
	queries *postgres.Queries
	storage *storage.Storage
	env     *config.Environment
}

type CreateCategoryRequest struct {
	Name  string                `form:"name" validate:"required"`
	Image *multipart.FileHeader `form:"image" validate:"required"`
}

type GetActiveCategoriesResponse struct {
	Name       string `json:"name" example:"Bebidas"`
	Image      string `json:"image" example:"https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg"`
	ExternalID string `json:"external_id" example:"123456"`
}

type GetActiveCategoriesPaginatedResponse struct {
	PageNumber int                           `json:"pageNumber"`
	Limit      int                           `json:"limit"`
	TotalItems int                           `json:"totalItems"`
	TotalPages int                           `json:"totalPages"`
	Data       []GetActiveCategoriesResponse `json:"data"`
}

type CreateCategoryResponse struct {
	ExternalID string `json:"external_id" example:"123456"`
}

type UpdateCategoryRequest struct {
	Name string `json:"name" validate:"required"`
}

type CreateCategorySuccessResponse = common.SuccessResponse[CreateCategoryResponse]
type GetActiveCategoriesSuccessResponse = common.SuccessResponseWithPagination[GetActiveCategoriesPaginatedResponse]

func NewCategoryHandler(env *config.Environment, queries *postgres.Queries, storage *storage.Storage) *chi.Mux {
	h := &CategoryHandler{
		queries: queries,
		storage: storage,
		env:     env,
	}
	router := chi.NewRouter()
	m := middlewares.New(env, queries)
	adminRouter := router.With(m.AdminPermissions)

	adminRouter.Post("/", h.Create)
	adminRouter.Put("/{external_id}", h.Update)
	adminRouter.Patch("/{external_id}", h.UpdateCategoryImage)
	router.Get("/", h.GetActiveCategories)

	return router
}

// Create godoc
// @Summary Create a new category
// @Description Create a new category
// @Tags Category
// @Security Bearer
// @Accept multipart/form-data
// @Produce json
// @Param name formData string true "Category name"
// @Param image formData file true "Category image"
// @Success 201 {object} CreateCategorySuccessResponse "Category created"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/category [post]
func (h *CategoryHandler) Create(w http.ResponseWriter, r *http.Request) {
	r.Body = http.MaxBytesReader(w, r.Body, MultipartFormMaxSize)
	if r.Body != nil {
		defer r.Body.Close()
	}

	mr, err := r.MultipartReader()
	if err != nil {
		log.Printf("Error creating multipart reader: %v\n", err)
		common.RespondError(w, fmt.Errorf("could not create multipart reader: %v", err), http.StatusBadRequest)
		return
	}

	file, fileHeader, formValues, err := helpers.DeferParts(mr)
	if err != nil {
		log.Printf("Error reading multipart form: %v\n", err)
		common.RespondError(w, fmt.Errorf("could not read multipart form: %v", err), http.StatusBadRequest)
		return
	}

	if file == nil || fileHeader == nil {
		log.Println("File or file header is nil")
		common.RespondError(w, fmt.Errorf("file is required"), http.StatusBadRequest)
		return
	}

	payload := CreateCategoryRequest{
		Name:  formValues["name"],
		Image: fileHeader,
	}

	helper := helpers.New()
	externalID := helper.GenerateULIDV2()

	filename := fmt.Sprintf("%s%s", externalID, filepath.Ext(fileHeader.Filename))
	imageURL, err := h.storage.UploadImage(r.Context(), filename, file)
	if err != nil {
		log.Printf("Error uploading image: %v\n", err)
		common.RespondError(w, err)
		return
	}

	_, err = h.queries.CreateCategory(r.Context(), postgres.CreateCategoryParams{
		Name:       payload.Name,
		Image:      imageURL,
		ExternalID: externalID,
	})
	if err != nil {
		log.Printf("Error creating category: %v\n", err)
		common.RespondError(w, err)
		// Delete the image if category creation fails
		if deleteErr := h.storage.DeleteImage(r.Context(), filename); deleteErr != nil {
			log.Printf("Error deleting image: %v\n", deleteErr)
		}
		return
	}

	common.RespondSuccess(w, externalID, http.StatusCreated)
}

// GetActiveCategories godoc
// @Summary Get active categories
// @Description Get active categories
// @Tags Category
// @Security Bearer
// @Accept json
// @Produce json
// @Param page query string false "Page"
// @Param limit query string false "Limit"
// @Success 200 {object} GetActiveCategoriesSuccessResponse "List of categories"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/category [get]
func (h *CategoryHandler) GetActiveCategories(w http.ResponseWriter, r *http.Request) {
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	offset := (page - 1) * limit

	categories, err := h.queries.GetCategories(r.Context(), postgres.GetCategoriesParams{
		Offset: int32(offset),
		Limit:  int32(limit),
	})

	if err != nil {
		log.Printf("Error getting active categories: %v\n", err)
		common.RespondError(w, err)
		return
	}

	totalItems := 0
	response := make([]GetActiveCategoriesResponse, len(categories))
	for i, category := range categories {
		totalItems = int(category.TotalCount)
		response[i] = GetActiveCategoriesResponse{
			Name:       category.Name,
			Image:      category.Image,
			ExternalID: category.ExternalID,
		}
	}

	common.RespondSuccessWithPagination(w, response, page, limit, totalItems)
}

// Update godoc
// @Summary Update a category
// @Description Update a category
// @Tags Category
// @Accept json
// @Produce json
// @Param external_id path string true "Category external ID"
// @Param payload body UpdateCategoryRequest true "Category data"
// @Success 200 {object} interface{} "Category updated"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/category/{external_id} [put]
func (h *CategoryHandler) Update(w http.ResponseWriter, r *http.Request) {
	externalID := chi.URLParam(r, "external_id")
	if externalID == "" {
		common.RespondError(w, fmt.Errorf("invalid externalID"), 400)
		return
	}

	payload := UpdateCategoryRequest{}
	helper := helpers.New()

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	err = h.queries.UpdateCategory(r.Context(), postgres.UpdateCategoryParams{
		Name:       payload.Name,
		ExternalID: externalID,
	})
	if err != nil {
		log.Printf("Error updating category: %v\n", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// UpdateCategoryImage godoc
// @Summary Update category image
// @Description Update category image
// @Tags Category
// @Security Bearer
// @Accept multipart/form-data
// @Produce json
// @Param external_id path string true "Category external ID"
// @Param image formData file true "Category image"
// @Success 200 {object} interface{} "Category image updated"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/category/{external_id} [patch]
func (h *CategoryHandler) UpdateCategoryImage(w http.ResponseWriter, r *http.Request) {
	externalID := chi.URLParam(r, "external_id")
	if externalID == "" {
		common.RespondError(w, fmt.Errorf("invalid externalID"), 400)
		return
	}

	r.Body = http.MaxBytesReader(w, r.Body, MultipartFormMaxSize)
	if r.Body != nil {
		defer r.Body.Close()
	}

	mr, err := r.MultipartReader()
	if err != nil {
		log.Printf("Error creating multipart reader: %v\n", err)
		common.RespondError(w, fmt.Errorf("could not create multipart reader: %v", err), http.StatusBadRequest)
		return
	}

	file, fileHeader, _, err := helpers.DeferParts(mr)
	if err != nil {
		log.Printf("Error reading multipart form: %v\n", err)
		common.RespondError(w, fmt.Errorf("could not read multipart form: %v", err), http.StatusBadRequest)
		return
	}

	if file == nil || fileHeader == nil {
		log.Println("File or file header is nil")
		common.RespondError(w, fmt.Errorf("file is required"), http.StatusBadRequest)
		return
	}

	filename := fmt.Sprintf("%s%s", externalID, filepath.Ext(fileHeader.Filename))

	imageURL, err := h.storage.UploadImage(r.Context(), filename, file)
	if err != nil {
		log.Printf("Error uploading image: %v\n", err)
		common.RespondError(w, err)
		return
	}

	err = h.queries.UpdateCategoryImage(r.Context(), postgres.UpdateCategoryImageParams{
		Image:      imageURL,
		ExternalID: externalID,
	})

	if err != nil {
		log.Printf("Error updating category image: %v\n", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}
