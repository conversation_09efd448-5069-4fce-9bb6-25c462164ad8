package handlers

import (
	"errors"
	"log"
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
)

type SearchHandler struct {
	queries *postgres.Queries
	env     *config.Environment
}

type SearchResponse struct {
	ProductName        string  `json:"product_name"`
	ProductImage       *string `json:"product_image,omitempty"`
	ProductBrand       *string `json:"product_brand,omitempty"`
	ProductExternalID  string  `json:"product_external_id"`
	ProductIs18Plus    bool    `json:"product_is_18_plus"`
	CategoryName       *string `json:"category_name,omitempty"`
	CategoryImage      *string `json:"category_image,omitempty"`
	CategoryExternalID *string `json:"category_external_id,omitempty"`
	CompanyName        *string `json:"company_name,omitempty"`
	CompanyExternalID  *string `json:"company_external_id,omitempty"`
	Price              *int32  `json:"price,omitempty"`
	Stock              *int32  `json:"stock,omitempty"`
	Discount           *int32  `json:"discount,omitempty"`
	SearchRank         float64 `json:"search_rank"`
}

type SearchSuccessResponse = common.SuccessResponseWithPagination[SearchResponse]

func NewSearchHandler(env *config.Environment, queries *postgres.Queries) *chi.Mux {
	h := &SearchHandler{
		queries: queries,
		env:     env,
	}
	router := chi.NewRouter()
	router.Group(func(r chi.Router) {
		r.Get("/global/{query}", h.GlobalSearch)
		r.Get("/category/{category_id}/{query}", h.CategorySearch)
		r.Get("/company/{company_id}/{query}", h.CompanySearch)
		r.Get("/company/{company_id}/category/{category_id}/{query}", h.CompanyCategorySearch)
	})
	return router
}

// GlobalSearch godoc
// @Summary Global Product Search
// @Description Search across all products in the system
// @Tags Search
// @Accept json
// @Produce json
// @Param query path string true "Search query"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} SearchSuccessResponse "Search results with pagination"
// @Failure 400 {object} common.ErrorResponse  "Bad request"
// @Failure 500 {object} common.ErrorResponse  "Internal server error"
// @Router /v1/search/global/{query} [get]
func (h *SearchHandler) GlobalSearch(w http.ResponseWriter, r *http.Request) {
	query := chi.URLParam(r, "query")
	if query == "" {
		log.Println("Query parameter is empty")
		common.RespondError(w, errors.New("query parameter is required"), http.StatusBadRequest)
		return
	}

	page, limit, offset := h.parsePagination(r)

	results, err := h.queries.SearchGlobal(r.Context(), postgres.SearchGlobalParams{
		PlaintoTsquery: query,
		Limit:          int32(limit),
		Offset:         int32(offset),
	})
	if err != nil {
		log.Printf("Error in global search: %v", err)
		common.RespondError(w, errors.New("error searching products"), http.StatusInternalServerError)
		return
	}

	h.respondWithGlobalResults(w, results, page, limit)
}

// CategorySearch godoc
// @Summary Category Product Search
// @Description Search products within a specific category
// @Tags Search
// @Accept json
// @Produce json
// @Param category_id path string true "Category external ID"
// @Param query path string true "Search query"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} SearchSuccessResponse "Search results with pagination"
// @Failure 400 {object} common.ErrorResponse  "Bad request"
// @Failure 500 {object} common.ErrorResponse  "Internal server error"
// @Router /v1/search/category/{category_id}/{query} [get]
func (h *SearchHandler) CategorySearch(w http.ResponseWriter, r *http.Request) {
	query := chi.URLParam(r, "query")
	categoryID := chi.URLParam(r, "category_id")

	if query == "" || categoryID == "" {
		log.Println("Query or category_id parameter is empty")
		common.RespondError(w, errors.New("query and category_id parameters are required"), http.StatusBadRequest)
		return
	}

	page, limit, offset := h.parsePagination(r)

	results, err := h.queries.SearchByCategory(r.Context(), postgres.SearchByCategoryParams{
		PlaintoTsquery: query,
		ExternalID:     categoryID,
		Limit:          int32(limit),
		Offset:         int32(offset),
	})
	if err != nil {
		log.Printf("Error in category search: %v", err)
		common.RespondError(w, errors.New("error searching products"), http.StatusInternalServerError)
		return
	}

	h.respondWithCategoryResults(w, results, page, limit)
}

// CompanySearch godoc
// @Summary Company Product Search
// @Description Search products within a specific company
// @Tags Search
// @Accept json
// @Produce json
// @Param company_id path string true "Company external ID"
// @Param query path string true "Search query"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} SearchSuccessResponse "Search results with pagination"
// @Failure 400 {object} common.ErrorResponse  "Bad request"
// @Failure 500 {object} common.ErrorResponse  "Internal server error"
// @Router /v1/search/company/{company_id}/{query} [get]
func (h *SearchHandler) CompanySearch(w http.ResponseWriter, r *http.Request) {
	query := chi.URLParam(r, "query")
	companyID := chi.URLParam(r, "company_id")

	if query == "" || companyID == "" {
		log.Println("Query or company_id parameter is empty")
		common.RespondError(w, errors.New("query and company_id parameters are required"), http.StatusBadRequest)
		return
	}

	page, limit, offset := h.parsePagination(r)

	results, err := h.queries.SearchByCompany(r.Context(), postgres.SearchByCompanyParams{
		PlaintoTsquery: query,
		ExternalID:     companyID,
		Limit:          int32(limit),
		Offset:         int32(offset),
	})
	if err != nil {
		log.Printf("Error in company search: %v", err)
		common.RespondError(w, errors.New("error searching products"), http.StatusInternalServerError)
		return
	}

	h.respondWithCompanyResults(w, results, page, limit)
}

// CompanyCategorySearch godoc
// @Summary Company Category Product Search
// @Description Search products within a specific company and category
// @Tags Search
// @Accept json
// @Produce json
// @Param company_id path string true "Company external ID"
// @Param category_id path string true "Category external ID"
// @Param query path string true "Search query"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} SearchSuccessResponse "Search results with pagination"
// @Failure 400 {object} common.ErrorResponse  "Bad request"
// @Failure 500 {object} common.ErrorResponse  "Internal server error"
// @Router /v1/search/company/{company_id}/category/{category_id}/{query} [get]
func (h *SearchHandler) CompanyCategorySearch(w http.ResponseWriter, r *http.Request) {
	query := chi.URLParam(r, "query")
	companyID := chi.URLParam(r, "company_id")
	categoryID := chi.URLParam(r, "category_id")

	if query == "" || companyID == "" || categoryID == "" {
		log.Println("Query, company_id, or category_id parameter is empty")
		common.RespondError(w, errors.New("query, company_id, and category_id parameters are required"), http.StatusBadRequest)
		return
	}

	page, limit, offset := h.parsePagination(r)

	results, err := h.queries.SearchByCompanyAndCategory(r.Context(), postgres.SearchByCompanyAndCategoryParams{
		PlaintoTsquery: query,
		ExternalID:     companyID,
		ExternalID_2:   categoryID,
		Limit:          int32(limit),
		Offset:         int32(offset),
	})
	if err != nil {
		log.Printf("Error in company+category search: %v", err)
		common.RespondError(w, errors.New("error searching products"), http.StatusInternalServerError)
		return
	}

	h.respondWithCompanyCategoryResults(w, results, page, limit)
}

// Helper method to parse pagination parameters
func (h *SearchHandler) parsePagination(r *http.Request) (page, limit, offset int) {
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page = 1
	limit = 10

	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	offset = (page - 1) * limit
	return page, limit, offset
}

// Helper method to respond with global search results (no company data)
func (h *SearchHandler) respondWithGlobalResults(w http.ResponseWriter, searchResults []postgres.SearchGlobalRow, page, limit int) {
	if len(searchResults) == 0 {
		common.RespondSuccessWithPagination(w, []SearchResponse{}, page, limit, 0)
		return
	}

	results := make([]SearchResponse, len(searchResults))
	totalItems := int(searchResults[0].TotalCount)

	for i, item := range searchResults {
		result := SearchResponse{
			ProductName:       item.ProductName,
			ProductExternalID: item.ProductExternalID,
			ProductIs18Plus:   item.Is18Plus,
			SearchRank:        float64(item.SearchRank),
		}

		// Handle nullable fields
		if item.ProductImage.Valid {
			result.ProductImage = &item.ProductImage.String
		}
		if item.ProductBrand.Valid {
			result.ProductBrand = &item.ProductBrand.String
		}
		if item.CategoryName.Valid {
			result.CategoryName = &item.CategoryName.String
		}
		if item.CategoryImage.Valid {
			result.CategoryImage = &item.CategoryImage.String
		}
		if item.CategoryExternalID.Valid {
			result.CategoryExternalID = &item.CategoryExternalID.String
		}

		results[i] = result
	}

	common.RespondSuccessWithPagination(w, results, page, limit, totalItems)
}

// Helper method to respond with company search results (includes company data)
func (h *SearchHandler) respondWithCompanyResults(w http.ResponseWriter, searchResults []postgres.SearchByCompanyRow, page, limit int) {
	if len(searchResults) == 0 {
		common.RespondSuccessWithPagination(w, []SearchResponse{}, page, limit, 0)
		return
	}

	results := make([]SearchResponse, len(searchResults))
	totalItems := int(searchResults[0].TotalCount)

	for i, item := range searchResults {
		result := SearchResponse{
			ProductName:       item.ProductName,
			ProductExternalID: item.ProductExternalID,
			ProductIs18Plus:   item.Is18Plus,
			SearchRank:        float64(item.SearchRank),
		}

		// Handle nullable fields
		if item.ProductImage.Valid {
			result.ProductImage = &item.ProductImage.String
		}
		if item.ProductBrand.Valid {
			result.ProductBrand = &item.ProductBrand.String
		}
		if item.CategoryName.Valid {
			result.CategoryName = &item.CategoryName.String
		}
		if item.CategoryImage.Valid {
			result.CategoryImage = &item.CategoryImage.String
		}
		if item.CategoryExternalID.Valid {
			result.CategoryExternalID = &item.CategoryExternalID.String
		}

		// Company fields
		result.CompanyName = &item.CompanyName
		result.CompanyExternalID = &item.CompanyExternalID

		// Handle company-specific fields (price, discount, stock)
		var priceFloat float64
		item.Price.AssignTo(&priceFloat)
		price := int32(priceFloat)
		result.Price = &price

		var discountFloat float64
		item.Discount.AssignTo(&discountFloat)
		discount := int32(discountFloat)
		result.Discount = &discount

		result.Stock = &item.Stock

		results[i] = result
	}

	common.RespondSuccessWithPagination(w, results, page, limit, totalItems)
}

// Helper method to respond with category search results (no company data)
func (h *SearchHandler) respondWithCategoryResults(w http.ResponseWriter, searchResults []postgres.SearchByCategoryRow, page, limit int) {
	if len(searchResults) == 0 {
		common.RespondSuccessWithPagination(w, []SearchResponse{}, page, limit, 0)
		return
	}

	results := make([]SearchResponse, len(searchResults))
	totalItems := int(searchResults[0].TotalCount)

	for i, item := range searchResults {
		result := SearchResponse{
			ProductName:       item.ProductName,
			ProductExternalID: item.ProductExternalID,
			ProductIs18Plus:   item.Is18Plus,
			SearchRank:        float64(item.SearchRank),
		}

		// Handle nullable fields
		if item.ProductImage.Valid {
			result.ProductImage = &item.ProductImage.String
		}
		if item.ProductBrand.Valid {
			result.ProductBrand = &item.ProductBrand.String
		}
		// Handle nullable category fields
		if item.CategoryName.Valid {
			result.CategoryName = &item.CategoryName.String
		}
		if item.CategoryImage.Valid {
			result.CategoryImage = &item.CategoryImage.String
		}
		if item.CategoryExternalID.Valid {
			result.CategoryExternalID = &item.CategoryExternalID.String
		}

		results[i] = result
	}

	common.RespondSuccessWithPagination(w, results, page, limit, totalItems)
}

// Helper method to respond with company+category search results (includes company data)
func (h *SearchHandler) respondWithCompanyCategoryResults(w http.ResponseWriter, searchResults []postgres.SearchByCompanyAndCategoryRow, page, limit int) {
	if len(searchResults) == 0 {
		common.RespondSuccessWithPagination(w, []SearchResponse{}, page, limit, 0)
		return
	}

	results := make([]SearchResponse, len(searchResults))
	totalItems := int(searchResults[0].TotalCount)

	for i, item := range searchResults {
		result := SearchResponse{
			ProductName:       item.ProductName,
			ProductExternalID: item.ProductExternalID,
			ProductIs18Plus:   item.Is18Plus,
			SearchRank:        float64(item.SearchRank),
		}

		// Handle nullable fields
		if item.ProductImage.Valid {
			result.ProductImage = &item.ProductImage.String
		}
		if item.ProductBrand.Valid {
			result.ProductBrand = &item.ProductBrand.String
		}
		// Handle nullable category fields
		if item.CategoryName.Valid {
			result.CategoryName = &item.CategoryName.String
		}
		if item.CategoryImage.Valid {
			result.CategoryImage = &item.CategoryImage.String
		}
		if item.CategoryExternalID.Valid {
			result.CategoryExternalID = &item.CategoryExternalID.String
		}

		// Company fields
		result.CompanyName = &item.CompanyName
		result.CompanyExternalID = &item.CompanyExternalID

		// Handle company-specific fields (price, discount, stock)
		var priceFloat float64
		item.Price.AssignTo(&priceFloat)
		price := int32(priceFloat)
		result.Price = &price

		var discountFloat float64
		item.Discount.AssignTo(&discountFloat)
		discount := int32(discountFloat)
		result.Discount = &discount

		result.Stock = &item.Stock

		results[i] = result
	}

	common.RespondSuccessWithPagination(w, results, page, limit, totalItems)
}
