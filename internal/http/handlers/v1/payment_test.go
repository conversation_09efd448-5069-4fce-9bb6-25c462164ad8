package handlers

import (
	"math"
	"testing"
)

// TestScenario represents a test case for split calculations
type TestScenario struct {
	Name               string
	ValorPedido        int32
	Frete              int32
	CommissionRate     float64
	CouponDiscount     int32
	CouponOwnerType    string
	ExpectedSplit      int32
	ExpectedCommission int32
	ExpectedTotalValue int32
}

// calculateSplit implements the same logic as the payment handler
func calculateSplit(valorPedido, frete int32, commissionRate float64, couponDiscount int32, couponOwnerType string) (int32, int32, int32) {
	var splitAmount int32
	var effectiveCommission int32
	var totalValue int32

	if couponOwnerType == "admin" && couponDiscount > 0 {
		// Admin coupon: Customer pays discounted amount, platform absorbs coupon cost
		// Split = (valorPedido - comissão) + frete
		// Partner receives full amount, platform absorbs coupon cost
		commission := int32(math.Round(float64(valorPedido) * commissionRate))
		minCommissionRequired := int32(100) // R$ 1.00

		maxCouponAbsorption := commission - minCommissionRequired
		if maxCouponAbsorption < 0 {
			maxCouponAbsorption = 0
		}

		if couponDiscount <= maxCouponAbsorption {
			// Normal case: platform can absorb full coupon while keeping min commission
			splitAmount = (valorPedido - commission) + frete
		} else {
			// Edge case: coupon exceeds what platform can absorb - this should be rejected
			// For testing purposes, we'll simulate the rejection by returning invalid values
			splitAmount = -1 // Invalid split to indicate rejection
		}
		effectiveCommission = (valorPedido + frete - couponDiscount) - splitAmount // What platform gets
		totalValue = valorPedido + frete - couponDiscount                          // Customer pays discounted amount
	} else if couponOwnerType == "company" && couponDiscount > 0 {
		// Company coupon: split = ((valorPedido - cupom) + frete) - comissão
		commission := int32(math.Round(float64(valorPedido) * commissionRate))
		valorComDesconto := valorPedido - couponDiscount
		if valorComDesconto < 0 {
			valorComDesconto = 0
		}
		splitAmount = (valorComDesconto + frete) - commission
		effectiveCommission = commission
		totalValue = valorComDesconto + frete
	} else {
		// No coupon: split = (valorPedido - comissão) + frete
		commission := int32(math.Round(float64(valorPedido) * commissionRate))
		splitAmount = (valorPedido - commission) + frete
		effectiveCommission = commission
		totalValue = valorPedido + frete
	}

	return splitAmount, effectiveCommission, totalValue
}

func TestSplitCalculations(t *testing.T) {
	testScenarios := []TestScenario{
		// Cenários sem cupom
		{
			Name:               "Sem cupom - Valor baixo",
			ValorPedido:        5000, // R$ 50,00
			Frete:              300,  // R$ 3,00
			CommissionRate:     0.05, // 5%
			CouponDiscount:     0,
			CouponOwnerType:    "",
			ExpectedSplit:      5050, // (5000 - 250) + 300
			ExpectedCommission: 250,  // 5% de 5000
			ExpectedTotalValue: 5300, // 5000 + 300
		},
		{
			Name:               "Sem cupom - Valor alto",
			ValorPedido:        20000, // R$ 200,00
			Frete:              500,   // R$ 5,00
			CommissionRate:     0.08,  // 8%
			CouponDiscount:     0,
			CouponOwnerType:    "",
			ExpectedSplit:      18900, // (20000 - 1600) + 500
			ExpectedCommission: 1600,  // 8% de 20000
			ExpectedTotalValue: 20500, // 20000 + 500
		},

		// Cenários com cupom admin
		{
			Name:               "Cupom admin - Desconto menor que comissão",
			ValorPedido:        10000, // R$ 100,00
			Frete:              400,   // R$ 4,00
			CommissionRate:     0.06,  // 6%
			CouponDiscount:     300,   // R$ 3,00
			CouponOwnerType:    "admin",
			ExpectedSplit:      9800,  // (10000 - 600) + 400
			ExpectedCommission: 300,   // Platform commission reduced by coupon (600 - 300)
			ExpectedTotalValue: 10100, // Customer pays: 10000 + 400 - 300
		},
		{
			Name:               "Cupom admin - Desconto próximo ao limite",
			ValorPedido:        10000, // R$ 100,00
			Frete:              400,   // R$ 4,00
			CommissionRate:     0.05,  // 5% = 500
			CouponDiscount:     400,   // R$ 4,00 (within max absorption: 500-100=400)
			CouponOwnerType:    "admin",
			ExpectedSplit:      9900,  // (10000 - 500) + 400
			ExpectedCommission: 100,   // Platform keeps minimum commission (500 - 400 = 100)
			ExpectedTotalValue: 10000, // Customer pays: 10000 + 400 - 400
		},
		{
			Name:               "Cupom admin - Desconto dentro do limite",
			ValorPedido:        10000, // R$ 100,00
			Frete:              400,   // R$ 4,00
			CommissionRate:     0.08,  // 8% = 800
			CouponDiscount:     600,   // R$ 6,00 (within max absorption: 800-100=700)
			CouponOwnerType:    "admin",
			ExpectedSplit:      9600, // (10000 - 800) + 400
			ExpectedCommission: 200,  // Platform commission reduced (800 - 600 = 200)
			ExpectedTotalValue: 9800, // Customer pays: 10000 + 400 - 600
		},

		// Cenários com cupom company
		{
			Name:               "Cupom company - Desconto pequeno",
			ValorPedido:        15000, // R$ 150,00
			Frete:              600,   // R$ 6,00
			CommissionRate:     0.07,  // 7%
			CouponDiscount:     800,   // R$ 8,00
			CouponOwnerType:    "company",
			ExpectedSplit:      13750, // ((15000 - 800) + 600) - 1050
			ExpectedCommission: 1050,  // 7% de 15000
			ExpectedTotalValue: 14800, // (15000 - 800) + 600
		},
		{
			Name:               "Cupom company - Desconto grande",
			ValorPedido:        12000, // R$ 120,00
			Frete:              500,   // R$ 5,00
			CommissionRate:     0.05,  // 5%
			CouponDiscount:     3000,  // R$ 30,00
			CouponOwnerType:    "company",
			ExpectedSplit:      8900, // ((12000 - 3000) + 500) - 600
			ExpectedCommission: 600,  // 5% de 12000
			ExpectedTotalValue: 9500, // (12000 - 3000) + 500
		},

		// Cenários edge cases
		{
			Name:               "Comissão muito baixa",
			ValorPedido:        8000, // R$ 80,00
			Frete:              200,  // R$ 2,00
			CommissionRate:     0.01, // 1%
			CouponDiscount:     0,
			CouponOwnerType:    "",
			ExpectedSplit:      8120, // (8000 - 80) + 200
			ExpectedCommission: 80,   // 1% de 8000
			ExpectedTotalValue: 8200, // 8000 + 200
		},
		{
			Name:               "Frete zero",
			ValorPedido:        6000, // R$ 60,00
			Frete:              0,    // R$ 0,00
			CommissionRate:     0.04, // 4%
			CouponDiscount:     0,
			CouponOwnerType:    "",
			ExpectedSplit:      5760, // (6000 - 240) + 0
			ExpectedCommission: 240,  // 4% de 6000
			ExpectedTotalValue: 6000, // 6000 + 0
		},
		{
			Name:               "Cupom admin com frete alto",
			ValorPedido:        7000, // R$ 70,00
			Frete:              1000, // R$ 10,00
			CommissionRate:     0.06, // 6%
			CouponDiscount:     200,  // R$ 2,00
			CouponOwnerType:    "admin",
			ExpectedSplit:      7580, // (7000 - 420) + 1000
			ExpectedCommission: 220,  // Platform commission reduced by coupon (420 - 200)
			ExpectedTotalValue: 7800, // Customer pays: 7000 + 1000 - 200
		},
		{
			Name:               "Cupom company com comissão alta",
			ValorPedido:        9000, // R$ 90,00
			Frete:              300,  // R$ 3,00
			CommissionRate:     0.12, // 12%
			CouponDiscount:     1500, // R$ 15,00
			CouponOwnerType:    "company",
			ExpectedSplit:      6720, // ((9000 - 1500) + 300) - 1080
			ExpectedCommission: 1080, // 12% de 9000
			ExpectedTotalValue: 7800, // (9000 - 1500) + 300
		},
		{
			Name:               "Valor médio com cupom admin válido",
			ValorPedido:        5000, // R$ 50,00
			Frete:              200,  // R$ 2,00
			CommissionRate:     0.08, // 8% = 400
			CouponDiscount:     250,  // R$ 2,50 (within max absorption: 400-100=300)
			CouponOwnerType:    "admin",
			ExpectedSplit:      4800, // (5000 - 400) + 200
			ExpectedCommission: 150,  // Platform commission reduced (400 - 250 = 150)
			ExpectedTotalValue: 4950, // Customer pays: 5000 + 200 - 250
		},
		{
			Name:               "Cupom company maior que valor do pedido (edge case)",
			ValorPedido:        2000, // R$ 20,00
			Frete:              200,  // R$ 2,00
			CommissionRate:     0.05, // 5%
			CouponDiscount:     2500, // R$ 25,00
			CouponOwnerType:    "company",
			ExpectedSplit:      100, // ((max(0, 2000 - 2500)) + 200) - 100
			ExpectedCommission: 100, // 5% de 2000
			ExpectedTotalValue: 200, // max(0, 2000 - 2500) + 200
		},
	}

	for _, scenario := range testScenarios {
		t.Run(scenario.Name, func(t *testing.T) {
			actualSplit, actualCommission, actualTotalValue := calculateSplit(
				scenario.ValorPedido,
				scenario.Frete,
				scenario.CommissionRate,
				scenario.CouponDiscount,
				scenario.CouponOwnerType,
			)

			if actualSplit != scenario.ExpectedSplit {
				t.Errorf("Split incorreto. Esperado: %d, Obtido: %d", scenario.ExpectedSplit, actualSplit)
			}

			if actualCommission != scenario.ExpectedCommission {
				t.Errorf("Comissão incorreta. Esperado: %d, Obtido: %d", scenario.ExpectedCommission, actualCommission)
			}

			if actualTotalValue != scenario.ExpectedTotalValue {
				t.Errorf("Valor total incorreto. Esperado: %d, Obtido: %d", scenario.ExpectedTotalValue, actualTotalValue)
			}

			t.Logf("✅ %s: Split=%d, Comissão=%d, Total=%d",
				scenario.Name, actualSplit, actualCommission, actualTotalValue)
		})
	}
}

// BenchmarkSplitCalculations benchmarks the split calculation performance
func BenchmarkSplitCalculations(b *testing.B) {
	scenarios := []struct {
		name            string
		valorPedido     int32
		frete           int32
		commissionRate  float64
		couponDiscount  int32
		couponOwnerType string
	}{
		{"NoCoupon", 10000, 500, 0.05, 0, ""},
		{"AdminCoupon", 10000, 500, 0.05, 200, "admin"},
		{"CompanyCoupon", 10000, 500, 0.05, 300, "company"},
	}

	for _, scenario := range scenarios {
		b.Run(scenario.name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				calculateSplit(
					scenario.valorPedido,
					scenario.frete,
					scenario.commissionRate,
					scenario.couponDiscount,
					scenario.couponOwnerType,
				)
			}
		})
	}
}

// TestSplitCalculationsTableDriven demonstrates table-driven testing approach
func TestAdminCouponSplitValidation(t *testing.T) {
	// Test the scenario from the logs where admin coupon causes validation error
	testCases := []struct {
		name                 string
		valorPedido          int32
		frete                int32
		commissionRate       float64
		couponDiscount       int32
		couponOwnerType      string
		shouldPassValidation bool
		description          string
	}{
		{
			name:                 "Admin coupon scenario from logs",
			valorPedido:          13020, // R$ 130.20
			frete:                0,     // No shipping in this case
			commissionRate:       0.12,  // 12%
			couponDiscount:       1302,  // R$ 13.02 (10% discount)
			couponOwnerType:      "admin",
			shouldPassValidation: true, // Should pass with corrected logic
			description:          "Original failing scenario should now pass",
		},
		{
			name:                 "Admin coupon with very high discount",
			valorPedido:          10000, // R$ 100.00
			frete:                500,   // R$ 5.00
			commissionRate:       0.05,  // 5%
			couponDiscount:       9800,  // R$ 98.00 (98% discount)
			couponOwnerType:      "admin",
			shouldPassValidation: false, // Should fail - commission too low
			description:          "Very high admin coupon should fail validation",
		},
		{
			name:                 "Company coupon equivalent scenario",
			valorPedido:          13020, // R$ 130.20
			frete:                0,     // No shipping
			commissionRate:       0.12,  // 12%
			couponDiscount:       1302,  // R$ 13.02
			couponOwnerType:      "company",
			shouldPassValidation: true, // Should pass
			description:          "Company coupon with same values should pass",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			splitAmount, effectiveCommission, totalValue := calculateSplit(
				tc.valorPedido, tc.frete, tc.commissionRate, tc.couponDiscount, tc.couponOwnerType)

			// Calculate final payment amount (what customer pays)
			finalPaymentAmount := totalValue - tc.couponDiscount

			// Test validation logic
			var validationPassed bool
			minCommissionRequired := int32(100) // R$ 1.00

			// Check if the transaction was rejected (splitAmount = -1)
			if splitAmount == -1 {
				validationPassed = false // Rejected transactions fail validation
			} else if tc.couponOwnerType == "admin" {
				// For admin coupons: validate against original amount
				originalAmount := tc.valorPedido + tc.frete
				validationPassed = splitAmount < originalAmount && effectiveCommission >= minCommissionRequired
			} else {
				// For company coupons: validate against final payment amount
				validationPassed = splitAmount < finalPaymentAmount && effectiveCommission >= minCommissionRequired
			}

			if validationPassed != tc.shouldPassValidation {
				t.Errorf("%s: Expected validation to pass=%v, but got=%v",
					tc.description, tc.shouldPassValidation, validationPassed)
				t.Errorf("  Split: %d, FinalPayment: %d, EffectiveCommission: %d",
					splitAmount, finalPaymentAmount, effectiveCommission)
			}

			t.Logf("✅ %s: Split=%d, FinalPayment=%d, Commission=%d, ValidationPassed=%v",
				tc.name, splitAmount, finalPaymentAmount, effectiveCommission, validationPassed)
		})
	}
}

func TestAdminCouponErrorScenarios(t *testing.T) {
	// Test scenarios that would trigger the split validation error
	scenarios := []struct {
		name            string
		orderValue      int32
		shippingFee     int32
		commissionRate  float64
		couponDiscount  int32
		description     string
		wouldFailBefore bool // Would fail with old validation logic
		shouldPassNow   bool // Should pass with new validation logic
	}{
		{
			name:            "Original failing scenario",
			orderValue:      13020, // R$ 130.20
			shippingFee:     0,
			commissionRate:  0.12, // 12%
			couponDiscount:  1302, // R$ 13.02 (10% discount)
			description:     "Exact scenario from logs",
			wouldFailBefore: true,
			shouldPassNow:   true,
		},
		{
			name:            "High commission rate with moderate discount",
			orderValue:      10000, // R$ 100.00
			shippingFee:     500,   // R$ 5.00
			commissionRate:  0.15,  // 15%
			couponDiscount:  1000,  // R$ 10.00 (10% discount) - reduced to keep commission above minimum
			description:     "High commission makes split > payment",
			wouldFailBefore: true,
			shouldPassNow:   true,
		},
		{
			name:            "Low commission with small discount",
			orderValue:      10000, // R$ 100.00 - increased to ensure commission > minimum
			shippingFee:     300,   // R$ 3.00
			commissionRate:  0.05,  // 5%
			couponDiscount:  200,   // R$ 2.00 (2% discount) - reduced to keep commission above minimum
			description:     "Low commission, should work fine",
			wouldFailBefore: false,
			shouldPassNow:   true,
		},
		{
			name:            "Extreme discount that should fail",
			orderValue:      10000, // R$ 100.00
			shippingFee:     0,
			commissionRate:  0.05, // 5%
			couponDiscount:  9950, // R$ 99.50 (99.5% discount)
			description:     "Extreme discount leaves no commission",
			wouldFailBefore: true,
			shouldPassNow:   false, // Should still fail due to minimum commission
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			// Calculate split using admin coupon logic
			splitAmount, effectiveCommission, totalValue := calculateSplit(
				scenario.orderValue, scenario.shippingFee, scenario.commissionRate,
				scenario.couponDiscount, "admin")

			originalAmount := scenario.orderValue + scenario.shippingFee
			finalPaymentAmount := totalValue - scenario.couponDiscount

			// Test old validation logic (would fail)
			oldValidationPassed := splitAmount < finalPaymentAmount

			// Test new validation logic
			minCommissionRequired := int32(100) // R$ 1.00
			newValidationPassed := splitAmount < originalAmount && effectiveCommission >= minCommissionRequired

			// Verify our expectations
			if oldValidationPassed == scenario.wouldFailBefore {
				t.Errorf("Old validation expectation wrong: expected wouldFailBefore=%v, got passed=%v",
					scenario.wouldFailBefore, oldValidationPassed)
			}

			if newValidationPassed != scenario.shouldPassNow {
				t.Errorf("New validation expectation wrong: expected shouldPassNow=%v, got passed=%v",
					scenario.shouldPassNow, newValidationPassed)
			}

			t.Logf("📊 %s:", scenario.name)
			t.Logf("   Order: R$ %.2f, Shipping: R$ %.2f, Commission: %.1f%%",
				float64(scenario.orderValue)/100, float64(scenario.shippingFee)/100, scenario.commissionRate*100)
			t.Logf("   Coupon: R$ %.2f (%.1f%% discount)",
				float64(scenario.couponDiscount)/100, float64(scenario.couponDiscount)/float64(scenario.orderValue)*100)
			t.Logf("   Customer pays: R$ %.2f (was R$ %.2f)",
				float64(finalPaymentAmount)/100, float64(totalValue)/100)
			t.Logf("   Partner receives: R$ %.2f", float64(splitAmount)/100)
			t.Logf("   Platform commission: R$ %.2f", float64(effectiveCommission)/100)
			t.Logf("   Old validation: %v, New validation: %v", oldValidationPassed, newValidationPassed)
		})
	}
}

// TestAdminCouponChargeAmountFix tests the specific fix for admin coupon charge amounts
func TestAdminCouponChargeAmountFix(t *testing.T) {
	// Test the exact scenario from the error logs
	orderValue := int32(12720)    // R$ 127.20 (without shipping)
	shippingFee := int32(300)     // R$ 3.00
	commissionRate := 0.12        // 12%
	couponDiscount := int32(1272) // R$ 12.72 (10% discount on order value only)

	// Calculate split amount using admin coupon logic
	splitAmount, effectiveCommission, _ := calculateSplit(
		orderValue, shippingFee, commissionRate, couponDiscount, "admin")

	originalAmount := orderValue + shippingFee            // 13020
	finalPaymentAmount := originalAmount - couponDiscount // 11748

	// For admin coupons, charge amount is the final payment amount (what customer pays)
	chargeAmountForAdmin := finalPaymentAmount // 11718

	t.Logf("Order Value: %d", orderValue)
	t.Logf("Shipping Fee: %d", shippingFee)
	t.Logf("Original Amount: %d", originalAmount)
	t.Logf("Coupon Discount: %d", couponDiscount)
	t.Logf("Final Payment Amount: %d", finalPaymentAmount)
	t.Logf("Split Amount: %d", splitAmount)
	t.Logf("Charge Amount (Admin): %d", chargeAmountForAdmin)
	t.Logf("Effective Commission: %d", effectiveCommission)

	// The key fix: split amount should be less than charge amount for admin coupons
	if splitAmount >= chargeAmountForAdmin {
		t.Errorf("Split amount (%d) should be less than charge amount (%d) for admin coupons",
			splitAmount, chargeAmountForAdmin)
	}

	// Verify the split amount matches expected calculation
	expectedCommission := int32(math.Round(float64(orderValue) * commissionRate)) // 1526
	// Since coupon (1272) < commission (1526), normal case
	expectedSplitAmount := (orderValue - expectedCommission) + shippingFee // 11494

	if splitAmount != expectedSplitAmount {
		t.Errorf("Expected split amount %d, got %d", expectedSplitAmount, splitAmount)
	}

	// Verify that this would work with Woovi (split < charge)
	if splitAmount >= chargeAmountForAdmin {
		t.Errorf("This would still fail with Woovi: split (%d) >= charge (%d)",
			splitAmount, chargeAmountForAdmin)
	} else {
		t.Logf("✓ This would work with Woovi: split (%d) < charge (%d)",
			splitAmount, chargeAmountForAdmin)
	}
}

func TestSplitCalculationsTableDriven(t *testing.T) {
	tests := []struct {
		name            string
		valorPedido     int32
		frete           int32
		commissionRate  float64
		couponDiscount  int32
		couponOwnerType string
		wantSplit       int32
		wantCommission  int32
		wantTotal       int32
	}{
		{
			name:            "basic_no_coupon",
			valorPedido:     10000,
			frete:           500,
			commissionRate:  0.05,
			couponDiscount:  0,
			couponOwnerType: "",
			wantSplit:       10000, // (10000 - 500) + 500
			wantCommission:  500,   // 5% de 10000
			wantTotal:       10500, // 10000 + 500
		},
		{
			name:            "admin_coupon_reduces_commission",
			valorPedido:     10000,
			frete:           500,
			commissionRate:  0.05,
			couponDiscount:  200,
			couponOwnerType: "admin",
			wantSplit:       10000, // (10000 - 500) + 500
			wantCommission:  300,   // Platform commission reduced by coupon (500 - 200)
			wantTotal:       10300, // Customer pays: 10000 + 500 - 200
		},
		{
			name:            "company_coupon_reduces_order_value",
			valorPedido:     10000,
			frete:           500,
			commissionRate:  0.05,
			couponDiscount:  1000,
			couponOwnerType: "company",
			wantSplit:       9000, // ((10000 - 1000) + 500) - 500
			wantCommission:  500,  // 5% de 10000
			wantTotal:       9500, // (10000 - 1000) + 500
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSplit, gotCommission, gotTotal := calculateSplit(
				tt.valorPedido,
				tt.frete,
				tt.commissionRate,
				tt.couponDiscount,
				tt.couponOwnerType,
			)

			if gotSplit != tt.wantSplit {
				t.Errorf("calculateSplit() split = %v, want %v", gotSplit, tt.wantSplit)
			}
			if gotCommission != tt.wantCommission {
				t.Errorf("calculateSplit() commission = %v, want %v", gotCommission, tt.wantCommission)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("calculateSplit() total = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

// TestAdminCouponRejectionScenarios tests scenarios where admin coupons should be rejected
func TestAdminCouponRejectionScenarios(t *testing.T) {
	scenarios := []struct {
		name           string
		orderValue     int32
		shippingFee    int32
		commissionRate float64
		couponDiscount int32
		description    string
	}{
		{
			name:           "Coupon equals commission",
			orderValue:     10000, // R$ 100.00
			shippingFee:    400,   // R$ 4.00
			commissionRate: 0.05,  // 5% = 500
			couponDiscount: 500,   // R$ 5.00 (exceeds max absorption: 500-100=400)
			description:    "Coupon discount equals commission, exceeds platform absorption capacity",
		},
		{
			name:           "Coupon exceeds commission",
			orderValue:     10000, // R$ 100.00
			shippingFee:    400,   // R$ 4.00
			commissionRate: 0.03,  // 3% = 300
			couponDiscount: 500,   // R$ 5.00 (exceeds max absorption: 300-100=200)
			description:    "Coupon discount exceeds commission, should be rejected",
		},
		{
			name:           "Low commission scenario",
			orderValue:     1500, // R$ 15.00
			shippingFee:    100,  // R$ 1.00
			commissionRate: 0.05, // 5% = 75 (less than min required 100)
			couponDiscount: 50,   // R$ 0.50 (exceeds max absorption: 0)
			description:    "Commission below minimum, any coupon should be rejected",
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			commission := int32(math.Round(float64(scenario.orderValue) * scenario.commissionRate))
			minCommissionRequired := int32(100) // R$ 1.00

			maxCouponAbsorption := commission - minCommissionRequired
			if maxCouponAbsorption < 0 {
				maxCouponAbsorption = 0
			}

			t.Logf("Scenario: %s", scenario.description)
			t.Logf("Order Value: %d, Commission: %d, Min Required: %d", scenario.orderValue, commission, minCommissionRequired)
			t.Logf("Max Coupon Absorption: %d, Coupon Discount: %d", maxCouponAbsorption, scenario.couponDiscount)

			shouldBeRejected := scenario.couponDiscount > maxCouponAbsorption
			if !shouldBeRejected {
				t.Errorf("Expected scenario to be rejected, but coupon (%d) <= max absorption (%d)",
					scenario.couponDiscount, maxCouponAbsorption)
			} else {
				t.Logf("✓ Scenario correctly identified for rejection: coupon (%d) > max absorption (%d)",
					scenario.couponDiscount, maxCouponAbsorption)
			}
		})
	}
}
