package handlers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/stretchr/testify/assert"
)

// MockSearchQueries is a simple mock for testing SearchUser
type MockSearchQueries struct {
	users []postgres.SearchUsersRow
	err   error
}

func (m *MockSearchQueries) SearchUsers(ctx context.Context, arg postgres.SearchUsersParams) ([]postgres.SearchUsersRow, error) {
	return m.users, m.err
}

// testUserHandler is a test version of UserHandler that uses our mock
type testUserHandler struct {
	mockQueries *MockSearchQueries
}

// SearchUser is a simplified version for testing
func (h *testUserHandler) SearchUser(w http.ResponseWriter, r *http.Request) {
	// Get query parameter from URL path
	query := chi.URLParam(r, "query")
	if query == "" {
		http.Error(w, `{"code":"001","message":"search query is required"}`, http.StatusBadRequest)
		return
	}

	// Validate query length
	if len(query) < 2 {
		http.Error(w, `{"code":"001","message":"search query must be at least 2 characters long"}`, http.StatusBadRequest)
		return
	}

	// Search users in database
	users, err := h.mockQueries.SearchUsers(r.Context(), postgres.SearchUsersParams{
		SearchQuery: query,
		Limit:       10,
		Offset:      0,
	})
	if err != nil {
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// Check if any users were found
	if len(users) == 0 {
		http.Error(w, `{"code":"001","message":"no users found matching the search criteria"}`, http.StatusNotFound)
		return
	}

	// Convert to response format
	userResponses := make([]SearchUsersResponse, len(users))
	totalItems := 0
	for i, user := range users {
		totalItems = int(user.TotalCount)

		// Handle nullable subscription_id
		var subscriptionID *int32
		if user.SubscriptionID.Valid {
			subscriptionID = &user.SubscriptionID.Int32
		}

		userResponses[i] = SearchUsersResponse{
			ExternalID:     user.ExternalID,
			Name:           user.Name,
			Email:          user.Email,
			Cpf:            user.Cpf,
			PhoneNumbers:   user.PhoneNumbers,
			CashbackValue:  user.CashbackValue,
			SubscriptionID: subscriptionID,
			IsActive:       user.IsActive,
			IsDeleted:      user.IsDeleted,
			CreatedAt:      user.CreatedAt.Format(time.RFC3339),
			UpdatedAt:      user.UpdatedAt.Format(time.RFC3339),
		}
	}

	response := struct {
		PageNumber int                   `json:"pageNumber"`
		Limit      int                   `json:"limit"`
		TotalItems int                   `json:"totalItems"`
		TotalPages int                   `json:"totalPages"`
		Data       []SearchUsersResponse `json:"data"`
	}{
		PageNumber: 1,
		Limit:      10,
		TotalItems: totalItems,
		TotalPages: (totalItems + 9) / 10, // Ceiling division
		Data:       userResponses,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func TestSearchUser(t *testing.T) {
	tests := []struct {
		name           string
		query          string
		mockUsers      []postgres.SearchUsersRow
		mockError      error
		expectedStatus int
		expectedCount  int
	}{
		{
			name:  "Successful search with results",
			query: "john",
			mockUsers: []postgres.SearchUsersRow{
				{
					ExternalID:     "user-123",
					Name:           "John Doe",
					Email:          "<EMAIL>",
					Cpf:            "12345678901",
					PhoneNumbers:   []string{"11999999999"},
					CashbackValue:  1000,
					SubscriptionID: sql.NullInt32{Int32: 1, Valid: true},
					IsActive:       true,
					IsDeleted:      false,
					CreatedAt:      time.Now(),
					UpdatedAt:      time.Now(),
					TotalCount:     1,
				},
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "No users found",
			query:          "nonexistent",
			mockUsers:      []postgres.SearchUsersRow{},
			mockError:      nil,
			expectedStatus: http.StatusNotFound,
			expectedCount:  0,
		},
		{
			name:           "Query too short",
			query:          "a",
			mockUsers:      nil,
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedCount:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockSearchQueries{
				users: tt.mockUsers,
				err:   tt.mockError,
			}

			// Create test handler
			testHandler := &testUserHandler{
				mockQueries: mockQueries,
			}

			// Create router and add route
			router := chi.NewRouter()
			router.Get("/search/{query}", testHandler.SearchUser)

			// Create request
			req := httptest.NewRequest("GET", "/search/"+tt.query, nil)
			w := httptest.NewRecorder()

			// Call handler
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response struct {
					PageNumber int                   `json:"pageNumber"`
					Limit      int                   `json:"limit"`
					TotalItems int                   `json:"totalItems"`
					TotalPages int                   `json:"totalPages"`
					Data       []SearchUsersResponse `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check response structure
				assert.Equal(t, tt.expectedCount, len(response.Data))
				assert.Equal(t, tt.expectedCount, response.TotalItems)

				// Check first user if exists
				if len(response.Data) > 0 {
					user := response.Data[0]
					assert.Equal(t, "user-123", user.ExternalID)
					assert.Equal(t, "John Doe", user.Name)
					assert.Equal(t, "<EMAIL>", user.Email)
					assert.True(t, user.IsActive)
					assert.False(t, user.IsDeleted)
				}
			}
		})
	}
}

func TestSearchUser_EmptyQuery(t *testing.T) {
	// Create test handler
	testHandler := &testUserHandler{
		mockQueries: &MockSearchQueries{},
	}

	// Create router and add route
	router := chi.NewRouter()
	router.Get("/search/{query}", testHandler.SearchUser)

	// Create request with empty query (this should not happen in real routing, but test edge case)
	req := httptest.NewRequest("GET", "/search/", nil)
	w := httptest.NewRecorder()

	// Call handler
	router.ServeHTTP(w, req)

	// Should return 404 because the route won't match
	assert.Equal(t, http.StatusNotFound, w.Code)
}

// MockUpdateUserStatusQueries is a mock for testing UpdateUserStatus
type MockUpdateUserStatusQueries struct {
	getUserResult postgres.GetUserByExternalIDRow
	updateResult  postgres.UpdateUserStatusRow
	getUserError  error
	updateError   error
}

func (m *MockUpdateUserStatusQueries) GetUserByExternalID(ctx context.Context, externalID string) (postgres.GetUserByExternalIDRow, error) {
	return m.getUserResult, m.getUserError
}

func (m *MockUpdateUserStatusQueries) UpdateUserStatus(ctx context.Context, arg postgres.UpdateUserStatusParams) (postgres.UpdateUserStatusRow, error) {
	return m.updateResult, m.updateError
}

// testUpdateUserStatusHandler is a test version of UserHandler for UpdateUserStatus
type testUpdateUserStatusHandler struct {
	mockQueries *MockUpdateUserStatusQueries
}

// UpdateUserStatus is a simplified version for testing
func (h *testUpdateUserStatusHandler) UpdateUserStatus(w http.ResponseWriter, r *http.Request) {
	externalID := chi.URLParam(r, "external_id")
	if externalID == "" {
		http.Error(w, `{"code":"001","message":"external_id is required"}`, http.StatusBadRequest)
		return
	}

	// Validate external_id format (ULID should be 26 characters)
	if len(externalID) != 26 {
		http.Error(w, `{"code":"001","message":"invalid external_id format"}`, http.StatusBadRequest)
		return
	}

	// Parse request body
	var payload UpdateUserStatusRequest
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		http.Error(w, `{"code":"001","message":"invalid request body"}`, http.StatusBadRequest)
		return
	}

	// Validate payload
	if payload.IsActive == nil {
		http.Error(w, `{"code":"001","message":"is_active field is required"}`, http.StatusBadRequest)
		return
	}

	// Verify user exists
	_, err := h.mockQueries.GetUserByExternalID(r.Context(), externalID)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, `{"code":"001","message":"user not found"}`, http.StatusNotFound)
			return
		}
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// Update user status
	updatedUser, err := h.mockQueries.UpdateUserStatus(r.Context(), postgres.UpdateUserStatusParams{
		ExternalID: externalID,
		IsActive:   *payload.IsActive,
	})
	if err != nil {
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	response := struct {
		Data UpdateUserStatusResponse `json:"data"`
	}{
		Data: UpdateUserStatusResponse{
			ExternalID: updatedUser.ExternalID,
			Name:       updatedUser.Name,
			IsActive:   updatedUser.IsActive,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func TestUpdateUserStatus(t *testing.T) {
	tests := []struct {
		name           string
		externalID     string
		requestBody    UpdateUserStatusRequest
		mockGetUser    postgres.GetUserByExternalIDRow
		mockUpdate     postgres.UpdateUserStatusRow
		getUserError   error
		updateError    error
		expectedStatus int
		expectedActive bool
	}{
		{
			name:       "Successfully activate user",
			externalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: UpdateUserStatusRequest{
				IsActive: &[]bool{true}[0],
			},
			mockGetUser: postgres.GetUserByExternalIDRow{
				ID:       1,
				Name:     "John Doe",
				IsActive: false,
			},
			mockUpdate: postgres.UpdateUserStatusRow{
				ID:         1,
				Name:       "John Doe",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				IsActive:   true,
			},
			getUserError:   nil,
			updateError:    nil,
			expectedStatus: http.StatusOK,
			expectedActive: true,
		},
		{
			name:       "Successfully deactivate user",
			externalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: UpdateUserStatusRequest{
				IsActive: &[]bool{false}[0],
			},
			mockGetUser: postgres.GetUserByExternalIDRow{
				ID:       1,
				Name:     "John Doe",
				IsActive: true,
			},
			mockUpdate: postgres.UpdateUserStatusRow{
				ID:         1,
				Name:       "John Doe",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				IsActive:   false,
			},
			getUserError:   nil,
			updateError:    nil,
			expectedStatus: http.StatusOK,
			expectedActive: false,
		},
		{
			name:           "User not found",
			externalID:     "01JR0X8RNYFJECMV1NBNVK9999",
			requestBody:    UpdateUserStatusRequest{IsActive: &[]bool{true}[0]},
			mockGetUser:    postgres.GetUserByExternalIDRow{},
			mockUpdate:     postgres.UpdateUserStatusRow{},
			getUserError:   sql.ErrNoRows,
			updateError:    nil,
			expectedStatus: http.StatusNotFound,
			expectedActive: false,
		},
		{
			name:           "Invalid external_id format - too short",
			externalID:     "short",
			requestBody:    UpdateUserStatusRequest{IsActive: &[]bool{true}[0]},
			mockGetUser:    postgres.GetUserByExternalIDRow{},
			mockUpdate:     postgres.UpdateUserStatusRow{},
			getUserError:   nil,
			updateError:    nil,
			expectedStatus: http.StatusBadRequest,
			expectedActive: false,
		},
		{
			name:           "Invalid external_id format - too long",
			externalID:     "01JR0X8RNYFJECMV1NBNVK9921EXTRA",
			requestBody:    UpdateUserStatusRequest{IsActive: &[]bool{true}[0]},
			mockGetUser:    postgres.GetUserByExternalIDRow{},
			mockUpdate:     postgres.UpdateUserStatusRow{},
			getUserError:   nil,
			updateError:    nil,
			expectedStatus: http.StatusBadRequest,
			expectedActive: false,
		},
		{
			name:           "Missing is_active field (nil pointer)",
			externalID:     "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody:    UpdateUserStatusRequest{IsActive: nil},
			mockGetUser:    postgres.GetUserByExternalIDRow{},
			mockUpdate:     postgres.UpdateUserStatusRow{},
			getUserError:   nil,
			updateError:    nil,
			expectedStatus: http.StatusBadRequest,
			expectedActive: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockUpdateUserStatusQueries{
				getUserResult: tt.mockGetUser,
				updateResult:  tt.mockUpdate,
				getUserError:  tt.getUserError,
				updateError:   tt.updateError,
			}

			// Create test handler
			testHandler := &testUpdateUserStatusHandler{
				mockQueries: mockQueries,
			}

			// Create router and add route
			router := chi.NewRouter()
			router.Patch("/{external_id}/status", testHandler.UpdateUserStatus)

			// Create request body
			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("PATCH", "/"+tt.externalID+"/status", bytes.NewBuffer(requestBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Call handler
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response struct {
					Data UpdateUserStatusResponse `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check response structure
				assert.Equal(t, tt.mockUpdate.Name, response.Data.Name)
				assert.Equal(t, tt.mockUpdate.ExternalID, response.Data.ExternalID)
				assert.Equal(t, tt.expectedActive, response.Data.IsActive)
			}
		})
	}
}
