package common

import (
	"encoding/json"
	"math"
	"net/http"
)

type CreditCard struct {
	Number          string   `json:"number" example:"****************" validate:"required,credit_card"`
	HolderName      string   `json:"holder_name" example:"<PERSON>" validate:"required"`
	HolderDocument  string   `json:"holder_document" example:"99521488018" validate:"required,min=11,max=14"`
	ExpirationMonth string   `json:"expiration_month" example:"02" validate:"required,numeric,len=2"`
	ExpirationYear  string   `json:"expiration_year" example:"2024" validate:"required,numeric,min=2,max=4"`
	CVV             string   `json:"cvv" example:"446" validate:"required,min=3,max=4"`
	Address         *Address `json:"address" validate:"required"`
	Installments    int      `json:"installments" example:"1" validate:"required,min=1"`
}

type Address struct {
	Street       string `json:"street" example:"Rua DR ZUQUIM" validate:"required"`
	Number       string `json:"number" example:"762" validate:"required"`
	Complement   string `json:"complement" example:"SALA 01"`
	Neighborhood string `json:"neighborhood" example:"Santana" validate:"required"`
	City         string `json:"city" example:"São Paulo" validate:"required"`
	State        string `json:"state" example:"SP" validate:"required,len=2"`
	ZipCode      string `json:"zip_code" example:"02035021" validate:"required,numeric,len=8"`
}

type ErrorResponse struct {
	Code    string      `json:"code" example:"001"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

type SuccessResponse[T interface{}] struct {
	Data T `json:"data"`
}

type SuccessResponseWithPagination[T interface{}] struct {
	PageNumber int `json:"pageNumber" example:"1"`
	Limit      int `json:"limit" example:"10"`
	TotalItems int `json:"totalItems" example:"100"`
	TotalPages int `json:"totalPages" example:"10"`
	Data       T   `json:"data"`
}

func RespondError(w http.ResponseWriter, err error, status ...int) {
	w.Header().Set("Content-Type", "application/json")
	if status != nil {
		w.WriteHeader(status[0])
	} else {
		w.WriteHeader(http.StatusInternalServerError)
	}
	json.NewEncoder(w).Encode(&ErrorResponse{
		Code:    "001",
		Message: err.Error(),
		Details: err,
	})
}

func RespondSuccess[T interface{}](w http.ResponseWriter, data T, status int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(&SuccessResponse[T]{
		Data: data,
	})
}

func RespondSuccessWithPagination[T interface{}](w http.ResponseWriter, data T, pageNumber, limit, totalItems int) {
	totalPages := int(math.Ceil(float64(totalItems) / float64(limit)))

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(&SuccessResponseWithPagination[T]{
		PageNumber: pageNumber,
		Limit:      limit,
		TotalItems: totalItems,
		TotalPages: totalPages,
		Data:       data,
	})
}
