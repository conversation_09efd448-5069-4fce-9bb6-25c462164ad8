package server

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/newrelic/go-agent/v3/newrelic"
	httpSwagger "github.com/swaggo/http-swagger"

	"github.com/izy-mercado/backend/docs"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/fcm"
	"github.com/izy-mercado/backend/internal/http/handlers/v1"
	"github.com/izy-mercado/backend/internal/integrations/storage"
	"github.com/izy-mercado/backend/internal/logger"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
)

type Server struct {
	queries    *postgres.Queries
	storage    *storage.Storage
	pool       *pgxpool.Pool
	env        *config.Environment
	logger     logger.Logger
	fcmService *fcm.Service
}

var nrapp *newrelic.Application

func New(env *config.Environment, pool *pgxpool.Pool, storage *storage.Storage) *Server {
	nrapp = startNewRelic(env.NewRelic, os.Getenv("APP_ENV"))
	queries := postgres.New(pool)

	// Initialize FCM service
	fcmConfig := fcm.Config{
		ServiceAccountPath: env.FCM.ServiceAccountPath,
		ServiceAccountJSON: env.FCM.ServiceAccountJSON,
		ProjectID:          env.FCM.ProjectID,
	}
	fcmService, err := fcm.NewService(fcmConfig, queries)
	if err != nil {
		log.Printf("Warning: Failed to initialize FCM service: %v", err)
		// Continue without FCM service for now
		fcmService = nil
	}

	return &Server{
		storage:    storage,
		queries:    queries,
		pool:       pool,
		env:        env,
		logger:     logger.NewZerologLogger(os.Stdout),
		fcmService: fcmService,
	}
}

// @title Izy Mercado API
// @version 1.0
// @description.markdown
// @securityDefinitions.apikey Bearer
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

// @securityDefinitions.apiKey ApiKeyAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

// @contact.name Developer Support
// @contact.url https://www.izymercado.com.br
// @contact.email <EMAIL>

// @host {{.Host}}
func (s *Server) Start() {

	// set conditional allowed origins by env
	var allowedOrigins = []string{}
	if os.Getenv("APP_ENV") == "development" {
		allowedOrigins = []string{"*"}
	} else {
		allowedOrigins = []string{"https://api.izymercado.com.br", "https://*.izymercado.com.br"}
	}

	r := chi.NewRouter()

	r.Use(addNewRelicContext)
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(middleware.Logger)
	r.Use(middleware.Timeout(60 * time.Second))
	r.Use(cors.Handler(cors.Options{
		AllowedOrigins:   allowedOrigins,
		AllowedMethods:   []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-Application-Type"},
		ExposedHeaders:   []string{"x-warning"},
		AllowCredentials: false,
		MaxAge:           300,
	}))

	docs.SwaggerInfo.Host = s.env.Core.SWAGGER_HOST

	basicAuthHandler := middleware.BasicAuth("izymercado", map[string]string{
		"izymercado": s.env.Core.BASIC_AUTH_PASSWORD,
	})

	// Api versioning
	v1Routes := chi.NewRouter()
	v1Routes.With(basicAuthHandler).Get("/docs/*", httpSwagger.WrapHandler)
	v1Routes.Mount("/auth", handlers.NewAuthHandler(s.env, s.queries, s.pool))
	v1Routes.Mount("/user", handlers.NewUserHandler(s.env, s.queries, s.pool, s.fcmService))
	v1Routes.Mount("/company", handlers.NewCompanyHandler(s.env, s.queries, s.storage, s.pool, s.fcmService, s.logger))
	v1Routes.Mount("/payment", handlers.NewPaymentHandler(s.env, s.queries, s.pool, s.logger, s.fcmService))
	v1Routes.Mount("/product", handlers.NewProductHandler(s.env, s.queries, s.storage, s.pool))
	v1Routes.Mount("/category", handlers.NewCategoryHandler(s.env, s.queries, s.storage))
	v1Routes.Mount("/price-comparison", handlers.NewPriceComparisonHandler(s.env, s.queries))
	v1Routes.Mount("/coupon", handlers.NewCouponHandler(s.env, s.queries, s.pool, s.logger))
	v1Routes.Mount("/search", handlers.NewSearchHandler(s.env, s.queries))
	v1Routes.Mount("/fcm", handlers.NewFCMHandler(s.env, s.queries))

	r.Mount("/v1", v1Routes)
	r.Mount("/health", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))

	fmt.Println("Server running at port 8080")
	http.ListenAndServe(":8080", r)
}

func addNewRelicContext(next http.Handler) http.Handler {
	fn := func(w http.ResponseWriter, r *http.Request) {
		// Only start New Relic transaction if nrapp is initialized
		if nrapp != nil {
			// start newrelic transaction
			txn := nrapp.StartTransaction(r.Method + r.URL.RequestURI())
			defer txn.End()

			txn.SetWebRequestHTTP(r)
			ctx := newrelic.NewContext(r.Context(), txn)

			next.ServeHTTP(w, r.WithContext(ctx))
		} else {
			// Skip New Relic monitoring when not in production
			next.ServeHTTP(w, r)
		}
	}
	return http.HandlerFunc(fn)
}

func startNewRelic(env config.NewRelic, appEnv string) *newrelic.Application {
	if appEnv != "production" {
		log.Println("Skipping New Relic setup because APP_ENV is not production")
		return nil
	}

	if env.LICENSE_KEY == "" {
		log.Println("Skipping New Relic setup because NEW_RELIC_LICENSE_KEY is not set")
		return nil
	}

	app, err := newrelic.NewApplication(
		newrelic.ConfigAppName(env.APP_NAME),
		newrelic.ConfigLicense(env.LICENSE_KEY),
		newrelic.ConfigDistributedTracerEnabled(true),
	)
	if err != nil {
		log.Printf("Error newrelic.NewApplication: %s\n", err)
		return nil
	}

	log.Printf("New Relic monitoring enabled for %s\n", env.APP_NAME)
	return app

}
