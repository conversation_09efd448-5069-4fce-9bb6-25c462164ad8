package middlewares

import (
	"context"
	"net/http"
	"time"

	"github.com/izy-mercado/backend/internal/logger"
	"github.com/izy-mercado/backend/internal/middlewares"
)

// Define typed context keys to avoid collisions
type contextKey string

const (
	// HTTP context keys
	HTTPMethodKey contextKey = "http_method"
	HTTPURLKey    contextKey = "http_url"
	UserAgentKey  contextKey = "user_agent"
	RemoteAddrKey contextKey = "remote_addr"
	RequestIDKey  contextKey = "request_id"
	TraceIDKey    contextKey = "trace_id"
)

// GCPLoggingMiddleware adds GCP Cloud Logging trace information to requests
func GCPLoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Extract trace ID from GCP headers
		traceID := extractTraceID(r)

		// Add trace ID to context if available
		ctx := r.Context()
		if traceID != "" {
			ctx = context.WithValue(ctx, <PERSON><PERSON><PERSON><PERSON>, trace<PERSON>)
		}

		// Create a response writer wrapper to capture status code
		wrapper := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}

		// Process request
		next.ServeHTTP(wrapper, r.WithContext(ctx))

		// Log the HTTP request in GCP format
		latency := time.Since(start)

		details := map[string]interface{}{
			"remote_addr": r.RemoteAddr,
			"request_id":  r.Header.Get("X-Request-ID"),
		}

		// Add trace ID to details if available
		if traceID != "" {
			details["trace_id"] = traceID
		}

		logger.LogHTTPRequest(
			ctx,
			r.Method,
			r.URL.String(),
			r.UserAgent(),
			wrapper.statusCode,
			latency,
			details,
		)
	})
}

// extractTraceID extracts trace ID from various GCP headers
func extractTraceID(r *http.Request) string {
	// Try GCP Cloud Trace header first
	if traceHeader := r.Header.Get("X-Cloud-Trace-Context"); traceHeader != "" {
		return logger.ExtractTraceFromRequest(traceHeader)
	}

	// Try other common trace headers
	if traceID := r.Header.Get("X-Trace-Id"); traceID != "" {
		return traceID
	}

	if traceID := r.Header.Get("Trace-Id"); traceID != "" {
		return traceID
	}

	return ""
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// GCPStructuredLoggingMiddleware adds structured logging fields for GCP
func GCPStructuredLoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Add request information to context for logging
		ctx = context.WithValue(ctx, HTTPMethodKey, r.Method)
		ctx = context.WithValue(ctx, HTTPURLKey, r.URL.String())
		ctx = context.WithValue(ctx, UserAgentKey, r.UserAgent())
		ctx = context.WithValue(ctx, RemoteAddrKey, r.RemoteAddr)

		// Add request ID if available
		if requestID := r.Header.Get("X-Request-ID"); requestID != "" {
			ctx = context.WithValue(ctx, RequestIDKey, requestID)
		}

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// LogBusinessEventMiddleware creates a middleware for logging specific business events
func LogBusinessEventMiddleware(eventType string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			// Log the business event
			details := map[string]interface{}{
				"endpoint": r.URL.Path,
				"method":   r.Method,
			}

			// Add user information if available from context
			if user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User); ok {
				details["user_external_id"] = user.ExternalID
				details["user_email"] = user.Email
			}

			logger.LogBusinessEvent(ctx, eventType, r.URL.Path, details)

			next.ServeHTTP(w, r)
		})
	}
}

// GCPErrorLoggingMiddleware logs errors in GCP-compatible format
func GCPErrorLoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if err := recover(); err != nil {
				ctx := r.Context()

				// Extract trace ID if available
				traceID := ""
				if tid, ok := ctx.Value(TraceIDKey).(string); ok {
					traceID = tid
				}

				details := map[string]interface{}{
					"panic":      err,
					"endpoint":   r.URL.Path,
					"method":     r.Method,
					"user_agent": r.UserAgent(),
				}

				if traceID != "" {
					details["trace_id"] = traceID
				}

				// Log critical error with trace information
				if traceID != "" {
					logger.LogWithTrace(ctx, traceID, logger.SeverityCritical, "Panic occurred in HTTP handler", details)
				} else {
					logger.LogError(ctx, nil, "Panic occurred in HTTP handler", details)
				}

				// Return 500 error
				http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			}
		}()

		next.ServeHTTP(w, r)
	})
}
