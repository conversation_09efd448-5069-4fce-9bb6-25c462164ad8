package fcm

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestService is a test version of the Service struct that allows us to test DetermineApplicationType
type TestService struct {
	mockUserRoles map[string][]string
}

// GetUserRoles is a mock implementation for testing
func (ts *TestService) GetUserRoles(ctx context.Context, userExternalID string) ([]string, error) {
	if roles, exists := ts.mockUserRoles[userExternalID]; exists {
		return roles, nil
	}
	return []string{}, nil
}

// DetermineApplicationType is copied from the main service for testing
func (ts *TestService) DetermineApplicationType(ctx context.Context, userExternalID, notificationType string) (string, error) {
	// Get user roles
	roles, err := ts.GetUserRoles(ctx, userExternalID)
	if err != nil {
		return "", err
	}

	// Check user roles
	hasPartnerRole := false
	hasUserRole := false
	for _, role := range roles {
		if role == "partner" {
			hasPartnerRole = true
		}
		if role == "user" {
			hasUserRole = true
		}
	}

	// Route notifications based on CONTEXT, not just role precedence
	switch notificationType {
	// PARTNER-CONTEXT notifications: Business management, new orders for business owners
	case "new_order_received", "partner_notification", "company_update":
		// These are business-context notifications that should ONLY go to partner dashboard
		if hasPartnerRole {
			return "partner-web", nil
		}
		// If user doesn't have partner role, they shouldn't receive these notifications
		return "", fmt.Errorf("partner role required for business notifications")

	// CUSTOMER-CONTEXT notifications: Order status updates for customers who placed orders
	case "invoice_status_update", "invoice_status", "order_update":
		// These are customer-context notifications about orders the user placed
		// They should go to mobile app regardless of whether user also has partner role
		if hasUserRole {
			return "mobile", nil
		}
		// If user doesn't have user role, they shouldn't receive customer notifications
		return "", fmt.Errorf("user role required for customer notifications")
	}

	// Default fallback for unknown notification types
	if hasUserRole {
		return "mobile", nil
	}
	if hasPartnerRole {
		return "partner-web", nil
	}

	return "", fmt.Errorf("user has no valid roles for notifications")
}

// TestDetermineApplicationType tests the application type determination logic
func TestDetermineApplicationType(t *testing.T) {
	tests := []struct {
		name             string
		userRoles        []string
		notificationType string
		expectedAppType  string
		expectError      bool
	}{
		// PARTNER-CONTEXT notifications: Business management, new orders for business owners
		{
			name:             "Partner role with new_order_received should go to partner-web",
			userRoles:        []string{"partner"},
			notificationType: "new_order_received",
			expectedAppType:  "partner-web",
			expectError:      false,
		},
		{
			name:             "Partner role with partner_notification should go to partner-web",
			userRoles:        []string{"partner"},
			notificationType: "partner_notification",
			expectedAppType:  "partner-web",
			expectError:      false,
		},
		{
			name:             "Partner role with company_update should go to partner-web",
			userRoles:        []string{"partner"},
			notificationType: "company_update",
			expectedAppType:  "partner-web",
			expectError:      false,
		},
		{
			name:             "User role with new_order_received should return error (no partner role)",
			userRoles:        []string{"user"},
			notificationType: "new_order_received",
			expectedAppType:  "",
			expectError:      true,
		},
		{
			name:             "User role with partner_notification should return error (no partner role)",
			userRoles:        []string{"user"},
			notificationType: "partner_notification",
			expectedAppType:  "",
			expectError:      true,
		},

		// CUSTOMER-CONTEXT notifications: Order status updates for customers who placed orders
		{
			name:             "User role with invoice_status_update should go to mobile",
			userRoles:        []string{"user"},
			notificationType: "invoice_status_update",
			expectedAppType:  "mobile",
			expectError:      false,
		},
		{
			name:             "User role with order_update should go to mobile",
			userRoles:        []string{"user"},
			notificationType: "order_update",
			expectedAppType:  "mobile",
			expectError:      false,
		},
		{
			name:             "Partner role with invoice_status_update should return error (no user role)",
			userRoles:        []string{"partner"},
			notificationType: "invoice_status_update",
			expectedAppType:  "",
			expectError:      true,
		},

		// CRITICAL TEST: Both roles - context-based routing (not role precedence)
		{
			name:             "Both roles with invoice_status_update should go to mobile (customer context)",
			userRoles:        []string{"user", "partner"},
			notificationType: "invoice_status_update",
			expectedAppType:  "mobile",
			expectError:      false,
		},
		{
			name:             "Both roles with new_order_received should go to partner-web (business context)",
			userRoles:        []string{"user", "partner"},
			notificationType: "new_order_received",
			expectedAppType:  "partner-web",
			expectError:      false,
		},
		{
			name:             "Both roles with partner_notification should go to partner-web (business context)",
			userRoles:        []string{"user", "partner"},
			notificationType: "partner_notification",
			expectedAppType:  "partner-web",
			expectError:      false,
		},
		{
			name:             "Both roles with order_update should go to mobile (customer context)",
			userRoles:        []string{"user", "partner"},
			notificationType: "order_update",
			expectedAppType:  "mobile",
			expectError:      false,
		},

		// Error cases
		{
			name:             "No roles should return error",
			userRoles:        []string{},
			notificationType: "invoice_status_update",
			expectedAppType:  "",
			expectError:      true,
		},
		{
			name:             "Unknown role should return error",
			userRoles:        []string{"unknown"},
			notificationType: "invoice_status_update",
			expectedAppType:  "",
			expectError:      true,
		},
		{
			name:             "Admin role with invoice_status_update should return error (no user role)",
			userRoles:        []string{"admin"},
			notificationType: "invoice_status_update",
			expectedAppType:  "",
			expectError:      true,
		},
		{
			name:             "Admin and user roles with invoice_status_update should go to mobile",
			userRoles:        []string{"admin", "user"},
			notificationType: "invoice_status_update",
			expectedAppType:  "mobile",
			expectError:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test service with mock user roles
			testService := &TestService{
				mockUserRoles: map[string][]string{
					"test-user": tt.userRoles,
				},
			}

			// Test the function
			result, err := testService.DetermineApplicationType(context.Background(), "test-user", tt.notificationType)

			// Verify results
			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedAppType, result)
			}
		})
	}
}

// TestDetermineApplicationType_ContextBasedRouting tests that notifications are routed based on context, not role precedence
func TestDetermineApplicationType_ContextBasedRouting(t *testing.T) {
	testService := &TestService{
		mockUserRoles: map[string][]string{
			"dual-role-user": {"user", "partner"},
		},
	}

	// Test partner-context notifications (should go to partner-web)
	partnerContextNotifications := []string{
		"new_order_received",
		"partner_notification",
		"company_update",
	}

	for _, notificationType := range partnerContextNotifications {
		t.Run("Partner context: "+notificationType, func(t *testing.T) {
			result, err := testService.DetermineApplicationType(context.Background(), "dual-role-user", notificationType)

			assert.NoError(t, err)
			assert.Equal(t, "partner-web", result, "Partner-context notification %s should go to partner-web", notificationType)
		})
	}

	// Test customer-context notifications (should go to mobile)
	customerContextNotifications := []string{
		"invoice_status_update",
		"invoice_status",
		"order_update",
	}

	for _, notificationType := range customerContextNotifications {
		t.Run("Customer context: "+notificationType, func(t *testing.T) {
			result, err := testService.DetermineApplicationType(context.Background(), "dual-role-user", notificationType)

			assert.NoError(t, err)
			assert.Equal(t, "mobile", result, "Customer-context notification %s should go to mobile", notificationType)
		})
	}
}
