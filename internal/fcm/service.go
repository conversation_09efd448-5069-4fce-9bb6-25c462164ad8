package fcm

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/izy-mercado/backend/internal/logger"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"google.golang.org/api/option"
)

// Service handles Firebase Cloud Messaging operations
type Service struct {
	client  *messaging.Client
	queries *postgres.Queries
	logger  logger.Logger
}

// Config holds FCM service configuration
type Config struct {
	ServiceAccountPath string `json:"service_account_path"` // For local development
	ServiceAccountJSON string `json:"service_account_json"` // For production (JSON content)
	ProjectID          string `json:"project_id"`
}

// NotificationPayload represents the data sent in FCM notifications
// SECURITY: Only external IDs and order IDs are included - never internal database IDs
type NotificationPayload struct {
	Type              string    `json:"type"`
	OrderID           string    `json:"order_id"`
	NewStatus         string    `json:"new_status"`
	OldStatus         string    `json:"old_status"`
	StatusDescription string    `json:"status_description"`
	Message           string    `json:"message"`
	UpdatedBy         string    `json:"updated_by"`
	UpdaterName       string    `json:"updater_name"`
	UserExternalID    string    `json:"user_external_id"`    // External ID only - never internal user_id
	CompanyExternalID string    `json:"company_external_id"` // External ID only - never internal company_id
	CompanyName       string    `json:"company_name"`
	ApplicationType   string    `json:"application_type"` // Target application type: mobile or partner-web
	Timestamp         time.Time `json:"timestamp"`
}

// DeliveryResult represents the result of an FCM notification delivery
type DeliveryResult struct {
	Success      bool   `json:"success"`
	MessageID    string `json:"message_id,omitempty"`
	Error        string `json:"error,omitempty"`
	TokenInvalid bool   `json:"token_invalid"`
	ShouldRetry  bool   `json:"should_retry"`
}

// NewService creates a new FCM service instance
func NewService(config Config, queries *postgres.Queries) (*Service, error) {
	serviceLogger := logger.GetLogger()

	// Initialize Firebase app with credentials
	var opt option.ClientOption
	if config.ServiceAccountJSON != "" {
		// Production: Use JSON content from Secret Manager
		opt = option.WithCredentialsJSON([]byte(config.ServiceAccountJSON))
		serviceLogger.WithFields(map[string]interface{}{
			"component": "fcm_service",
			"action":    "using_json_credentials",
		}).Info("Using JSON credentials for FCM initialization")
	} else if config.ServiceAccountPath != "" {
		// Development: Use file path
		opt = option.WithCredentialsFile(config.ServiceAccountPath)
		serviceLogger.WithFields(map[string]interface{}{
			"component": "fcm_service",
			"action":    "using_file_credentials",
			"path":      config.ServiceAccountPath,
		}).Info("Using file credentials for FCM initialization")
	} else {
		return nil, fmt.Errorf("either ServiceAccountJSON or ServiceAccountPath must be provided")
	}

	app, err := firebase.NewApp(context.Background(), &firebase.Config{
		ProjectID: config.ProjectID,
	}, opt)
	if err != nil {
		serviceLogger.WithFields(map[string]interface{}{
			"component": "fcm_service",
			"action":    "initialization_failed",
			"error":     err.Error(),
		}).Error("Failed to initialize Firebase app")
		return nil, fmt.Errorf("failed to initialize Firebase app: %w", err)
	}

	// Get messaging client
	client, err := app.Messaging(context.Background())
	if err != nil {
		serviceLogger.WithFields(map[string]interface{}{
			"component": "fcm_service",
			"action":    "client_creation_failed",
			"error":     err.Error(),
		}).Error("Failed to create FCM client")
		return nil, fmt.Errorf("failed to create FCM client: %w", err)
	}

	service := &Service{
		client:  client,
		queries: queries,
		logger:  serviceLogger,
	}

	serviceLogger.WithFields(map[string]interface{}{
		"component":  "fcm_service",
		"action":     "initialized",
		"project_id": config.ProjectID,
		"timestamp":  time.Now(),
	}).Info("FCM service initialized successfully")

	return service, nil
}

// DetermineApplicationType determines the appropriate application type for notifications
// based on notification CONTEXT and user roles. This implements proper RBAC integration:
// - Customer-context notifications (order updates for customers) → mobile app
// - Partner-context notifications (business alerts for owners) → partner-web app
func (s *Service) DetermineApplicationType(ctx context.Context, userExternalID, notificationType string) (string, error) {
	// Get user roles
	roles, err := s.queries.GetUserRoles(ctx, userExternalID)
	if err != nil {
		return "", fmt.Errorf("failed to get user roles: %w", err)
	}

	// Check user roles
	hasPartnerRole := false
	hasUserRole := false
	for _, role := range roles {
		if role == "partner" {
			hasPartnerRole = true
		}
		if role == "user" {
			hasUserRole = true
		}
	}

	// Route notifications based on CONTEXT, not just role precedence
	switch notificationType {
	// PARTNER-CONTEXT notifications: Business management, new orders for business owners
	case "new_order_received", "partner_notification", "company_update":
		// These are business-context notifications that should ONLY go to partner dashboard
		if hasPartnerRole {
			return "partner-web", nil
		}
		// If user doesn't have partner role, they shouldn't receive these notifications
		return "", fmt.Errorf("partner role required for business notifications")

	// CUSTOMER-CONTEXT notifications: Order status updates for customers who placed orders
	case "invoice_status_update", "invoice_status", "order_update":
		// These are customer-context notifications about orders the user placed
		// They should go to mobile app regardless of whether user also has partner role
		if hasUserRole {
			return "mobile", nil
		}
		// If user doesn't have user role, they shouldn't receive customer notifications
		return "", fmt.Errorf("user role required for customer notifications")
	}

	// Default fallback for unknown notification types
	if hasUserRole {
		return "mobile", nil
	}
	if hasPartnerRole {
		return "partner-web", nil
	}

	return "", fmt.Errorf("user has no valid roles for notifications")
}

// SendNotificationToToken sends a notification directly to a specific FCM token
func (s *Service) SendNotificationToToken(ctx context.Context, fcmToken, title, body string, data map[string]interface{}) (*DeliveryResult, error) {
	startTime := time.Now()

	// Convert data map to string map for FCM
	stringData := make(map[string]string)
	for k, v := range data {
		if str, ok := v.(string); ok {
			stringData[k] = str
		} else {
			stringData[k] = fmt.Sprintf("%v", v)
		}
	}

	// Create FCM message
	fcmMessage := &messaging.Message{
		Token: fcmToken,
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Data: stringData,
		Android: &messaging.AndroidConfig{
			Priority: "high",
			Notification: &messaging.AndroidNotification{
				ChannelID: "secure_notifications",
				Priority:  messaging.PriorityHigh,
				Sound:     "default",
			},
		},
		APNS: &messaging.APNSConfig{
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert: &messaging.ApsAlert{
						Title: title,
						Body:  body,
					},
					Sound: "default",
				},
			},
		},
	}

	response, err := s.client.Send(ctx, fcmMessage)
	if err != nil {
		s.logger.WithFields(map[string]interface{}{
			"component":   "fcm_service",
			"action":      "notification_failed",
			"token":       fcmToken[:8] + "...",
			"error":       err.Error(),
			"duration_ms": time.Since(startTime).Milliseconds(),
		}).Error("Failed to send FCM notification to token")

		return &DeliveryResult{
			Success: false,
			Error:   err.Error(),
		}, err
	}

	s.logger.WithFields(map[string]interface{}{
		"component":      "fcm_service",
		"action":         "notification_sent",
		"token":          fcmToken[:8] + "...",
		"fcm_message_id": response,
		"duration_ms":    time.Since(startTime).Milliseconds(),
	}).Info("FCM notification sent to token successfully")

	return &DeliveryResult{
		Success:   true,
		MessageID: response,
	}, nil
}

// SendInvoiceStatusNotification sends an invoice status update notification via FCM
func (s *Service) SendInvoiceStatusNotification(ctx context.Context, payload NotificationPayload) (*DeliveryResult, error) {
	startTime := time.Now()

	// Get user to verify they exist
	user, err := s.queries.GetUserByExternalID(ctx, payload.UserExternalID)
	if err != nil {
		s.logger.WithFields(map[string]interface{}{
			"component":        "fcm_service",
			"action":           "notification_failed",
			"user_external_id": payload.UserExternalID,
			"order_id":         payload.OrderID,
			"error":            err.Error(),
			"reason":           "user_not_found",
		}).Error("Failed to get user for FCM notification")
		return &DeliveryResult{
			Success:     false,
			Error:       "user not found",
			ShouldRetry: false,
		}, err
	}

	// Determine application type if not specified
	applicationType := payload.ApplicationType
	if applicationType == "" {
		// Auto-determine based on notification type and user roles
		applicationType, err = s.DetermineApplicationType(ctx, payload.UserExternalID, payload.Type)
		if err != nil {
			s.logger.WithFields(map[string]interface{}{
				"component":        "fcm_service",
				"action":           "notification_failed",
				"user_external_id": payload.UserExternalID,
				"order_id":         payload.OrderID,
				"error":            err.Error(),
				"reason":           "application_type_determination_failed",
			}).Error("Failed to determine application type for FCM notification")
			return &DeliveryResult{
				Success:     false,
				Error:       fmt.Sprintf("failed to determine application type: %v", err),
				ShouldRetry: false,
			}, err
		}
	}

	// Get user's active push tokens for the determined application type
	pushTokens, err := s.queries.GetActiveFCMTokensByUserAndAppType(ctx, postgres.GetActiveFCMTokensByUserAndAppTypeParams{
		UserID:          user.ID,
		ApplicationType: applicationType,
	})

	if err != nil {
		s.logger.WithFields(map[string]interface{}{
			"component":        "fcm_service",
			"action":           "notification_failed",
			"user_external_id": payload.UserExternalID,
			"order_id":         payload.OrderID,
			"application_type": applicationType,
			"error":            err.Error(),
		}).Error("Failed to get push tokens for FCM notification")
		return &DeliveryResult{
			Success:     false,
			Error:       fmt.Sprintf("failed to get push tokens: %v", err),
			ShouldRetry: true,
		}, err
	}

	// Check if user has any active push tokens
	if len(pushTokens) == 0 {
		s.logger.WithFields(map[string]interface{}{
			"component":        "fcm_service",
			"action":           "notification_skipped",
			"user_external_id": payload.UserExternalID,
			"order_id":         payload.OrderID,
			"application_type": applicationType,
			"reason":           "no_push_tokens",
		}).Debug("No active push tokens available for user")
		return &DeliveryResult{
			Success:     false,
			Error:       "no active push tokens for application type",
			ShouldRetry: false,
		}, nil
	}

	// Send notification to all active devices
	var results []*DeliveryResult
	var lastError error

	for _, token := range pushTokens {
		// Create FCM message for this device
		message := s.buildFCMMessage(token.FcmToken, payload)

		// Send the message
		response, err := s.client.Send(ctx, message)
		if err != nil {
			// Check if token is invalid
			tokenInvalid := s.isTokenInvalidError(err)
			if tokenInvalid {
				// Mark token as invalid (we'll handle cleanup later)
				s.logger.WithFields(map[string]interface{}{
					"component":        "fcm_service",
					"action":           "invalid_token_detected",
					"user_external_id": payload.UserExternalID,
					"device_id":        token.DeviceID,
					"platform":         token.Platform,
				}).Warn("Invalid FCM token detected")
			}

			s.logger.WithFields(map[string]interface{}{
				"component":        "fcm_service",
				"action":           "notification_failed",
				"user_external_id": payload.UserExternalID,
				"order_id":         payload.OrderID,
				"device_id":        token.DeviceID,
				"platform":         token.Platform,
				"error":            err.Error(),
				"token_invalid":    tokenInvalid,
			}).Warn("Failed to send FCM notification to device")

			result := &DeliveryResult{
				Success:      false,
				Error:        err.Error(),
				TokenInvalid: tokenInvalid,
				ShouldRetry:  !tokenInvalid,
			}
			results = append(results, result)
			lastError = err
			continue
		}

		// Log successful delivery
		s.logger.WithFields(map[string]interface{}{
			"component":        "fcm_service",
			"action":           "notification_sent",
			"user_external_id": payload.UserExternalID,
			"order_id":         payload.OrderID,
			"device_id":        token.DeviceID,
			"platform":         token.Platform,
			"message_id":       response,
			"duration_ms":      time.Since(startTime).Milliseconds(),
		}).Info("FCM notification sent successfully")

		result := &DeliveryResult{
			Success:   true,
			MessageID: response,
		}
		results = append(results, result)
	}

	// Return overall result
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	if successCount > 0 {
		// At least one notification was sent successfully
		return &DeliveryResult{
			Success:   true,
			MessageID: fmt.Sprintf("sent to %d/%d devices", successCount, len(pushTokens)),
		}, nil
	} else {
		// All notifications failed
		return &DeliveryResult{
			Success:     false,
			Error:       fmt.Sprintf("failed to send to all %d devices", len(pushTokens)),
			ShouldRetry: lastError != nil,
		}, lastError
	}

}

// buildFCMMessage creates an FCM message from the notification payload
func (s *Service) buildFCMMessage(token string, payload NotificationPayload) *messaging.Message {
	// Convert payload to data map
	dataMap := make(map[string]string)
	payloadBytes, _ := json.Marshal(payload)
	var payloadMap map[string]interface{}
	json.Unmarshal(payloadBytes, &payloadMap)

	for key, value := range payloadMap {
		dataMap[key] = fmt.Sprintf("%v", value)
	}

	return &messaging.Message{
		Token: token,
		Notification: &messaging.Notification{
			Title: s.getNotificationTitle(payload.NewStatus),
			Body:  payload.Message,
		},
		Data: dataMap,
		Android: &messaging.AndroidConfig{
			Priority: "high",
			Notification: &messaging.AndroidNotification{
				ChannelID:   "invoice_updates",
				Priority:    messaging.PriorityHigh,
				Sound:       "default",
				ClickAction: "IZY_NOTIFICATION_CLICK",
				Tag:         payload.OrderID, // Group notifications by order
			},
		},
		APNS: &messaging.APNSConfig{
			Headers: map[string]string{
				"apns-priority": "10",
			},
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert: &messaging.ApsAlert{
						Title: s.getNotificationTitle(payload.NewStatus),
						Body:  payload.Message,
					},
					Sound:    "default",
					Category: "INVOICE_UPDATE",
				},
			},
		},
	}
}

// getNotificationTitle returns appropriate title based on invoice status
func (s *Service) getNotificationTitle(status string) string {
	titles := map[string]string{
		"pending":    "Pedido Recebido",
		"processing": "Pagamento Confirmado",
		"preparing":  "Preparando Pedido",
		"ready":      "Pedido Pronto",
		"delivering": "Saiu para Entrega",
		"completed":  "Pedido Entregue",
		"cancelled":  "Pedido Cancelado",
		"expired":    "Pagamento Expirado",
		"failed":     "Falha no Pagamento",
	}

	if title, exists := titles[status]; exists {
		return title
	}
	return "Atualização do Pedido"
}

// isTokenInvalidError checks if the error indicates an invalid FCM token
func (s *Service) isTokenInvalidError(err error) bool {
	if err == nil {
		return false
	}

	errorStr := err.Error()
	invalidTokenErrors := []string{
		"registration-token-not-registered",
		"invalid-registration-token",
		"invalid-argument",
	}

	for _, invalidError := range invalidTokenErrors {
		if strings.Contains(errorStr, invalidError) {
			return true
		}
	}
	return false
}
