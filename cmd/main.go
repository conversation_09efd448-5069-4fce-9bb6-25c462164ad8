package main

import (
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v4/pgxpool"

	"github.com/izy-mercado/backend/internal/config"
	server "github.com/izy-mercado/backend/internal/http"
	"github.com/izy-mercado/backend/internal/integrations/storage"
)

var version = "dev" // Default if not set via -ldflags //

func main() {
	fmt.Println("App Version:", version)
	// load environment variables
	env := config.Must()
	ctx := context.Background()

	// init postgres connection
	pool := setupPostgres(env.Core.DATABASE_URL, ctx)

	storage, err := storage.New(env.Storage)
	if err != nil {
		log.Fatal("failed to initialize storage:", err)
	}

	// start http server
	server.New(env, pool, storage).Start()
}

// connect to postgres pool
func setupPostgres(databaseURL string, ctx context.Context) *pgxpool.Pool {
	conf, err := pgxpool.ParseConfig(databaseURL)
	if err != nil {
		log.Fatalf("Unable to parse database url: %v", err)
	}

	conf.MaxConns = 20
	conf.MinConns = 5

	pool, err := pgxpool.ConnectConfig(ctx, conf)
	if err != nil {
		log.Fatalf("Unable to connect to database: %v", err)
	}

	return pool
}
