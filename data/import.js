const { Pool } = require("pg");
const fs = require("fs");

// PostgreSQL Connection
const pool = new Pool({
  user: "postgres",
  host: "localhost",
  database: "izy_mercado",
  password: "postgres",
  port: 5432,
});

// Load JSON file
const jsonData = JSON.parse(fs.readFileSync("output.json", "utf8"));

async function insertProducts() {
  const client = await pool.connect();
  try {
    await client.query("BEGIN");

    const insertQuery = `
      INSERT INTO products (name, ean, description, image, category, brand, external_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (ean) DO NOTHING;
    `;

    for (const product of jsonData) {
      const values = [
        product.name,
        product.ean,
        product.description,
        product.image,
        product.category,
        product.brand,
        product.external_id,
      ];

      await client.query(insertQuery, values);
    }

    await client.query("COMMIT");
    console.log("✅ Data inserted successfully!");
  } catch (error) {
    await client.query("ROLLBACK");
    console.error("❌ Error inserting data:", error);
  } finally {
    client.release();
  }
}

insertProducts()
  .then(() => pool.end())
  .catch((err) => console.error(err));
