import { createReadStream, createWriteStream } from 'fs';
import { ulid } from 'ulid'


function generateImageUrl(code) {
  code = code.padStart(13, '0');
  const formattedCode = `${code.slice(0, 3)}/${code.slice(3, 6)}/${code.slice(6, 9)}/${code.slice(9)}`;
  return `https://images.openfoodfacts.org/images/products/${formattedCode}/1.400.jpg`;
}

async function processFile() {
  const readStream = createReadStream('data.json', { encoding: 'utf8' });
  const writeStream = createWriteStream('output.json');
  
  let jsonString = '';
  
  readStream.on('data', (chunk) => {
    jsonString += chunk;
  });

  readStream.on('end', async () => {
    try {
      const jsonArray = JSON.parse(jsonString);
      const transformedArray = jsonArray.map((data) => {
        let hasImage = false
        if (!data.code || !data.product_name) return null; // Avoid null entries
        if (Object.hasOwnProperty.call(data, 'images')) {
          hasImage = true;
        }
        
        return {
          ean: data.code,
          name: data.product_name,
          description: data.ingredients_text || data.ingredients_text_fr || data.ingredients_text_with_allergens || data.ingredients_text_with_allergens_fr || "",
          image: hasImage ? generateImageUrl(data.code) : "",
          brand: data.brands || "",
          category: data.categories || "",
          external_id: ulid()
        };
      }).filter(Boolean); // Remove null objects from the array
      console.log(`Total of products processed: ${transformedArray.length}`);
      writeStream.write(JSON.stringify(transformedArray, null, 2));
      writeStream.end();
      console.log('Processing complete.');
    } catch (err) {
      console.error('Error parsing JSON file:', err.message);
    }
  });

  readStream.on('error', (err) => {
    console.error('Error reading file:', err.message);
  });
}

processFile().catch(err => console.error('Error processing file:', err));
