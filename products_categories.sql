--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4 (Ubuntu 17.4-1.pgdg24.04+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: products_categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.products_categories (product_id, category_id) VALUES (2734, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2719, 3);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2722, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2737, 3);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2733, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2736, 7);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2735, 5);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2732, 2);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2731, 5);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2730, 9);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2729, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2728, 9);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2727, 4);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2726, 4);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2720, 2);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2721, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2738, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2739, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2740, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2741, 5);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2742, 4);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2743, 5);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2745, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2746, 11);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2747, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2748, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2749, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2751, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2752, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2753, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2754, 11);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2755, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2756, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2758, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2759, 13);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2760, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2761, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2762, 2);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2757, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2744, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2764, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2765, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2723, 2);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2767, 7);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2766, 7);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2769, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2771, 5);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2772, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2776, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (24, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2777, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2778, 4);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2779, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2782, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2784, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2783, 13);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2781, 2);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2780, 13);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2775, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (932, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2774, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2773, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2770, 7);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2768, 3);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2785, 4);
INSERT INTO public.products_categories (product_id, category_id) VALUES (317, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2750, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2786, 14);
INSERT INTO public.products_categories (product_id, category_id) VALUES (58, 6);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2787, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2788, 13);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2789, 5);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2790, 14);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2792, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2793, 2);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2794, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2795, 2);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2791, 2);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2796, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (159, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2797, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2798, 8);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2799, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2800, 3);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2801, 4);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2802, 2);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2803, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2804, 10);
INSERT INTO public.products_categories (product_id, category_id) VALUES (2805, 4);


--
-- PostgreSQL database dump complete
--

