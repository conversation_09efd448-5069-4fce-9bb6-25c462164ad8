version: 1
packages:
  - path: "pkg/storage/postgres"
    name: "postgres"
    sql_package: "pgx/v4"
    engine: "postgresql"
    schema: "pkg/storage/postgres/migrations/"
    queries: "pkg/storage/postgres/queries/"
    overrides:
    - column: "user_addresses.address"
      go_type:
        import: "github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
        package: "custom_models"
        type: "AddressParams"
    - column: "user_addresses.location"
      go_type:
          import: "github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
          package: "custom_models"
          type: "Location"  
    - column: "users_products_lists.products"
      go_type:
        import: "github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
        package: "custom_models"
        type: "Products"
    - column: "templates_products_lists.products"
      go_type:
        import: "github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
        package: "custom_models"
        type: "Products"
    - column: "*.Location"
      go_type:
        import: "github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
        package: "custom_models"
        type: "Location"